---------------------------------------
   ---@author: WQ
   ---@time: 01/06/2025 16:18
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------

 include('shared.lua')


function ENT:CInteract()
    local pOwner = LocalPlayer()
    if not IsValid(pOwner) then return end
    if IsValid(self.vFrame) then self.vFrame:Remove() end

    if not pOwner.WQ_Inventory_tInventory then
        net.Start("LVM:WQ_Inventory:SyncInventory")
        net.SendToServer()
        return
    end

    LVM_V2.PoubelleCosmetic:Open()
end

local floor = Material("narutorp/effects/circle")
function ENT:Draw()
    self:SetNWBool("CUse", true)
    self:SetNWBool("CName", "Marchand fou")

    
    self:DrawModel()
    self:ResetSequence("d1_t02_playground_cit1_arms_crossed")
    
    if LocalPlayer():GetPos():Distance(self:GetPos()) < 300 then
        local alpha = (LocalPlayer():GetPos():Distance(self:GetPos()) / 400.0)
        alpha = math.Clamp(1.25 - alpha, 0 ,0.9) 
        local a = Angle(0,0,0) 
        a:RotateAroundAxis(Vector(1,0,0),90) 
        a.y = LocalPlayer():GetAngles().y - 90 

        local margin = 12
        
        local width = 300
        
        cam.Start3D2D(self:GetPos() + Vector(0,0, 85 + (math.abs(math.sin(CurTime() * 2)) * 2)) , a , 0.08) 
            draw.RoundedBox(20, -width/1.9, -75, width, 75 , Color(255,20,20, 150)) 

            draw.RoundedBox(14, -width/1.9 + (margin/2), -75 + (margin / 2), width - margin, 75 - margin , Color(25,25,25, 255 * alpha)) 

            draw.SimpleText("Marchand fou", LVM_V2.Fonts("LVM", 14, 500), -2, -40, white , 1 , 1) 
        cam.End3D2D() 
    end

    local range = (RealTime() / 2) % 1
    render.SetMaterial(floor)
    render.DrawQuadEasy(self:GetPos() + vector_origin, vector_up, range * 148, range * 148, Color(255, 255, 255, 255 * (1 - range)), 0)
end
