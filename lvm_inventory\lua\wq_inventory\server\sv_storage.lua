------------------------------------ UTIL ------------------------------------


local tNets = {
    "LVM:WQ_Inventory:OpenInventory",
    "LVM:WQ_Inventory:SyncInventory",
    "LVM:WQ_Inventory:AddItem",
    "LVM:WQ_Inventory:EquipItem",
    "LVM:WQ_Inventory:EquipKenjutsu",
    "LVM:WQ_Inventory:UnequipItem",
    "LVM:WQ_Inventory:OpenAdminPanel",
    "LVM:WQ_Inventory:SetModel",
    "LVM:WQ_Inventory:AddItemAdmin",
    "LVM:WQ_Inventory:RemoveItem",
    "LVM:WQ_Inventory:GiveItem",
    "LVM:WQ_Inventory:EquipMask",
    "LVM:WQ_Inventory:EquipAccesory",
    "LVM:WQ_Inventory:OpenInventory:Admin",
    "LVM:WQ_Inventory:UnequipItem:Admin",
    "LVM:WQ_Inventory:EquipItem:Admin",
    "LVM:WQ_Inventory:RemoveItem:Admin",
    "LVM:WQ_Inventory:GetCharacter",
    "LVM:WQ_Inventory:DropItem",
    "LVM:WQ_Missive:OpenParchemin",
    "LVM_WQ_Missive:Create",
    "WQ_Inventory:SaveWeaponModification",
}

for _, sNets in pairs(tNets) do
    util.AddNetworkString(sNets)
end
 
local bHoldHair = {}


------------------------------------ HYTTP ------------------------------------

require("reqwest")


local function ColorObjectToEmbedColor(color)
    return bit.bor(
        bit.lshift(color.r, 16),
        bit.lshift(color.g, 8),
        color.b
    )
end

local function SendDiscordWebhook(body)
    reqwest({
        method = "POST",
        url =
        "https://discord.com/api/webhooks/1388278388971802796/YBbi1cO3U00KcRpv3SIkaeeoZHkxqD2hpswCMsxJbF8lYEPNOdlH0PNYNGATYfOknvVr",
        timeout = 30,
        body = util.TableToJSON(body),
        type = "application/json",
        headers = {
            ["User-Agent"] = "MyUserAgent/1.0",
        },
        success = function(status, body, headers)
            print("Success: " .. status)
        end,
        failed = function(err, errExt)
            print("Error: " .. err .. " (" .. errExt .. ")")
        end
    })
end

local function WebhookInventoryGive(pOwner, pTarget, sItem, iAmount)
    if not IsValid(pOwner) or not IsValid(pTarget) then return end

    local body = {
        embeds = {
            {
                title = "[LVM GIVE ADMIN]",
                description = "Un item a été donné",
                color = ColorObjectToEmbedColor(Color(15, 137, 207)),
                fields = {
                    {
                        name = "Nom rp de l'admin",
                        value = pOwner:Nick(),
                        inline = true
                    },
                    {
                        name = "SteamID de l'admin",
                        value = pOwner:SteamID64(),
                        inline = true
                    },
                    {
                        name = "Joueurs",
                        value = pTarget:Nick(),
                        inline = true
                    },
                    {
                        name = "SteamID du joueur",
                        value = pTarget:SteamID64(),
                        inline = true
                    },
                    {
                        name = "Class de l'item",
                        value = sItem,
                        inline = true
                    },
                    {
                        name = "Quantité",
                        value = tostring(iAmount),
                        inline = true
                    }
                }
            }
        }
    }

    SendDiscordWebhook(body)
end

local function WebhookInventoryGivePlayerToPlayer(pOwner, pTarget, sItem, iAmount)
    if not IsValid(pOwner) or not IsValid(pTarget) then return end

    local body = {
        embeds = {
            {
                title = "[LVM INVENTORY]",
                description = "Un item a été donné",
                color = ColorObjectToEmbedColor(Color(0, 255, 0)),
                fields = {
                    {
                        name = "Nom rp du joueurs donneur",
                        value = pOwner:Nick(),
                        inline = true
                    },
                    {
                        name = "SteamID du joueurs donneur",
                        value = pOwner:SteamID64(),
                        inline = true
                    },
                    {
                        name = "Nom rp du joueurs receveur",
                        value = pTarget:Nick(),
                        inline = true
                    },
                    {
                        name = "SteamID du joueurs receveur",
                        value = pTarget:SteamID64(),
                        inline = true
                    },
                    {
                        name = "Class de l'item",
                        value = sItem,
                        inline = true
                    },
                    {
                        name = "Quantité",
                        value = tostring(iAmount),
                        inline = true
                    }
                }
            }
        }
    }

    SendDiscordWebhook(body)
end




local function SendInventoryWebhook(title, description, color, fieldsData)
    local fields = {}

    for _, field in ipairs(fieldsData) do
        table.insert(fields, {
            name = field.name,
            value = field.value,
            inline = field.inline or false
        })
    end

    local body = {
        embeds = {
            {
                title = title,
                description = description,
                color = color or 16711904, -- couleur par défaut (rouge)
                fields = fields
            }
        }
    }

    SendDiscordWebhook(body)
end



------------------------------------ NETS ------------------------------------


net.Receive("LVM:WQ_Inventory:AddItemAdmin", function(_, pOwner)
    local sItem = net.ReadString()
    local iAmount = net.ReadInt(16)
    local pTarget = net.ReadPlayer()


    if not pTarget:IsPlayer() then return end
    if not pOwner:IsSuperAdmin() then return end

    local iCharId = pTarget:LVM_GetCurrentCharacter().id
    if not iCharId then return end


    if not WQ_Inventory.tItemRegistered[sItem] then return end
    if iAmount <= 0 or iAmount > 100 then return end

    WQ_Inventory:AddInventory(pTarget, sItem, iAmount, iCharId)
    local sItemName = WQ_Inventory.tItemRegistered[sItem].sName or sItem
    DarkRP.notify(pTarget, 0, 5, "[LVM GIVE ADMIN] Vous avez recu l'item: " .. sItemName .." par ".. pOwner:Nick())
    DarkRP.notify(pOwner, 0, 5, "[LVM GIVE ADMIN] Vous avez donné l'item: " .. sItemName .." à ".. pTarget:Nick())


    WebhookInventoryGive(pOwner, pTarget, sItem, iAmount)
end)

net.Receive("LVM:WQ_Inventory:GiveItem", function(_, pOwner)
    local iCharId = pOwner:LVM_GetCurrentCharacter().id
    if not iCharId then return end

    if not pOwner:IsSuperAdmin() then 
        if pOwner:AdminMode() then 
            DarkRP.notify(pOwner, 0, 5, "Vous ne pouvez pas donner d'item en mode admin !")
            return 
        end
    end

    local sItem = net.ReadString()
    local pTarget = net.ReadEntity()

    local iTargetCharId = pTarget:LVM_GetCurrentCharacter().id
    if not iTargetCharId then return end

    if not IsValid(pTarget) then return end
    if not WQ_Inventory.tItemRegistered[sItem] then return end
    local hasItem, amout, isBoutique = WQ_Inventory:HasItem(pOwner, sItem)
    if not hasItem then return end
    if isBoutique then
        DarkRP.notify(pOwner, 0, 5, "Cette item a été acheté sur la boutique !")
        return
    end

    WQ_Inventory:AddInventory(pTarget, sItem, 1, iTargetCharId)
    WQ_Inventory:RemoveInventory(pOwner, sItem, 1, iCharId)

    local sItemName = WQ_Inventory.tItemRegistered[sItem].sName or sItem

    DarkRP.notify(pTarget, 0, 5, "Vous avez reçu " .. sItemName .." de ".. pTarget:Nick())
    DarkRP.notify(pOwner, 0, 5, "Vous avez donné " .. sItemName .." à ".. pTarget:Nick())


    WebhookInventoryGivePlayerToPlayer(pOwner, pTarget, sItem, 1)
end)
local duplil = {
    "accesoire1",
    "accesoire2",
    "accesoire3",
}


net.Receive("LVM:WQ_Inventory:EquipItem", function(_, pOwner)
    local iCharId = pOwner:LVM_GetCurrentCharacter().id
    if not iCharId then return end

    local sItem = net.ReadString()
    local sCategory = net.ReadString()


    if not WQ_Inventory.tItemRegistered[sItem] then return end
    if not WQ_Inventory:IfHaveItemInInventory(pOwner, iCharId, sItem) then return end

    if sCategory:match("^accesoire") then
    local desired = tonumber(sCategory:match("accesoire(%d)")) or 1
    local slotFound = false
        for slot = desired, 3 do
            local cat = "accesoire" .. slot
            local occupied = false
            for _, eq in ipairs(WQ_Inventory:GetEquiped(pOwner, iCharId)) do
                if eq.category == cat then
                    occupied = true
                    break
                end
            end
            if not occupied then
                sCategory = cat
                slotFound = true
                break
            end
        end
        if not slotFound then
            DarkRP.notify(pOwner, 1, 5, "Vous avez déjà tous les emplacements d'accessoires occupés !")
            return
        end
    end


    local sGrade = WQ_Inventory.tItemRegistered[sItem].sGrade
    local sVillage = WQ_Inventory.tItemRegistered[sItem].sVillage

    if sVillage then 
        local village = string.lower(DLVM:GetVillage(pOwner:Team()))
        if sVillage != village then 
            DarkRP.notify(pOwner, 1, 5, "Tu n'es pas de ce village pour équiper cet item")
            return
        end
    end

    if sGrade then
        if not pOwner:IfMinimumRank(sGrade) then 
            DarkRP.notify(pOwner, 1, 5, "Tu n'as pas le grade requis pour équiper cet item")
            return
        end
    end


    local sItemName = WQ_Inventory.tItemRegistered[sItem].sName or sItem
    

    
    if sCategory == "food" then


        local iAdd = math.Clamp((pOwner:getDarkRPVar("Energy") or 100) + (WQ_Inventory.tItemRegistered[sItem].iAddHungry or 10), 0, 100)
        pOwner:setSelfDarkRPVar("Energy", iAdd)

        WQ_Inventory:RemoveInventory(pOwner, sItem, 1, iCharId)
        return
    elseif sCategory == "util" then
        if sItem == "parchemin_vide" then

            net.Start("LVM:Missive:OpenParchemin")
            net.Send(pOwner)
            return
        else
            DarkRP.notify(pOwner, 0, 5, "Vous ne pouvez pas équiper cet item")
            return
        end
    elseif sCategory:match("accesoire") == "accesoire" then
        if WQ_Inventory:IfHaveItemInEquiped(pOwner, iCharId, sItem) then
            DarkRP.notify(pOwner, 0, 5, "Vous avez déjà cet accessoire équipé")
            return
        end
    end


    if pOwner:GetNWBool("NARUTON_FIGHTSYSTEM_PlayerIsInFight", false) then
        DarkRP.notify(pOwner, 0, 5, "Vous ne pouvez pas équiper d'item en combat")
        return
    end

    if pOwner:GetNWBool("LVM:WQ:PAIN") then
        DarkRP.notify(pOwner, 0, 5, "Vous ne pouvez pas équiper d'item en étant blessé")
        return
    end

    if not WQ_Inventory:EquipiItem(pOwner, sItem, sCategory) then
        DarkRP.notify(pOwner, 0, 5, "Vous ne pouvez pas équiper cette item !")
        return
    end


    local __, sIem, boutique = WQ_Inventory:HasItem(pOwner, sItem)

    WQ_Inventory:AddEquip(pOwner, sItem, iCharId, sCategory, boutique)
    WQ_Inventory:RemoveInventory(pOwner, sItem, 1, iCharId)


    DLVM:SendLogs("Inventory Equip", Color(255, 0, 0), pOwner, " a équipé l'item : ", Color(255, 255, 0), sItemName, Color(255, 255, 255), " le")

    DarkRP.notify(pOwner, 0, 5, "Vous avez équiper : " .. sItemName)
end)



net.Receive("LVM:WQ_Inventory:UnequipItem", function(_, pOwner)
    local iCharId = pOwner:LVM_GetCurrentCharacter().id
    if not iCharId then return end

    local sItem = net.ReadString()
    local sCategory = net.ReadString()

    if not WQ_Inventory.tItemRegistered[sItem] then return end
    if not WQ_Inventory:IfHaveItemInEquiped(pOwner, iCharId, sItem) then return end

    if pOwner:GetNWBool("NARUTON_FIGHTSYSTEM_PlayerIsInFight", false) then
        DarkRP.notify(pOwner, 0, 5, "Vous ne pouvez pas dé-équiper d'item en combat")
        return
    end

    if pOwner:GetNWBool("LVM:WQ:PAIN") then
        DarkRP.notify(pOwner, 0, 5, "Vous ne pouvez pas dé-équiper d'item en étant blessé")
        return
    end

    local sItemName = WQ_Inventory.tItemRegistered[sItem].sName or sItem


    if not WQ_Inventory:UnequipItem(pOwner, sItem, sCategory) then
        DarkRP.notify(pOwner, 0, 5, "Vous ne pouvez pas dé-équiper cette item !")
        return
    end

    local __, sIem, boutique = WQ_Inventory:HasItem(pOwner, sItem)

    WQ_Inventory:RemoveEquip(pOwner, sItem, iCharId, sCategory)
    
    if boutique == true then
        WQ_Inventory:AddInventoryBoutique(pOwner, sItem, iCharId)
    else
        WQ_Inventory:AddInventory(pOwner, sItem, 1, iCharId)
    end


     DLVM:SendLogs("Inventory Equip", Color(255, 0, 0), pOwner, " a déquipé l'item : ", Color(255, 255, 0), sItemName, Color(255, 255, 255), " le")

    DarkRP.notify(pOwner, 0, 5, "Vous avez dé-équiper : " .. sItemName)
end)

net.Receive("LVM:WQ_Inventory:RemoveItem", function(_, pOwner)
    local iCharId = pOwner:LVM_GetCurrentCharacter().id
    if not iCharId then return end

    local sItem = net.ReadString()
    
    
    if not WQ_Inventory.tItemRegistered[sItem] then return end
    
    local hasItem, amout, isBoutique = WQ_Inventory:HasItem(pOwner, sItem)
    if not hasItem then return end
    -- if isBoutique then
    --     DarkRP.notify(pOwner, 0, 5, "Cette item a été acheté sur la boutique !")
    --     return
    -- end

    WQ_Inventory:RemoveInventory(pOwner, sItem, 1, iCharId)

    DarkRP.notify(pOwner, 0, 5, "Vous avez supprimé cette item !")

    SendInventoryWebhook(
        "[LVM SUPPRESSION ITEM]",
        "Un item a été supprimé",
        16753920, 
        {
            { name = "Nom RP du joueur", value = pOwner:Nick(), inline = true },
            { name = "SteamID", value = pOwner:SteamID64(), inline = true },
            { name = "Class de l'item", value = sItem, inline = true },
            { name = "Quantité", value = "1", inline = true }
        }
    )
end)

net.Receive("LVM:WQ_Inventory:DropItem", function(_, pOwner)
    local iCharId = pOwner:LVM_GetCurrentCharacter().id
    if not iCharId then return end

    if not pOwner:IsSuperAdmin() then 
        if pOwner:AdminMode() then 
            DarkRP.notify(pOwner, 0, 5, "Vous ne pouvez pas jeter d'item en mode admin !")
            return 
        end
    end

    local sItem = net.ReadString()
    local iAmount = net.ReadInt(16)

    if not WQ_Inventory.tItemRegistered[sItem] then return end
    if not WQ_Inventory:IfHaveItemInInventory(pOwner, iCharId, sItem, iAmount) then return end
    
    local has, amount, boutique = WQ_Inventory:HasItem(pOwner, sItem)
    
    local tItem = WQ_Inventory:GetItem(sItem)

    if tItem.sItemType == "eye" then
        DarkRP.notify(pOwner, 0, 5, "Vous ne pouvez pas jeter ce type d'object!")
        return
    end
    
    if boutique then
        DarkRP.notify(pOwner, 0, 5, "Vous ne pouvez pas jeter un item boutique !")
        return
    end

    if iAmount > amount then
        DarkRP.notify(pOwner, 0, 5, "Vous ne pouvez pas jeter plus que ce que vous avez !")
        return
    end

    WQ_Inventory:RemoveInventory(pOwner, sItem, iAmount, iCharId)

    local ent = ents.Create("LVM_itemdrop")
    ent:SetPos(pOwner:EyePos() + pOwner:GetAimVector() * 50)
    ent:SetItem(sItem)
    ent:SetItemCount(iAmount)
    ent:Spawn()

    DarkRP.notify(pOwner, 0, 5, "Vous avez jeté x".. iAmount .." ".. tItem.sName .." par terre !")

    SendInventoryWebhook(
        "[LVM DROP ITEM]",
        "Un item a ete drop",
        ColorObjectToEmbedColor(Color(255, 165, 0)), 
        {
            { name = "Nom RP du joueur", value = pOwner:Nick(), inline = true },
            { name = "SteamID", value = pOwner:SteamID64(), inline = true },
            { name = "Class de l'item", value = tItem.sName, inline = true },
            { name = "Quantité", value = iAmount, inline = true }
        }
    )
end)

net.Receive("LVM:WQ_Inventory:RemoveItem:Admin", function(_, pPlayer)
    if not pPlayer:IsSuperAdmin() then return end
    local pOwner = net.ReadEntity()

    local iCharId = pOwner:LVM_GetCurrentCharacter().id
    if not iCharId then return end

    local sItem = net.ReadString()

    if not WQ_Inventory.tItemRegistered[sItem] then return end
    if not WQ_Inventory:IfHaveItemInInventory(pOwner, iCharId, sItem) then return end

    WQ_Inventory:RemoveInventory(pOwner, sItem, 1, iCharId)

    local sItemName = WQ_Inventory.tItemRegistered[sItem].sName or sItem
    
    DarkRP.notify(pOwner, 0, 5, "[LVM ADMIN INVENTORY] Un admin a supprimé l'item ".. sItemName)
    DarkRP.notify(pPlayer, 0, 5, "[LVM ADMIN INVENTORY] Vous avez supprimé l'item ".. sItemName)

    SendInventoryWebhook(
        "[LVM ADMIN SUPPRESSION ITEM]",
        "Un item a été supprimé",
        ColorObjectToEmbedColor(Color(255, 0, 0)), 
        {
            { name = "Nom RP du joueur", value = pOwner:Nick(), inline = true },
            { name = "SteamID Du joueurs", value = pOwner:SteamID64(), inline = true },
            { name = "Nom RP de l'admin", value = pPlayer:Nick(), inline = true },
            { name = "SteamID de l'admin", value = pPlayer:SteamID64(), inline = true },
            { name = "Nom de l'item", value = sItemName, inline = true },
            { name = "Quantité", value = "1", inline = true }
        }
    )
end)

net.Receive("LVM:WQ_Inventory:OpenAdminPanel", function(_, pOwner)
    if not pOwner:IsSuperAdmin() then return end

    net.Start("LVM:WQ_Inventory:OpenAdminPanel")
    net.Send(pOwner)
end)


net.Receive("LVM:WQ_Inventory:SyncInventory", function(_, pOwner)
    WQ_Inventory:SyncInventory(pOwner)
end)


net.Receive("LVM:WQ_Inventory:OpenInventory:Admin", function(_, pOwner)
    if not pOwner:IsSuperAdmin() then return end


    local pTarget = net.ReadEntity()

    local text = "%s à ouvert l'inventaire de %s"
    LVM_V2:ServerActivity(text:format(pOwner:Nick(), pTarget:Nick()), pOwner)

    if not IsValid(pTarget) then return end
    if not pTarget:IsPlayer() then return end

    WQ_Inventory:SyncInventory(pTarget)

    local tPlayerInventory = WQ_Inventory:fcGetInventory(pTarget, pTarget:LVM_GetCurrentCharacter().id)
    if not istable(tPlayerInventory) then return end

    local tPlayerEquiped = WQ_Inventory:GetEquiped(pTarget, pTarget:LVM_GetCurrentCharacter().id)
    if not istable(tPlayerEquiped) then return end

    local tChar = pTarget:LVM_GetCurrentCharacter()
    if not tChar then return end

    net.Start("LVM:WQ_Inventory:OpenInventory:Admin")
    net.WriteEntity(pTarget)
    net.WriteUInt(#tPlayerInventory, 16)

    for _, tItem in pairs(tPlayerInventory) do
        net.WriteString(tItem.item)
        net.WriteInt(tItem.iAmount, 32)
        net.WriteInt(tItem.iCharid, 32)
    end

    net.WriteUInt(#tPlayerEquiped, 16)

    for _, tItem in pairs(tPlayerEquiped) do
        net.WriteString(tItem.item)
        net.WriteString(tItem.category)

        net.WriteInt(tItem.iCharid, 32)
    end

    net.WriteTable(tChar)

    net.Send(pOwner)
end)



net.Receive("LVM:WQ_Inventory:UnequipItem:Admin", function(_, pPlayer)
    if not pPlayer:IsSuperAdmin() then return end
    local pOwner = net.ReadEntity()

    local iCharId = pOwner:LVM_GetCurrentCharacter().id
    if not iCharId then return end


    local sItem = net.ReadString()
    local sCategory = net.ReadString()

    if not WQ_Inventory.tItemRegistered[sItem] then return end
    if not WQ_Inventory:IfHaveItemInEquiped(pOwner, iCharId, sItem) then return end


    local sItemName = WQ_Inventory.tItemRegistered[sItem].sName or sItem


    if not WQ_Inventory:UnequipItem(pOwner, sItem, sCategory) then
        DarkRP.notify(pOwner, 0, 5, "Vous ne pouvez pas dé-équiper cette item !")
        return
    end

    local __, sIem, boutique = WQ_Inventory:HasItem(pOwner, sItem)

    WQ_Inventory:RemoveEquip(pOwner, sItem, iCharId, sCategory)
    
    if boutique == true then
        WQ_Inventory:AddInventoryBoutique(pOwner, sItem, iCharId)
    else
        WQ_Inventory:AddInventory(pOwner, sItem, 1, iCharId)
    end


    DarkRP.notify(pOwner, 0, 5, "[LVM ADMIN INVENTORY] Un admin vous a désequiper l'item : ".. sItemName .." !")
    DarkRP.notify(pPlayer, 0, 5, "[LVM ADMIN INVENTORY] Vous avez désequiper l'item : ".. sItemName .." !")
end)


net.Receive("LVM:WQ_Inventory:EquipItem:Admin", function(_, pPlayer)
    if not pPlayer:IsSuperAdmin() then return end
    local pOwner = net.ReadEntity()

    local iCharId = pOwner:LVM_GetCurrentCharacter().id
    if not iCharId then return end

    local sItem = net.ReadString()
    local sCategory = net.ReadString()


    if not WQ_Inventory.tItemRegistered[sItem] then return end
    if not WQ_Inventory:IfHaveItemInInventory(pOwner, iCharId, sItem) then return end



    local sItemName = WQ_Inventory.tItemRegistered[sItem].sName or sItem

    if not WQ_Inventory:EquipiItem(pOwner, sItem, sCategory) then
        DarkRP.notify(pOwner, 0, 5, "[LVM ADMIN INVENTORY] Vous ne pouvez pas équiper l'item !")
        return
    end
    
    local __, sIem, boutique = WQ_Inventory:HasItem(pOwner, sItem)

    WQ_Inventory:AddEquip(pOwner, sItem, iCharId, sCategory, boutique)
    WQ_Inventory:RemoveInventory(pOwner, sItem, 1, iCharId)

    DarkRP.notify(pOwner, 0, 5, "[LVM ADMIN INVENTORY] Un admin vous a équiper l'item : ".. sItemName .." !")
    DarkRP.notify(pPlayer, 0, 5, "[LVM ADMIN INVENTORY] Vous avez équiper l'item : ".. sItemName .." !")
end)

------------------------------------ FUNCTION ------------------------------------

function WQ_Inventory:IfKenjutsuEquiped(pOwner)
    if not IsValid(pOwner) then return end

    local tData = WQ_Inventory.GetData(pOwner:SteamID64())

    for k, v in pairs(tData.tEquiped) do
        if v.category == "weapon" then
            return WQ_Inventory.tItemRegistered[v.item]
        end
    end

    return false
end

function WQ_Inventory:SyncInventory(pOwner)
    if not IsValid(pOwner) then return end

    local iCharId = pOwner.SelectedCharacter
    if not iCharId then return end

    local tInventory = WQ_Inventory:fcGetInventory(pOwner, iCharId)
    if not istable(tInventory) then return end

    pOwner.inventory = tInventory

    local tEquiped = WQ_Inventory:GetEquiped(pOwner, iCharId)
    if not istable(tEquiped) then return end

    pOwner.equiped = tEquiped

    net.Start("LVM:WQ_Inventory:SyncInventory")
    net.WriteUInt(#tInventory, 7)

    for _, tItem in pairs(tInventory) do
        net.WriteString(tItem.item)
        net.WriteInt(tItem.iAmount, 10)
        net.WriteInt(tItem.iCharid, 2)
    end

    net.WriteUInt(#tEquiped, 7)

    for _, tItem in pairs(tEquiped) do
        net.WriteString(tItem.item)
        net.WriteInt(tItem.iCharid, 2)
        net.WriteString(tItem.category)
    end

    net.Send(pOwner)
end

function WQ_Inventory:fcGetInventory(pOwner, iCharid)
    if not IsValid(pOwner) then return end

    local tData = WQ_Inventory.GetData(pOwner:SteamID64())

    local tInventory = {}

    for k, v in pairs(tData.tInventory) do
        if v.iCharid == iCharid then
            if v.iAmount == nil or v.iAmount < 0 then continue end
            if not WQ_Inventory.tItemRegistered[v.item] then continue end
            table.insert(tInventory, v)
        end
    end

    return tInventory
end

function WQ_Inventory:DeleteInventory(pOwner, iCharid)
    if not IsValid(pOwner) then return end

    local iSteamid = pOwner:SteamID64()
    local tData = WQ_Inventory.GetData(iSteamid)

    for k, v in pairs(tData.tInventory) do
        if v.iCharid == iCharid then
            if v.iAmount == nil or v.iAmount < 0 then continue end
            WQ_Inventory.tCache[iSteamid].tInventory[k] = nil
        end
    end

    for k, v in pairs(tData.tEquiped) do
        if v.iCharid == iCharid then
            WQ_Inventory.tCache[iSteamid].tEquiped[k] = nil
        end
    end

    WQ_Inventory.tCache[iSteamid].tEquiped = table.ClearKeys(WQ_Inventory.tCache[iSteamid].tEquiped)
    WQ_Inventory.tCache[iSteamid].tInventory = table.ClearKeys(WQ_Inventory.tCache[iSteamid].tInventory)

    WQ_Inventory:fcSaveData(iSteamid)

    --sql.Query("UPDATE LVM_inventory WHERE steamid = '"..pOwner:SteamID64().."' SET inventory = '".. util.TableToJSON(WQ_Inventory.tCache[iSteamid].tInventory) .."'")
end

function WQ_Inventory:GetEquiped(pOwner, iCharid)
    if not IsValid(pOwner) then return end

    local tData = WQ_Inventory.GetData(pOwner:SteamID64())
    if not tData then return end

    local tEquiped = {}

    for k, v in pairs(tData.tEquiped) do
        if v.iCharid == iCharid then
            if not WQ_Inventory.tItemRegistered[v.item] then continue end
            table.insert(tEquiped, v)
        end
    end

    return tEquiped
end

function WQ_Inventory:IfHaveItemInInventory(pOwner, iCharid, sItem)
    if not IsValid(pOwner) then return end

    local tData = WQ_Inventory.GetData(pOwner:SteamID64())

    for k, v in pairs(tData.tInventory) do
        if v.iCharid == iCharid and v.item == sItem then
            return true
        end
    end

    return false
end

function WQ_Inventory:IfHaveItemInEquiped(pOwner, iCharid, sItem)
    if not IsValid(pOwner) then return end

    local tData = WQ_Inventory.GetData(pOwner:SteamID64())

    for k, v in pairs(tData.tEquiped) do
        if v.iCharid == iCharid and v.item == sItem then
            return true
        end
    end

    return false
end

function WQ_Inventory:UnequipAll(pOwner)
    net.Start("LVM:WQ_Inventory:EquipKenjutsu")
    net.WriteBool(false)
    net.WriteEntity(pOwner)
    net.Broadcast()

    pOwner.Kenjutsu = nil;
    pOwner.KenjutsuMod = false;

    pOwner:SetModel(pOwner:GetBaseModel())

    net.Start("LVM:WQ_Inventory:SetModel")
    net.WriteString(pOwner:GetBaseModel())
    net.Send(pOwner)

    net.Start("LVM:WQ_Inventory:EquipMask")
    net.WriteBool(false)
    net.WriteEntity(pOwner)
    net.Broadcast()

    for i = 1, 3 do 
        net.Start("LVM:WQ_Inventory:EquipAccesory")
        net.WriteBool(false)
        net.WriteString(i) 
        net.WriteEntity(pOwner)
        net.Broadcast()
    end
end


function WQ_Inventory:GiveAllAccesoiry(pOwner)
    local charid = pOwner.SelectedCharacter
    if not charid then return end
    if not IsValid(pOwner) then return end
    if not pOwner:IsAnimateur() then return end
    if charid ~= 3 then return end

    local accesory = self:GetAccesory()
    for k, v in pairs(accesory) do
        if self:HasItem(pOwner, v) then
            continue
        end
        self:AddInventory(pOwner, v, 1, charid)
    end
    
end

hook.Add("LVM_CHARACTERS_SELECT_SEUL", "GiveAllAccesoiry", function(pOwner, char)
    if pOwner.SelectedCharacter ~= 3 then return end
    if not pOwner:IsAnimateur() then return end
    
    WQ_Inventory:GiveAllAccesoiry(pOwner)

end)




function WQ_Inventory:EquipiItem(pOwner, sItem, sCategory, toOnePlayer)
    if not IsValid(pOwner) then return end

    local tItem = WQ_Inventory.tItemRegistered[sItem]
    if not tItem then return end

    pOwner:SetLVM_Boost(sItem)

     if tItem.sItemType == "farm" then
        return false
    end

    if tItem.sItemType == "weapon" then
        local boutique = DLVM.GC:Get("boutique", pOwner:SteamID64())
        if boutique == nil then return end
        local sClassName = tItem.sClassName or "weapon_" .. sItem
        local pos = boutique[sClassName] and boutique[sClassName].vPos or tItem.vPos
        local aAng = boutique[sClassName] and boutique[sClassName].aAng or tItem.aAngle
        
    
        net.Start("LVM:WQ_Inventory:EquipKenjutsu")
        net.WriteBool(true)
        net.WriteString(sItem)
        net.WriteEntity(pOwner)
        net.WriteVector(pos)
        net.WriteAngle(aAng)
        
        if toOnePlayer == nil then
            net.Broadcast()
        else
            net.Send(toOnePlayer)
        end

        pOwner.Kenjutsu = sItem;
    elseif tItem.sItemType == "cloth" then
        pOwner:SetModel(tItem.sModel or pOwner:GetBaseModel())

        net.Start("LVM:WQ_Inventory:SetModel")
        net.WriteString(tItem.sModel or pOwner:GetBaseModel())
        net.Send(pOwner)
    elseif tItem.sItemType == "mask" then
        net.Start("LVM:WQ_Inventory:EquipMask")
        net.WriteBool(true)
        net.WriteString(tItem.sClassName)
        net.WriteEntity(pOwner)
        if toOnePlayer == nil then
            net.Broadcast()
        else
            net.Send(toOnePlayer)
        end

        VoiceBox.FX.Config:AddPlayerFX(pOwner:SteamID64(), "Masked")
    elseif tItem.sItemType == "helmet" then
        local helmet = tItem.sModel
    elseif tItem.sItemType == "armor" then
        local armor = tItem.sModel
    elseif tItem.sItemType == "pant" then
        local pant = tItem.sModel
    elseif tItem.sItemType == "shoes" then
        local shoes = tItem.sModel
    elseif tItem.sItemType == "eye" then
        if tItem.tBoost.DojutsuID ~= nil then
            if tItem.tBoost.DojutsuClan == pOwner:GetClan() then return true end
            local placement = (tItem.tBoost.DojutsuPlacement == 1 and "left" or tItem.tBoost.DojutsuPlacement == 2 and "right" or nil);
            local sharingan = "sharingan" .. tItem.tBoost.DojutsuLevel
            local left = (placement == "left" and sharingan or "default")
            local right = (placement == "right" and sharingan or "default")
            pOwner:SetPupilTexture(right, left)
            pOwner:SetNetworkVar("Dojutsu", tItem.tBoost.DojutsuID)
            --DLVM.Technics:SpecialDojutsu(pOwner, tItem.tBoost.DojutsuID, true, tItem.tBoost.DojutsuLevel, placement)
        end
    elseif type(tItem.sItemType) == "table" then
        for _, sType in pairs(tItem.sItemType) do
            if sType == sCategory then
                local accessoireNum = string.match(sType, "accesoire(%d+)")
                if accessoireNum then
                    net.Start("LVM:WQ_Inventory:EquipAccesory")
                    net.WriteBool(true)
                    net.WriteString(accessoireNum) 
                    net.WriteString(tItem.sClassName)
                    net.WriteEntity(pOwner)
                    
                    if toOnePlayer == nil then
                        net.Broadcast()
                    else
                        net.Send(toOnePlayer)
                    end


                    if tItem.bIsHat then 
                        local characters = DLVM.GC:Get("characters", pOwner:SteamID64())

                        if characters == nil then return end
                        if pOwner.SelectedCharacter == nil then return end
                        
                        local char = characters[pOwner.SelectedCharacter]
                        if table.Count(char) <= 0 then return end

                        local oldhair = char["face"]["hair"]
                        

                        if not bHoldHair[pOwner] then
                            bHoldHair[pOwner] = oldhair
                        end
                        char.face.hair = 2

                        DLVM.GC:Push("characters", pOwner:SteamID64(), characters)

                        LVM_V2.Characters:SyncPlayer(pOwner, false )
                        
                        LVM_V2.Characters:SyncFace(pOwner)
                        
                    end

                    break
                end
            end
        end
    end



    return true
end

function WQ_Inventory:SyncAllEquippedOfOtherPlayer(ply)
    if not IsValid(ply) then return end

    for _, pPlayer in pairs(player.GetAll()) do
        if not IsValid(pPlayer) then continue end
        if pPlayer == ply then continue end
        
        if pPlayer.SelectedCharacter == nil then continue end

        local tEquiped = WQ_Inventory:GetEquiped(pPlayer, pPlayer.SelectedCharacter)
        if not istable(tEquiped) then continue end
        
        for __,v in pairs(tEquiped) do
            --PrintTable(v)
            WQ_Inventory:EquipiItem(pPlayer, v.item, v.category, ply)
        end
    end

    MsgC(Color(255, 200, 0), "[LVM INVENTORY] ", Color(255, 255, 255), "Syncronize equipped item of other player for ", Color(100, 255, 100), ply:Nick() .. "\n")
    

end

function WQ_Inventory:SyncAllEquippedToOtherPlayer(ply)
    if not IsValid(ply) then return end

    local tEquiped = WQ_Inventory:GetEquiped(ply, ply.SelectedCharacter)

    for __,v in pairs(tEquiped) do
        WQ_Inventory:EquipiItem(ply, v.item, v.category)
    end
end

concommand.Add("sync_acc", function(ply)
    WQ_Inventory:SyncAllEquippedOfOtherPlayer(ply)
end)

function WQ_Inventory:UnequipItem(pOwner, sItem, sCategory)
    if not IsValid(pOwner) then return end

    local tItem = WQ_Inventory.tItemRegistered[sItem]
    if not tItem then return end

    pOwner:SetLVM_Boost(sItem, true)


    --print(sItem)

    if tItem.sItemType == "weapon" then
        --pOwner:StripWeapon(sItem)
        pOwner:SetNetworkVar("KenjutsuOut", false)
        pOwner.KenjutsuMod = false;
        DLVM:HoldtypeKenjutsu(pOwner, false)

        net.Start("LVM:WQ_Inventory:EquipKenjutsu")
        net.WriteBool(false)
        net.WriteEntity(pOwner)
        net.Broadcast()

        pOwner.Kenjutsu = nil;
    elseif tItem.sItemType == "cloth" then
        pOwner:SetModel(pOwner:GetBaseModel())

        net.Start("LVM:WQ_Inventory:SetModel")
        net.WriteString(pOwner:GetBaseModel())
        net.Send(pOwner)
    elseif tItem.sItemType == "mask" then
        net.Start("LVM:WQ_Inventory:EquipMask")
        net.WriteBool(false)
        net.WriteEntity(pOwner)
        net.Broadcast()

        VoiceBox.FX.Config:RemovePlayerFx(pOwner:SteamID64(), "Stormtrooper")
    elseif tItem.sItemType == "helmet" then
        local helmet = tItem.sModel
    elseif tItem.sItemType == "armor" then
        local armor = tItem.sModel
    elseif tItem.sItemType == "pant" then
        local pant = tItem.sModel
    elseif tItem.sItemType == "shoes" then
        local shoes = tItem.sModel
    elseif tItem.sItemType == "eye" then
        pOwner:SetPupilTexture("default", "default")
        if tItem.tBoost.DojutsuID ~= nil then
            if tItem.tBoost.DojutsuClan == pOwner:GetClan() then return true end
            --DLVM.Technics:SpecialDojutsu(pOwner, nil, false, 1, nil)
        end
    elseif type(tItem.sItemType) == "table" then
        for _, sType in pairs(tItem.sItemType) do
            if sType == sCategory then
                local accessoireNum = string.match(sType, "accesoire(%d+)")
                if accessoireNum then
                    net.Start("LVM:WQ_Inventory:EquipAccesory")
                    net.WriteBool(false)
                    net.WriteString(accessoireNum) 
                    net.WriteEntity(pOwner)
  
                    net.Broadcast()


                    if tItem.bIsHat then
                        if bHoldHair[pOwner] then
                            local characters = DLVM.GC:Get("characters", pOwner:SteamID64())

                            if characters == nil then return end
                            if pOwner.SelectedCharacter == nil then return end
                            
                            local char = characters[pOwner.SelectedCharacter]
                            if table.Count(char) <= 0 then return end

                            char.face.hair = bHoldHair[pOwner]
                          
                            bHoldHair[pOwner] = nil

                            DLVM.GC:Push("characters", pOwner:SteamID64(), characters)

                            LVM_V2.Characters:SyncPlayer(pOwner, false )
                            
                            LVM_V2.Characters:SyncFace(pOwner)
                        end
                    end
                    break
                end
            end
        end
    end

    return true
end

------------------------------------ PLAYERMETA ------------------------------------

local meta = FindMetaTable("Player")

function meta:LVM_GetCurrentCharacter()
    if self.LVM_Characters and self.LVM_Characters.Current then
        return self.LVM_Characters.Current
    end
    -- Fallback: essaye d'utiliser une méthode alternative si elle existe
    if self.ATG_GetCharacter then
        return self:ATG_GetCharacter()
    end
    return false
end

function meta:SetLVM_Boost(sItem, bRemove)
    local tItem = WQ_Inventory.tItemRegistered[sItem]
    if not tItem then return end

    self.activeBoosts = self.activeBoosts or {}

    if tItem.tBoost then
        for k, v in pairs(tItem.tBoost) do
            if bRemove then
                if self.activeBoosts[k] then
                    self:SetLVM_BoostValue(k, v, true)
                    self.activeBoosts[k] = nil
                end
            else
                if not self.activeBoosts[k] then
                    self:SetLVM_BoostValue(k, v, false)
                    self.activeBoosts[k] = v
                end
            end
        end
    end
end


function meta:SetLVM_BoostValue(sKey, iValue, bRemove)
    if sKey == "Chakra" then
        local iMaxChakra = self:GetMaxChakra()
        local iBoostAmount = (iMaxChakra * iValue) / 100

        if bRemove then
            local newchakra = CTG_Chakra.Config.MaxChakra[DLVM.Jobs[self:Team()]] + (self:GetSkill("chakra") * 300)
            self:SetMaxChakra(newchakra)
        --    self:SetCurrentChakra(newchakra)
            self:SetNetworkVar("LVM:WQ:ChakraBoost", false)
        else
            self:SetNetworkVar("LVM:WQ:ChakraBoost", true)
            self:SetMaxChakra(math.Clamp(iMaxChakra + iBoostAmount, iMaxChakra, iMaxChakra + iBoostAmount))
           -- self:SetCurrentChakra(math.Clamp(self:GetCurrentChakra() + iBoostAmount, 0, iMaxChakra + iBoostAmount))
        end
        
    elseif sKey == "Health" then

        local iMaxTotal = self:GetMaxHealth()
        local iTotal = self:Health()

        local iBoostAmount = (iMaxTotal * iValue) / 100

        if bRemove then

            local healthPercentage = (self.CTG_JOB_HP * iValue) / 100

            self:SetMaxHealth(self.CTG_JOB_HP)

            self:SetHealth(iTotal - healthPercentage)

            if self:Health() <= 0 then
                self:Kill()
            end

            self:SetNetworkVar("LVM:WQ:HealthBoost", false)

        else
            self:SetNetworkVar("LVM:WQ:HealthBoost", true)
            self:SetMaxHealth(iMaxTotal + iBoostAmount)
            self:SetHealth(math.Clamp(iTotal + iBoostAmount, iTotal, iMaxTotal + iBoostAmount))
        end

    elseif sKey == "Resistance" then

        if bRemove then
            
            self:RemoveBoostResistance(iValue)
        else
            self:ActiveBoostResistance(iValue)
        end
    elseif sKey == "Speed" then
        
        if bRemove then
            self:RemoveSpeedVitesse(iValue)
        else
            self:ActiveBoostVitesse(iValue)
        end
    elseif sKey == "DojutsuID" then
        if iValue < 0 then
            self.DojutsuID = nil;
            return
        end
        self.DojutsuID = iValue;
    elseif sKey == "DojutsuLevel" then
        if iValue < 0 then
            self.DojutsuLevel = nil;
            return
        end
        self.DojutsuLevel = iValue
    elseif sKey == "DojutsuPlacement" then
        if iValue < 0 then
            self.DojutsuPlacement = nil;
            return
        end
        self.DojutsuPlacement = (iValue == 1 and "left" or iValue == 2 and "right" or nil);
    elseif sKey == "DojutsuClan" then
        if iValue < 0 then
            self.DojutsuClan = nil;
            return
        end
        self.DojutsuClan = DLVM.Clans[iValue]
    end
end

------------------------------------ HOOK ------------------------------------

hook.Add("PlayerButtonDown", "B:WQ_Inventory:OpenInventory", function(pOwner, oKey)
    if oKey == KEY_F1 then
        WQ_Inventory:SyncInventory(pOwner)

        net.Start("LVM:WQ_Inventory:OpenInventory")
        net.Send(pOwner)
    end
end)

hook.Add("PlayerInitialSpawn", "H:WQ_Inventory:SyncInventory", function(pOwner)
    timer.Simple(3, function()
        WQ_Inventory:SyncAllEquippedOfOtherPlayer(pOwner)
    end)
end)

hook.Add("LVM_CHARACTERS_SELECT", "H:WQ_Inventory:SyncInventory", function(pOwner)
    timer.Simple(0.1, function()
        WQ_Inventory:SyncAllEquippedToOtherPlayer(pOwner)
    end)
end)

hook.Add("PlayerDeath", "H:WQ_Inventory:SyncInventory", function(pOwner)
    WQ_Inventory:SyncInventory(pOwner)


    local charId = pOwner:LVM_GetCurrentCharacter() and pOwner:LVM_GetCurrentCharacter().id or -1
    if not charId or tonumber(charId) < 1 then return end
    local tEquiped = WQ_Inventory:GetEquiped(pOwner, charId)

    if not istable(tEquiped) then return end
    if not IsValid(pOwner) then return end

    for k, v in pairs(tEquiped) do
        local tItem = WQ_Inventory.tItemRegistered[v.item]
        if tItem.bRemoveOnDeath then
            WQ_Inventory:RemoveEquip(pOwner, v.item, charId, v.category)

            DarkRP.notify(pOwner, 0, 5, "Vous avez perdu l'item: " .. tItem.sName.. " car vous êtes mort")
        end
    end

end)

-- hook.Remove("LVM_CHARACTERS_SELECT", "H:WQ_Inventory:SyncInventory")

hook.Add("PlayerLoadout", "H:WQ_Inventory:SyncInventory", function(pOwner)
    if not IsValid(pOwner) then return end
    local charId = pOwner:LVM_GetCurrentCharacter() and pOwner:LVM_GetCurrentCharacter().id or -1
    if not charId or tonumber(charId) < 1 then return end
    local tEquiped = WQ_Inventory:GetEquiped(pOwner, charId)

    if not istable(tEquiped) then return end
    if not IsValid(pOwner) then return end

    timer.Simple(0.3, function()
        WQ_Inventory:UnequipAll(pOwner)

        timer.Simple(0.1, function()
            WQ_Inventory:SyncInventory(pOwner)

            for k, v in pairs(tEquiped) do
                WQ_Inventory:EquipiItem(pOwner, v.item, v.category)
            end
        end)
    end)
end)


------------------------------------ DATABASE ------------------------------------

function WQ_Inventory:fcInitStorage()
    local query = sql.Query([[CREATE TABLE IF NOT EXISTS LVM_WQ_inventory(
		steamid STRING PRIMARY KEY,
        inventory TEXT,
        equiped TEXT
	)]])

    WQ_Inventory.tCache = {}
end

function WQ_Inventory:fcGetCache(pOwner)
    return WQ_Inventory.tCache[pOwner:SteamID64()]
end

function WQ_Inventory.GetData(iSteamid)
    if not iSteamid then return end

    if WQ_Inventory.tCache[iSteamid] then
        return WQ_Inventory.tCache[iSteamid]
    else
        local sRow = sql.Query(string.format("SELECT * FROM LVM_WQ_inventory WHERE steamid = %s LIMIT 1",
            sql.SQLStr(iSteamid)))
        local tData = nil
        if sRow then
            sRow = sRow[1]
            tData = {
                iSteamid = sRow.steamid,
                tInventory = util.JSONToTable(sRow.inventory),
                tEquiped = util.JSONToTable(sRow.equiped)
            }
        else
            tData = {
                iSteamid = iSteamid,
                tInventory = {},
                tEquiped = {}
            }
        end
        WQ_Inventory.tCache[iSteamid] = tData
    end

    return WQ_Inventory.tCache[iSteamid]
end

function WQ_Inventory:fcSaveData(iSteamid)
    if not iSteamid then return end

    local tData = WQ_Inventory.tCache[iSteamid]
    if not tData then return end

    local sInventory = util.TableToJSON(tData.tInventory)
    local sEquiped = util.TableToJSON(tData.tEquiped)

    local sRow = sql.Query(string.format([[SELECT * FROM LVM_WQ_inventory WHERE steamid = %s LIMIT 1]],
        sql.SQLStr(iSteamid)))
    if sRow then
        if json == "[]" then
            sql.Query(string.format([[DELETE FROM LVM_WQ_inventory WHERE steamid = %s]], sql.SQLStr(iSteamid)))
        else
            sql.Query(string.format([[UPDATE LVM_WQ_inventory SET inventory = %s, equiped = %s WHERE steamid = %s]],
                sql.SQLStr(sInventory), sql.SQLStr(sEquiped), sql.SQLStr(iSteamid)))
        end
    else
        sql.Query(string.format([[INSERT INTO LVM_WQ_inventory (steamid, inventory, equiped) VALUES (%s, %s, %s)]],
            sql.SQLStr(iSteamid), sql.SQLStr(sInventory), sql.SQLStr(sEquiped)))
    end

    tData.tInventory = util.JSONToTable(sInventory) or {}
    tData.tEquiped = util.JSONToTable(sEquiped) or {}

    WQ_Inventory.tCache[iSteamid] = tData

    local pOwner = player.GetBySteamID64(iSteamid)
    if IsValid(pOwner) then
        WQ_Inventory:SyncInventory(pOwner)
    end
end

function WQ_Inventory:HasItem(pOwner, sItem)
    if not IsValid(pOwner) and pOwner:IsPlayer() then return end

    local tData = WQ_Inventory.GetData(pOwner:SteamID64())
    local tInventory = tData.tInventory
    local tEquiped = tData.tEquiped
    local iCharid = pOwner.SelectedCharacter

    if not istable(tInventory) then return false, 0 end
    if not istable(tEquiped) then return false, 0 end

    for k, v in pairs(tInventory) do
        if v.item == sItem and v.iCharid == iCharid then
            return true, v.iAmount, v.bBoutique
        end
    end

    for k, v in pairs(tEquiped) do
        if v.item == sItem and v.iCharid == iCharid then
            return true, v.iAmount, v.bBoutique
        end
    end

    return false, 0
end

local tFarm = {

    ["planche_de_bois_brut"] = true,
    ["bois_ancestral"]= true,
    ["bois_renforce"]= true,
    ["bois_arbre_sacre"]= true,

    ["minerai_de_fer_brut"]= true,
    ["lingot_de_fer_purifie"]= true,
    ["lingot_de_fer_maudit"]= true,
    ["fer_des_etoiles"]= true,

    ["tissu_simple"]= true,
    ["tissu_raffine"]= true,
    ["tissu_spirituel"]= true,
    ["tissu_chakra_condense"]= true,

    ["fil_de_soie_basique"]= true,
    ["fibres_de_chanvre"]= true,
    ["soie_celeste"]= true,
    ["soie_araignee_demoniaque"]= true,
}

function WQ_Inventory:AddInventory(pOwner, sItem, iAmount, iCharid)
    if not IsValid(pOwner) and pOwner:IsPlayer() then return end

    if tFarm[sItem] then 
        if not pOwner:IsSuperAdmin() then 
            if pOwner:AdminMode() then
                DarkRP.notify(pOwner, 0, 5, "Vous ne pouvez pas ramasser d'item en mode admin !")
    
                return
            end
        end
    end


    local tData = WQ_Inventory.GetData(pOwner:SteamID64())
    local tInventory = tData.tInventory

    if not istable(tInventory) then return end

    for k, v in pairs(tInventory) do
        if v.item == sItem and v.iCharid == iCharid then
            v.iAmount = v.iAmount + iAmount
            WQ_Inventory:fcSaveData(pOwner:SteamID64())
            return
        end
    end

    table.insert(tInventory, {
        item = sItem,
        iAmount = iAmount,
        iCharid = iCharid,
    })

    WQ_Inventory:fcSaveData(pOwner:SteamID64())

    WQ_Inventory:SyncInventory(pOwner)
end

function WQ_Inventory:AddInventoryBoutique(pOwner, sItem, iCharid)
    if not IsValid(pOwner) and pOwner:IsPlayer() then return end

    local tData = WQ_Inventory.GetData(pOwner:SteamID64())
    local tInventory = tData.tInventory

    if not istable(tInventory) then return end

        for k, v in pairs(tInventory) do
        if v.item == sItem and v.iCharid == iCharid and v.bBoutique then
            v.iAmount = v.iAmount + 1
            WQ_Inventory:fcSaveData(pOwner:SteamID64())
            WQ_Inventory:SyncInventory(pOwner)
            return
        end
    end


    table.insert(tInventory, {
        item = sItem,
        iAmount = 1,
        iCharid = iCharid,
        bBoutique = true,
    })

    WQ_Inventory:fcSaveData(pOwner:SteamID64())

    WQ_Inventory:SyncInventory(pOwner)
end

function WQ_Inventory:RemoveInventory(pOwner, sItem, iAmount, iCharid)
    if not IsValid(pOwner) and pOwner:IsPlayer() then return end

    local tData = WQ_Inventory.GetData(pOwner:SteamID64())
    local tInventory = tData.tInventory

    if not istable(tInventory) then return end

    for k, v in pairs(tInventory) do
        if v.item == sItem and v.iCharid == iCharid then
            v.iAmount = v.iAmount - iAmount
            if v.iAmount <= 0 then
                table.remove(tInventory, k)
            end
            WQ_Inventory:fcSaveData(pOwner:SteamID64())
            return
        end
    end

    WQ_Inventory:fcSaveData(pOwner:SteamID64())
    WQ_Inventory:SyncInventory(pOwner)
end

function WQ_Inventory:AddEquip(pOwner, sItem, iCharid, category, bBoutique)
    if not IsValid(pOwner) and pOwner:IsPlayer() then return end

    local tData = WQ_Inventory.GetData(pOwner:SteamID64())
    local tEquiped = tData.tEquiped

    if not istable(tEquiped) then return end

    for k, v in pairs(tEquiped) do
        if v.item == sItem and v.iCharid == iCharid then
            v.category = category
            WQ_Inventory:fcSaveData(pOwner:SteamID64())
            return
        end
    end

    table.insert(tEquiped, {
        
        item = sItem,
        iCharid = iCharid,
        category = category,
        bBoutique = (bBoutique == nil and false or bBoutique),
        
    })

    WQ_Inventory:fcSaveData(pOwner:SteamID64())
    WQ_Inventory:SyncInventory(pOwner)
end

function WQ_Inventory:RemoveEquip(pOwner, sItem, iCharid, category)
    if not IsValid(pOwner) and pOwner:IsPlayer() then return end

    local tData = WQ_Inventory.GetData(pOwner:SteamID64())
    local tEquiped = tData.tEquiped

    if not istable(tEquiped) then return end

    for k, v in pairs(tEquiped) do
        if v.item == sItem and v.iCharid == iCharid then
            v.category = category
            table.remove(tEquiped, k)
            WQ_Inventory:fcSaveData(pOwner:SteamID64())
            return
        end
    end



    WQ_Inventory:fcSaveData(pOwner:SteamID64())
    WQ_Inventory:SyncInventory(pOwner)
end

WQ_Inventory:fcInitStorage()



------------------------------------ COMMAND ------------------------------------

concommand.Add("LVM_WQ_inventory_reset", function(pOwner)
    if not pOwner:IsSuperAdmin() then return end

    sql.Query("DROP TABLE LVM_WQ_inventory")
    WQ_Inventory:fcInitStorage()
end)

local accesgivetenue = {
    ["STEAM_0:0:580102902"] = true,
    ["STEAM_0:1:192301885"] = true
}

concommand.Add("LVM_WQ_give_item", function(pOwner, _, tArgs)
    local function aprint(...)
        local msg = { ... }
        if IsValid(ply) then
            local lua = string.format([[print("%s")]], table.concat(msg, " "))
            ply:SendLua(lua)
        else
            print(table.concat(msg, " "))
        end
    end



    if IsValid(pOwner) and not accesgivetenue[pOwner:SteamID()] then
        aprint("Vous n'avez pas la permission")
        return
    end

    local sSteamid, sClassname = tArgs[1], tArgs[2]
    if not sSteamid or not sClassname then
        aprint("LVM_WQ_give_item <steamid> <classname>")
        return
    end

    local pTarget = player.GetBySteamID64(tArgs[1])
    if not IsValid(pTarget) then
        aprint("Joueur introuvable")
        return
    end

    local iCharid = pTarget:LVM_GetCurrentCharacter().id
    if not iCharid then
        aprint("Personnage introuvable")
        return
    end

    if not WQ_Inventory.tItemRegistered[sClassname] then
        aprint("Item introuvable")
        return
    end

    WQ_Inventory:AddInventory(pTarget, sClassname, 1, iCharid)

    aprint("Item ajouté")
end)







---------------------------------------------------------------------


local tPlayerFood = {}


hook.Add("PlayerDeath", "RestoreFoodAfterDeathD", function(ply)
    if not IsValid(ply) then return end
    tPlayerFood[ply:SteamID64()] = ply:getDarkRPVar("Energy") or 100    
end)

hook.Add("PlayerSpawn", "RestoreFoodAfterDeath", function(pPlayer)
    if IsValid(pPlayer) and pPlayer:IsPlayer() then
        pPlayer:setSelfDarkRPVar("Energy", tPlayerFood[pPlayer:SteamID64()] or 50)        
        tPlayerFood[pPlayer:SteamID64()] = nil
    end
end)


----------------------------------------------------------------------

local MissveData = {}

util.AddNetworkString("LVM:Missive:Get")
util.AddNetworkString("LVM:Missive:GetC")
util.AddNetworkString("LVM:Missive:Open")
util.AddNetworkString("LVM:Missive:Create")
util.AddNetworkString("LVM:Missive:Show")
util.AddNetworkString("LVM:Missive:ResShow")
util.AddNetworkString("LVM:Missive:OpenParchemin")
util.AddNetworkString("LVM:Missive:Delete")

local function SendOneMissive(ply, missivetbl)
    if not IsValid(ply) then return end
    if not ply:IsPlayer() then return end
    
    net.Start("LVM:Missive:GetC")
        net.WriteBool(false)
        net.WriteString(missivetbl.missiveid)
        net.WriteUInt(missivetbl.date, 32)
    net.Send(ply)
end

local function SendAllMissives(ply)
    if not IsValid(ply) then return end
    if not ply:IsPlayer() then return end
    
    local result = {}
    local std64 = ply:SteamID64()

    for k, v in pairs(MissveData) do
        if v.date == nil or v.target == nil then continue end
        if v.target == std64 then
            result[v.missiveid] = v.date;
        end
    end
    
    local resulti = table.Count(result)
    
    net.Start("LVM:Missive:GetC")
        net.WriteUInt(resulti, 8)
        for k, v in pairs(result) do
            net.WriteString(k)
            net.WriteUInt(v, 32)
        end
    net.Send(ply)
end


net.Receive("LVM:Missive:Create", function(len, pOwner)
    if not IsValid(pOwner) then return end

    local title = net.ReadString()
    local content = net.ReadString()
    local target = net.ReadPlayer()

    local iCharId = pOwner.SelectedCharacter
    if not iCharId then return end
    if not IsValid(target) then return end
    
    local hasItem = WQ_Inventory:HasItem(pOwner, "parchemin_vide")
    
    if not hasItem then
        return
    end

    local randomid = "missive_"..pOwner:SteamID64().."_"..math.random(1, 10000)

    WQ_Inventory:RegisterMissive(randomid, title, content, true, pOwner, target:SteamID64())
    
    WQ_Inventory:RemoveInventory(pOwner, "parchemin_vide", 1, pOwner.SelectedCharacter)
    
    DarkRP.notify(pOwner, 0, 5, "Vous avez envoyé votre parchemin à ".. target:Nick() .." !")
    
    SendOneMissive(target, {missiveid = randomid, date = os.time()})

end)

net.Receive("LVM:Missive:Show", function(_, ply)
    local id = net.ReadString()
    
    print(id)
    
    local data = MissveData[id]
    
    PrintTable(data)
    if data == nil then return end
    if data.target != ply:SteamID64() then return end
    
    net.Start("LVM:Missive:ResShow")
        net.WriteString(data.title)
        net.WriteString(data.content)
    net.Send(ply)
    
    sql.Query("DELETE FROM LVM_WQ_missive WHERE missiveid = " .. sql.SQLStr(id))
    
    MissveData[id] = nil;
end)

net.Receive("LVM:Missive:Delete", function(_, ply)
    local id = net.ReadString()
    
    local data = MissveData[id]
    if data == nil then return end
    if data.target != ply:SteamID64() then return end
    
    sql.Query("DELETE FROM LVM_WQ_missive WHERE missiveid = " .. sql.SQLStr(id))
    MissveData[id] = nil;
end)

local CD = {}

net.Receive("LVM:Missive:Get", function(_, ply)
    
    if CD[ply] != nil and CD[ply] > CurTime() then
        return
    end
    
    SendAllMissives(ply)
    CD[ply] = CurTime() + 50

end)



function WQ_Inventory:InitStorageMissive()
    sql.Query([[CREATE TABLE IF NOT EXISTS LVM_WQ_missive(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            steamid TEXT,
            missiveid TEXT,
            target TEXT,
            title TEXT,
            content TEXT,
            created_at TEXT DEFAULT (datetime('now', 'localtime'))
        ) 
    ]])
end





function WQ_Inventory:RegisterMissive(missiveid, title, content, bAddSql, pOwner, pTarget)

    if bAddSql then

        local steamid = sql.SQLStr(pOwner:SteamID64())
        local safeMissiveId = sql.SQLStr(missiveid)
        local safeTitle = sql.SQLStr(title) 
        local safeContent = sql.SQLStr(content)
        local safeTarget = sql.SQLStr(pTarget)

        local query = sql.Query("INSERT INTO LVM_WQ_missive (steamid, missiveid, target, title, content) VALUES ("..
            steamid..", "..
            safeMissiveId..", "..
            safeTarget..", "..
            safeTitle..", "..
            safeContent..")")

        MissveData[missiveid] = {
            target = pTarget,
            steamid = pOwner:SteamID64(),
            title = title,
            content = content,
            date = os.time(),
        }
    end
end

function WQ_Inventory:IsMissive(missiveid)
    return MissveData[missiveid]
end

-- function WQ_Inventory:UseMissive(pOwner, missiveid)
--     if not MissveData[missiveid] then return end

--     local data = MissveData[missiveid]

--     net.Start("LVM:Missive:Open")
--         net.WriteString(data.itemname)
--         net.WriteString(data.title)
--         net.WriteString(data.content)
--     net.Send(pOwner)
-- end


function WQ_Inventory:LoadAllMisive()
    local query = sql.Query("SELECT * FROM LVM_WQ_missive")
    if not query then return end

    for k, v in pairs(query) do
        if MissveData[v.missiveid] then continue end

        MissveData[v.missiveid] = {
            steamid = v.steamid,
            target = v.target,
            title = v.title,
            content = v.content,
            date = v.created_at,
        }
    end
end

-- hook.Add("PlayerInitialSpawn", "WQ_LoadPlayerMissives", function(ply)
--     timer.Simple(1, function() 
--         if not IsValid(ply) then return end
--         print("PlayerInitialSpawn: " .. ply:Nick())
--         local steamid = ply:SteamID64()
--         if not MissveData then return end
--     end)
-- end)



WQ_Inventory:InitStorageMissive()

WQ_Inventory:LoadAllMisive()



net.Receive("WQ_Inventory:SaveWeaponModification", function(len, ply)
    local vPos = net.ReadVector()
    local aAng = net.ReadAngle()
    local sClassName = net.ReadString()

    if not IsValid(ply) then return end

    local boutique = DLVM.GC:Get("boutique", ply:SteamID64())
    if boutique == nil then return end
    if boutique[sClassName] == nil then
        boutique[sClassName] = {}
    end

    boutique[sClassName].vPos = vPos
    boutique[sClassName].aAng = aAng

    DLVM.GC:Push("boutique", ply:SteamID64(), boutique)

    LVM_V2.Boutique:SyncPlayer(ply)
end)