---------------------------------------
   ---@author: WQ
   ---@time: 31/05/2025 17:48
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------

util.AddNetworkString("LVM:Reroll:Start")
util.AddNetworkString("LVM:Reroll:Spin")

local boostKG = 1; -- 1 = Chance par défaut

LVM_V2.Reroll:SetChanceKeikkei("none", 1500000); -- Rien

LVM_V2.Reroll:SetChanceKeikkei("Futton", 6750 * boostKG); -- Commun
LVM_V2.Reroll:SetChanceKeikkei("Shakuton", 6750 * boostKG); -- Commun

LVM_V2.Reroll:SetChanceKeikkei("Inkuton", 2000 * boostKG); -- Rare
LVM_V2.Reroll:Set<PERSON><PERSON>ceKeikkei("Hyoton", 2000 * boostKG); -- Rare

LVM_V2.Reroll:SetChanceKeikkei("Kiminari", 1200 * boostKG); --- Epique
LVM_V2.Reroll:SetChanceKeikkei("Jiton", 1200 * boostKG); -- Epique

LVM_V2.Reroll:SetChanceKeikkei("Bakuton", 150 * boostKG); -- Mythique

LVM_V2.Reroll:SetChanceKeikkei("Jinton", 2 * boostKG); -- Légendaire
LVM_V2.Reroll:SetChanceKeikkei("Mokuton", 2 * boostKG); -- Légendaire


local Cooldown = {}

local nameByID = {"reroll", "reroll_keikkei"}

function LVM_V2.Reroll:AddReroll(ply, id, amount) 
    local boutique = DLVM.GC:Get("boutique", ply:SteamID64())
    
    if boutique == nil then return false end
    
    boutique[nameByID[id]] = boutique[nameByID[id]] + amount
        
    DLVM.GC:Push("boutique", ply:SteamID64(), boutique)
    
    return true;
end

require("reqwest")

function LVM_V2.Reroll:SendLogs(name, old, new, rerolls, steamid64, steamid, type, color)
    local message = "Le joueur "..name.." a reroll nature "..type 
    color = color or 16711904
    reqwest({
        method = "POST",
        url = "",
        timeout = 30,

        body = util.TableToJSON({
            embeds = {
                {
                    title = "Reroll Nature "..type,
                    description = message,
                    color = color,
                    fields = {
                        {
                            name = "Joueurs",
                            value = name,
                            inline = true
                        },
                        {
                            name = "SteamID 64",
                            value = steamid64,
                            inline = true
                        },
                        {
                            name = "SteamID",
                            value = tostring(steamid),
                            inline = true
                        },
                        {
                            name = "Ancienne Nature",
                            value = string.upper(old),
                            inline = true
                        },
                        {
                            name = "Nouvelle Nature",
                            value = string.upper(new),
                            inline = true
                        },
                        {
                            name = "Rerolls restants",
                            value = tostring(rerolls),
                            inline = true
                        }
                    },

                }
            }
        }),
        type = "application/json",

        headers = {
            ["User-Agent"] = "MyUserAgent/1.0",
        },
    })
end

local tnature = {
    [0] = "none",
    [1] = "Katon",
    [2] = "Doton",
    [3] = "Futon",
    [4] = "Raiton",
    [5] = "Suiton"
}


local function GetNatureById(id)
    if id == "" then return "none" end
    if id == 0 then return "none" end
    if tnature[id] == nil then return "none" end

    return tnature[id]
end

local function GetNatureByName(name, cb)
    for k, v in pairs(tnature) do
        if v == name then
            cb(k)
        end
    end
end

local Keikkei_Genkai = {
    [0] = "none",
    [1] = "Jiton",
    [2] = "Futton",
    [3] = "Shakuton",
    [4] = "Bakuton",
    [5] = "Kiminari",
    [6] = "Hyoton",
    [7] = "Inkuton",
    [8] = "Mokuton",
    [9] = "Jinton",
}

local function GetKeikkeiGenkaiById(id)
    if id == "" then return "none" end
    if id == 0 then return "none" end
    if Keikkei_Genkai[id] == nil then return "none" end

    return Keikkei_Genkai[id]
end

local function GetKeikkeiGenkaiByName(name, cb)
    for k, v in pairs(Keikkei_Genkai) do
        if v == name then
            cb(k)
        end
    end
end


net.Receive("LVM:Reroll:Start", function(_, ply)
    
    if ply.SelectedCharacter == 3 then return end
  
    local uin = net.ReadUInt(2)
    
    if Cooldown[ply:SteamID64()] != nil and Cooldown[ply:SteamID64()] > CurTime() then return end
    
    local boutique = DLVM.GC:Get("boutique", ply:SteamID64())
    local technics = DLVM.GC:GetByChar("jutsu", ply, nil, true)
    if boutique == nil then return end
    if technics == nil then return end
    
    local reroll = (uin == 1 and boutique.reroll or boutique.reroll_keikkei)
    
    if reroll <= 0 then return end
    
    Cooldown[ply:SteamID64()] = CurTime() + 1
    
    if uin == 1 then
        local NatureByJob = net.ReadUInt(2)
        
        if NatureByJob == 1 then
            if not LVM_V2.Reroll:CanByJob("base", DLVM.Jobs[ply:Team()]) then 
                DarkRP.notify(ply, 0, 5, "Vous ne pouvez pas reroll votre nature de base !")
                NatureByJob = 0 
                return 
            end
            NatureByJob = "base"
        elseif NatureByJob == 2 then
            if not LVM_V2.Reroll:CanByJob("reroll", DLVM.Jobs[ply:Team()]) then 
                DarkRP.notify(ply, 0, 5, "Vous ne pouvez pas reroll votre seconde nature !")
                NatureByJob = "base" 
                return 
            end
            NatureByJob = "reroll"
        elseif NatureByJob == 3 then
            if not LVM_V2.Reroll:CanByJob("gived", DLVM.Jobs[ply:Team()]) then 
                DarkRP.notify(ply, 0, 5, "Vous ne pouvez pas reroll votre troisème nature !")
                NatureByJob = "base" 
                return 
            end
            NatureByJob = "gived"
        end

    

        local newReroll = LVM_V2.Reroll:GetRandom(1)
        
        boutique.reroll = boutique.reroll - 1
        
        local oldNature = technics.nature[NatureByJob]
        
        DLVM:SendLogs("Reroll", "test")
        LVM_V2.Reroll:SendLogs(ply:Nick(), technics.nature[NatureByJob], newReroll, boutique.reroll, ply:SteamID64(), ply:SteamID(), "Nature Base")
        
        DLVM.GC:Push("boutique", ply:SteamID64(), boutique)
        
        GetNatureByName(newReroll, function(nature)
            technics.nature[NatureByJob] = nature;
            technics.Push(technics)
            
            DLVM.Technics:SyncPlayer(ply)
        end)
        
        Cooldown[ply:SteamID64()] = CurTime() + 1
        
        if oldNature != 0 then
            ply:ResetJutsuByNature(GetNatureById(oldNature))
        end
        
        GetNatureByName(newReroll, function(reroll)
            net.Start("LVM:Reroll:Spin")
                net.WriteUInt(reroll, 5)
            net.Send(ply)
        end)


        timer.Simple(0.2, function()
            if NatureByJob == "base" then
                ply.Nature = newReroll
            elseif NatureByJob == "reroll" then
                ply.RerollNature = newReroll
            elseif NatureByJob == "gived" then
                ply.GivedNature = newReroll
            end
        end)
        

    else
    
        local confirmation_bool = net.ReadBool()
    
        local newReroll = LVM_V2.Reroll:GetRandom(2)


        
        boutique.reroll_keikkei = boutique.reroll_keikkei - 1
        
        if newReroll == "none" then
            color = 16711680 
        else
            color = 65280 
        end

        
        LVM_V2.Reroll:SendLogs(ply:Nick(), technics.nature.keikkei_genkai, newReroll, boutique.reroll_keikkei, ply:SteamID64(), ply:SteamID(), "Genkai "..tostring(confirmation_bool).." confirmé.", color)
        
        DLVM.GC:Push("boutique", ply:SteamID64(), boutique)
        
        local oldNature = technics.nature.keikkei_genkai
        
        GetKeikkeiGenkaiByName(newReroll, function(reroll)
            technics.nature.keikkei_genkai = reroll;
            technics.Push(technics)
            DLVM.Technics:SyncPlayer(ply)
        end)
        
        Cooldown[ply:SteamID64()] = CurTime() + 1
        
        if oldNature != 0 then
            ply:ResetJutsuByNature(GetKeikkeiGenkaiById(oldNature))
        end
        
        GetKeikkeiGenkaiByName(newReroll, function(reroll)
            net.Start("LVM:Reroll:Spin")
                net.WriteUInt(reroll, 5)
            net.Send(ply)
        end)
    end
    
    timer.Simple(0.05, function()
        LVM_V2.Boutique:SyncPlayer(ply)
    end)
  
end)

function RerollCommand(ply, cmd, args)
    local id = tonumber(args[1])
    local count = tonumber(args[2]) or 1

    if not id or (id ~= 1 and id ~= 2) then
        print("Usage: LVM_reroll [1 = standard, 2 = kekkei genkai] [count = optional]")
        return
    end


    local results = {}

    for i = 1, count do
        timer.Simple(0.00001 * i, function()
            local result = LVM_V2.Reroll:GetRandom(id)
            results[result] = (results[result] or 0) + 1
        end)
    end
    
    timer.Simple(0.00001 * count, function()
        print("Reroll Summary for ID " .. id .. " (" .. count .. " rolls):")
        for k, v in pairs(results) do
            print("  " .. k .. ": " .. v)
        end
    end)
end


concommand.Add("LVM_reroll", function(ply, cmd, args)
    RerollCommand(ply, cmd, args)
end)

concommand.Add("LVM_count_kg_all", function(ply, cmd, args)
    if IsValid(ply) then
        ply:ChatPrint("Cette commande est réservée à la console serveur.")
        return
    end

    local path = "LVM/LVM_jutsu/"
    local files, _ = file.Find(path .. "*.json", "DATA")

    local count = 0
    local totalFiles = 0
    local kgDistribution = {}

    print("----- Analyse des Kekkei Genkai dans les fichiers -----")

    for _, filename in ipairs(files) do
        local steamid64 = string.StripExtension(filename)
        local content = file.Read(path .. filename, "DATA")

        if content then
            totalFiles = totalFiles + 1
            local success, data = pcall(util.JSONToTable, content)

            if success and istable(data) then
                for charID, charData in pairs(data) do
                    if istable(charData) and charData.nature and charData.nature.keikkei_genkai then
                        local kgID = charData.nature.keikkei_genkai
                        if kgID and kgID ~= 0 then
                            count = count + 1
                            local kgName = DLVM:GetKeikkeiGenkaiById(kgID) 

                            print("• SteamID64: " .. steamid64 .. " | Perso: " .. charID .. " | KG: " .. kgName)

                            kgDistribution[kgName] = (kgDistribution[kgName] or 0) + 1
                        end
                    end
                end
            else
                print("Erreur de lecture du fichier : " .. filename)
            end
        end
    end

    print("--------------------------------------------------------")
    print("Total fichiers analysés : " .. totalFiles)
    print("Total joueurs avec un KG : " .. count)
    print("Répartition des KG :")
    for kg, nb in pairs(kgDistribution) do
        print("  " .. kg .. " : " .. nb)
    end
    print("--------------------------------------------------------")
end)