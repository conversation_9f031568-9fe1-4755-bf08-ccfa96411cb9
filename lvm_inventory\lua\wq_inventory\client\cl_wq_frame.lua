local PANEL = {}

function PANEL:Init()
    self.base = vgui.Create("AvatarImage", self)
    self.base:Dock(FILL)
    self.base:SetPaintedManually(true)
end

function PANEL:GetBase()
    return self.base
end

function PANEL:PushMask(mask)
    render.ClearStencil()
    render.SetStencilEnable(true)

    render.SetStencilWriteMask(1)
    render.SetStencilTestMask(1)

    render.SetStencilFailOperation(STENCILOPERATION_REPLACE)
    render.SetStencilPassOperation(STENCILOPERATION_ZERO)
    render.SetStencilZFailOperation(STENCILOPERATION_ZERO)
    render.SetStencilCompareFunction(ST<PERSON>CI<PERSON>OMPARISONFUNCTION_NEVER)
    render.SetStencilReferenceValue(1)

    mask()

    render.SetStencilFailOperation(STENCILOPERATION_ZERO)
    render.SetStencilPassOperation(STENCILOPERATION_REPLACE)
    render.SetStencilZFailOperation(STENCILOPERATION_ZERO)
    render.SetStencilCompareFunction(STENCI<PERSON>OMPARISONFUNCTION_EQUAL)
    render.SetStencilReferenceValue(1)
end

function PANEL:PopMask()
    render.SetStencilEnable(false)
    render.ClearStencil()
end

function PANEL:SetPlayer(ply, size)
    self.base:SetPlayer(ply, size)
end

function PANEL:Paint(w, h)
    self:PushMask(function()
        local poly = {}

        local x, y = w / 2, h / 2
        for angle = 1, 360 do
            local rad = math.rad(angle)

            local cos = math.cos(rad) * y
            local sin = math.sin(rad) * y

            poly[#poly + 1] = {
                x = x + cos,
                y = y + sin
            }
        end

        draw.NoTexture()
        surface.SetDrawColor(255, 255, 255)
        surface.DrawPoly(poly)
    end)
    self.base:PaintManual()
    self:PopMask()
end

vgui.Register("CircularAvatar", PANEL)

function WQ_Inventory:fcOpenInventory(is_sync_open)
    local pOwner = LocalPlayer()

    if not pOwner.WQ_Inventory_tInventory then
        net.Start("LVM:WQ_Inventory:SyncInventory")
        net.SendToServer()
        return
    end

    if not IsValid(pOwner) then return end
    if IsValid(self.vFrame) then self.vFrame:Remove() end

    self.vFrame = vgui.Create("DFrame")
    self.vFrame:SetSize(SW(1662), SH(931))
    self.vFrame:Center()
    self.vFrame:SetDraggable(false)
    self.vFrame:ShowCloseButton(false)
    self.vFrame:SetTitle("")
    self.vFrame:MakePopup()
    self.vFrame.Paint = function(s, w, h)
        surface.SetDrawColor(255, 255, 255)
        surface.SetMaterial(WQ_Inventory.Materials["WQ_inventory_background"])
        surface.DrawTexturedRect(0, 0, w, h)
    end

    if is_sync_open == nil then
        self.vFrame:SetAlpha(0)
        self.vFrame:AlphaTo(255, 0.3, 0)
    end


    self.vFrame.bClose = vgui.Create("DButton", self.vFrame)
    self.vFrame.bClose:SetSize(SW(23), SH(22))
    self.vFrame.bClose:SetPos(SW(1600), SH(30))
    self.vFrame.bClose:SetText("")
    self.vFrame.bClose.Paint = function(self, w, h)
        surface.SetMaterial(WQ_MarketPlace.Material["close"])
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(0, 0, w, h)
    end
    self.vFrame.bClose.DoClick = function()
        self.vFrame:Remove()
        if IsValid(self.RightPanel) then
            self.RightPanel:Remove()
        end

        if IsValid(WQ_Inventory.RightPanelEquiped) then
            WQ_Inventory.RightPanelEquiped:Remove()
        end
    end

    if pOwner:IsSuperAdmin() then
        self.vFrame.btnAdmin = vgui.Create("DButton", self.vFrame)
        self.vFrame.btnAdmin:SetSize(SW(100), SH(50))
        self.vFrame.btnAdmin:SetPos(SW(30), SH(850))
        self.vFrame.btnAdmin:SetText("Admin")
        self.vFrame.btnAdmin.Paint = function(s, w, h)
            surface.SetDrawColor(31, 30, 30)
            surface.DrawRect(0, 0, w, h)
        end
        self.vFrame.btnAdmin.DoClick = function()
            if IsValid(self.vFrameAdmin) then self.vFrameAdmin:Remove() end
            if not pOwner:IsSuperAdmin() then return end
            net.Start("LVM:WQ_Inventory:OpenAdminPanel")
            net.SendToServer()
            self.vFrame:Remove()
        end
    end

    self.vFrame.vProfile = vgui.Create("DPanel", self.vFrame)
    self.vFrame.vProfile:SetSize(SW(281), SH(104))
    self.vFrame.vProfile:SetPos(SW(150), SH(175))
    self.vFrame.vProfile.Paint = function(s, w, h)
        surface.SetDrawColor(255, 255, 255)
        surface.SetMaterial(WQ_Inventory.Materials["WQ_iventory_profile_background"])
        surface.DrawTexturedRect(0, 0, w, h)
    end

    local avatar = vgui.Create("CircularAvatar", self.vFrame.vProfile)
    avatar:SetSize(SW(64), SH(64))
    avatar:SetPos(SW(30), SH(20))
    avatar:SetPlayer(pOwner, 64)


    self.vFrame.vProfile.vName = vgui.Create("DLabel", self.vFrame.vProfile)
    self.vFrame.vProfile.vName:SetFont(("LVM:WQ:INVENTORY:1:%i"):format(20))
    self.vFrame.vProfile.vName:SetText(pOwner:Nick())
    self.vFrame.vProfile.vName:SetTextColor(Color(255, 255, 255))

    surface.SetFont(("LVM:WQ:INVENTORY:1:%i"):format(20))
    local iTextWidthName = surface.GetTextSize(pOwner:Nick())

    local iFinalXName = SW(190)
    if SW(190) + iTextWidthName > SW(250) then
        iFinalXName = SW(250) - iTextWidthName
    end

    self.vFrame.vProfile.vName:SetSize(iTextWidthName, SH(20))
    self.vFrame.vProfile.vName:SetPos(iFinalXName, SH(20))

    self.vFrame.vProfile.vMoney = vgui.Create("DLabel", self.vFrame.vProfile)
    self.vFrame.vProfile.vMoney:SetFont(("LVM:WQ:INVENTORY:1:%i"):format(20))
    self.vFrame.vProfile.vMoney:SetText(WQ_Inventory.fcMoneyWithSpace(pOwner:getDarkRPVar("money")))
    self.vFrame.vProfile.vMoney:SetTextColor(Color(255, 255, 255))

    surface.SetFont(("LVM:WQ:INVENTORY:1:%i"):format(20))

    local iTextWidthMoney = surface.GetTextSize(WQ_Inventory.fcMoneyWithSpace(pOwner:getDarkRPVar("money")))

    local iFinalXMoney = SW(180)
    if SW(180) + iTextWidthMoney > SW(235) then
        iFinalXMoney = SW(235) - iTextWidthMoney
    end

    self.vFrame.vProfile.vMoney:SetSize(iTextWidthMoney, SH(20))
    self.vFrame.vProfile.vMoney:SetPos(iFinalXMoney, SH(53))


    self.vFrame.vInventory = vgui.Create("DPanel", self.vFrame)
    self.vFrame.vInventory:SetSize(SW(586), SH(496))
    self.vFrame.vInventory:SetPos(SW(150), SH(300))
    self.vFrame.vInventory.Paint = function(s, w, h)
        surface.SetDrawColor(255, 255, 255)
        surface.SetMaterial(WQ_Inventory.Materials["WQ_iventory_inventoory_background"])
        surface.DrawTexturedRect(0, 0, w, h)
    end


    local fcOldButtonPressInventory = self.vFrame.vInventory.OnMousePressed

    self.vFrame.vInventory.OnMousePressed = function(self, keyCode)
        if keyCode ~= MOUSE_LEFT then
            fcOldButtonPressInventory(self, keyCode)
            return
        end


        if IsValid(self.vFrame.vInventory.RightPanel) then
            self.vFrame.vInventory.RightPanel:Remove()
        end
    end

    self.vFrame.vInventory.vScroll = vgui.Create("DScrollPanel", self.vFrame.vInventory)
    self.vFrame.vInventory.vScroll:SetSize(SW(586), SH(400))
    self.vFrame.vInventory.vScroll:SetPos(SW(-10), SH(50))
    self.vFrame.vInventory.vScroll.Paint = function(s, w, h)
        surface.SetDrawColor(255, 255, 255, 0)
    end


    self.vFrame.vInventory.vScroll.vBar = self.vFrame.vInventory.vScroll:GetVBar()
    self.vFrame.vInventory.vScroll.vBar:SetSize(SW(10), SH(496))
    self.vFrame.vInventory.vScroll.vBar.Paint = function(s, w, h)
        surface.SetDrawColor(255, 255, 255)
        surface.SetMaterial(WQ_Inventory.Materials["WQ_inventory_scroll_background"])
        surface.DrawTexturedRect(0, 0, w, h)
    end


    self.vFrame.vInventory.vScroll.vBar.btnUp.Paint = function(self, w, h)
    end
    self.vFrame.vInventory.vScroll.vBar.btnDown.Paint = function(self, w, h)
    end

    self.vFrame.vInventory.vScroll.vBar.btnGrip.Paint = function(s, w, h)
        surface.SetDrawColor(255, 255, 255)
        surface.SetMaterial(WQ_Inventory.Materials["WQ_inventory_scroll_grip"])
        surface.DrawTexturedRect(0, 0, w, h)
    end




    self.vFrame.vInventory.vScroll:Receiver("inventory_item", function(pnl, tbl, bDropped, _, iX, iY)
        if not bDropped then return end
        surface.PlaySound("item_equip.mp3")

        net.Start("LVM:WQ_Inventory:UnequipItem")
        net.WriteString(tbl[1].sItem)
        net.WriteString(tbl[1].sCategorie)
        net.SendToServer()

        --   WQ_Inventory:UpdateModel(pOwner:GetModel() or WQ_Inventory.TenuBase)


        WQ_Inventory:CreateCategorie(tbl[1].sCategorie)
    end)

    self.vFrame.vInventory.vScroll.IconLayout = vgui.Create("DIconLayout", self.vFrame.vInventory.vScroll)
    self.vFrame.vInventory.vScroll.IconLayout:Dock(FILL)
    self.vFrame.vInventory.vScroll.IconLayout:DockMargin(30, 0, 0, 0)
    self.vFrame.vInventory.vScroll.IconLayout:SetSpaceX(SW(10))
    self.vFrame.vInventory.vScroll.IconLayout:SetSpaceY(SH(10))

    for i = 1, #pOwner.WQ_Inventory_tInventory do
        self.vFrame.vInventory.vScroll["Item" .. i] = self.vFrame.vInventory.vScroll.IconLayout:Add("DPanel")
        self.vFrame.vInventory.vScroll["Item" .. i]:SetSize(SW(96), SH(99))
        self.vFrame.vInventory.vScroll["Item" .. i]:SetMouseInputEnabled(true)
        self.vFrame.vInventory.vScroll["Item" .. i]:SetCursor("hand")
        self.vFrame.vInventory.vScroll["Item" .. i].Paint = function(s, w, h)
            local bg = WQ_Inventory.Materials["item_none"]

            if WQ_Inventory:GetItem(pOwner.WQ_Inventory_tInventory[i].item).sRarity then
                bg = WQ_Inventory.Materials
                    ["item_" .. WQ_Inventory:GetItem(pOwner.WQ_Inventory_tInventory[i].item).sRarity]
            end

            surface.SetDrawColor(255, 255, 255)
            surface.SetMaterial(bg)
            surface.DrawTexturedRect(0, 0, w, h)
        end



        self.vFrame.vInventory.vScroll["Item" .. i].OnStartDragging = function(self)
            surface.PlaySound("solve_naruto_base/ui/menu_selection_v2.wav")
            
            local itemTypes = WQ_Inventory:GetItem(pOwner.WQ_Inventory_tInventory[i].item).sItemType
            local itemTypesTable = type(itemTypes) == "table" and itemTypes or {itemTypes}
            
            for _, sCategorie in ipairs(itemTypesTable) do
                local sPanelName = string.upper(sCategorie:sub(1, 1)) .. sCategorie:sub(2)
                local sMaterial = WQ_Inventory.Materials["WQ_inventory_noequip_" .. sCategorie .. "_select"]
                
                if sMaterial == nil then return end
                
                if IsValid(WQ_Inventory.vFrame.vInventory["Equiped" .. sPanelName]) then
                    WQ_Inventory.vFrame.vInventory["Equiped" .. sPanelName].Paint = function(s, w, h)
                        surface.SetDrawColor(255, 255, 255)
                        surface.SetMaterial(sMaterial)
                        surface.DrawTexturedRect(0, 0, w, h)
                    end
                end
            end
        end

        self.vFrame.vInventory.vScroll["Item" .. i].OnStopDragging = function(self)
            local itemTypes = WQ_Inventory:GetItem(pOwner.WQ_Inventory_tInventory[i].item).sItemType
            local itemTypesTable = type(itemTypes) == "table" and itemTypes or {itemTypes}
            
            surface.PlaySound("solve_naruto_base/ui/menu_selection_v2.wav")
            
            for _, sCategorie in ipairs(itemTypesTable) do
                if sCategorie == "food" or sCategorie == "farm" or sCategorie == "util" then
                    continue
                end
                

                WQ_Inventory:CreateCategorie(sCategorie)
            end
        end

        self.vFrame.vInventory.vScroll["Item" .. i].InfoButton = vgui.Create("DButton",
            self.vFrame.vInventory.vScroll["Item" .. i])
        self.vFrame.vInventory.vScroll["Item" .. i].InfoButton:SetSize(SW(13), SH(13))
        self.vFrame.vInventory.vScroll["Item" .. i].InfoButton:SetPos(SW(75), SH(5))
        self.vFrame.vInventory.vScroll["Item" .. i].InfoButton:SetText("")
        self.vFrame.vInventory.vScroll["Item" .. i].InfoButton.Paint = function(self, w, h)
            surface.SetMaterial(WQ_MarketPlace.Material["info"])
            surface.SetDrawColor(color_white)
            surface.DrawTexturedRect(0, 0, w, h)
        end


        self.vFrame.vInventory.vScroll["Item" .. i].InfoButton.OnCursorEntered = function(btn)
            if IsValid(btn.InfoPanel) then return end
            surface.PlaySound("solve_naruto_base/ui/menu_selection_v2.wav")
            local parent = btn:GetParent()
            local x, y = gui.MousePos()

            btn.InfoPanel = WQ_Inventory:OpenInfoItemMenu(x + 10, y + 10,
                WQ_Inventory:GetItem(pOwner.WQ_Inventory_tInventory[i].item),
                pOwner.WQ_Inventory_tInventory[i].iAmount)
        end

        self.vFrame.vInventory.vScroll["Item" .. i].InfoButton.OnCursorExited = function(btn)
            if IsValid(btn.InfoPanel) then
                btn.InfoPanel:Remove()
                btn.InfoPanel = nil
            end
        end




        self.vFrame.vInventory.vScroll["Item" .. i].sItem = WQ_Inventory:GetItem(pOwner.WQ_Inventory_tInventory
            [i].item)
        self.vFrame.vInventory.vScroll["Item" .. i].iNumber = i
        self.vFrame.vInventory.vScroll["Item" .. i].iAmmount = pOwner.WQ_Inventory_tInventory[i].iAmount
        local dropcategorie = WQ_Inventory:GetItem(pOwner.WQ_Inventory_tInventory[i].item).sItemType



        local dropcategories = WQ_Inventory:GetItem(pOwner.WQ_Inventory_tInventory[i].item).sItemType
    

        if type(dropcategories) == "table" then
            for _, dropcategorie in ipairs(dropcategories) do
                self.vFrame.vInventory.vScroll["Item" .. i]:Droppable(dropcategorie)
            end
        else
            self.vFrame.vInventory.vScroll["Item" .. i]:Droppable(dropcategorie)
        end

        local fcOldMousePress = self.vFrame.vInventory.vScroll["Item" .. i].OnMousePressed

        self.vFrame.vInventory.vScroll["Item" .. i].OnMousePressed = function(self, keyCode)
            if keyCode ~= MOUSE_RIGHT then
                fcOldMousePress(self, keyCode)
                return
            end
            surface.PlaySound("solve_naruto_base/ui/menu_selection_v2.wav")
            if IsValid(WQ_Inventory.RightPanel) then
                WQ_Inventory.RightPanel:Remove()
            end

            local mx, my = gui.MousePos()
            WQ_Inventory:OpenRightPanel(mx, my,
                WQ_Inventory:GetItem(pOwner.WQ_Inventory_tInventory[i].item)
            )
        end


        if WQ_Inventory:GetItem(pOwner.WQ_Inventory_tInventory[i].item).sModel then
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon = vgui.Create("DModelPanel",
                self.vFrame.vInventory.vScroll["Item" .. i])
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon:SetSize(SW(64), SH(64))
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon:SetPos(SW(16), SH(13))
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon:SetModel(WQ_Inventory:GetItem(pOwner
                .WQ_Inventory_tInventory[i].item).sModel)
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon.LayoutEntity = function(self, Entity)
                return
            end
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon.Entity:SetPos(self.vFrame.vInventory.vScroll["Item" .. i]
                .vIcon.Entity:GetPos() - Vector(0, 0, 0))
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon:SetCamPos(Vector(15, -6, 60))
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon:SetLookAt(Vector(0, 0, 60))
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon:SetFOV(50)
            local iMin, iMax = self.vFrame.vInventory.vScroll["Item" .. i].vIcon.Entity:GetRenderBounds()
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon:SetCamPos(iMin:Distance(iMax) * Vector(.7, .7, .7))
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon:SetLookAt((iMax + iMin) / 2)
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon:SetMouseInputEnabled(false)
        elseif WQ_Inventory:GetItem(pOwner.WQ_Inventory_tInventory[i].item).sMaterial then
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon = vgui.Create("DPanel",
                self.vFrame.vInventory.vScroll["Item" .. i])
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon:SetSize(SW(64), SH(64))
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon:SetPos(SW(16), SH(13))
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon.Paint = function(s, w, h)
                surface.SetDrawColor(255, 255, 255)
                surface.SetMaterial(WQ_Inventory.Materials
                    [WQ_Inventory:GetItem(pOwner.WQ_Inventory_tInventory[i].item).sMaterial])
                surface.DrawTexturedRect(0, 0, w, h)
            end
            self.vFrame.vInventory.vScroll["Item" .. i].vIcon:SetMouseInputEnabled(false)
        end

        self.vFrame.vInventory.vScroll["Item" .. i].Ammount = vgui.Create("DLabel",
            self.vFrame.vInventory.vScroll["Item" .. i])
        self.vFrame.vInventory.vScroll["Item" .. i].Ammount:SetPos(SW(70), SH(70))
        self.vFrame.vInventory.vScroll["Item" .. i].Ammount:SetFont("LVM:WQ:INVENTORY:2:17")
        self.vFrame.vInventory.vScroll["Item" .. i].Ammount:SetTextColor(color_white)
        local sAmountText = "x" .. pOwner.WQ_Inventory_tInventory[i].iAmount
        surface.SetFont("LVM:WQ:INVENTORY:2:20")
        local iTextWidth, iTextHeight = surface.GetTextSize(sAmountText)
        self.vFrame.vInventory.vScroll["Item" .. i].Ammount:SetSize(iTextWidth, iTextHeight)
        self.vFrame.vInventory.vScroll["Item" .. i].Ammount:SetText(sAmountText)
    end

    local tPos = {
        ["mask"] = { x = 800, y = 175 },
        ["helmet"] = { x = 1400, y = 175 },
        ["cloth"] = { x = 800, y = 333 },
        ["armor"] = { x = 1400, y = 333 },
        ["pant"] = { x = 800, y = 491 },
        ["shoes"] = { x = 1400, y = 491 },
        ["weapon"] = { x = 800, y = 649 },
        ["eye"] = { x = 1400, y = 649 },
        ["accesoire1"] = { x = 970, y = 769 },
        ["accesoire2"] = { x = 1120, y = 769},
        ["accesoire3"] = { x = 1270, y = 769},
    }


    function WQ_Inventory:CreateCategorie(sCategorie)
        local bEquiped, tItem = WQ_Inventory.IsEquipedItem(sCategorie)
        local sPanelName = string.upper(sCategorie:sub(1, 1)) .. sCategorie:sub(2)

        if IsValid(self.vFrame.vInventory["Equiped" .. sPanelName]) then
            self.vFrame.vInventory["Equiped" .. sPanelName]:Remove()
        end

        self.vFrame.vInventory["Equiped" .. sPanelName] = vgui.Create("DPanel", self.vFrame)
        self.vFrame.vInventory["Equiped" .. sPanelName]:SetSize(SW(128), SH(128))
        if not tPos[sCategorie] then return end
        self.vFrame.vInventory["Equiped" .. sPanelName]:SetPos(SW(tPos[sCategorie].x), SH(tPos[sCategorie].y))

        self.vFrame.vInventory["Equiped" .. sPanelName].Paint = function(s, w, h)
            
            local bg = WQ_Inventory.Materials["WQ_inventory_noequip_" .. sCategorie]


            if bEquiped then
                bg = WQ_Inventory.Materials["item_select_none"]

                if WQ_Inventory:GetItem(tItem).sRarity then
                    bg = WQ_Inventory.Materials["item_select_" .. WQ_Inventory:GetItem(tItem).sRarity]
                end
            end

            surface.SetDrawColor(255, 255, 255)
            surface.SetMaterial(bg)
            surface.DrawTexturedRect(0, 0, w, h)
        end


        if bEquiped then
            if WQ_Inventory:GetItem(tItem).sModel then
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon = vgui.Create("DModelPanel",
                    self.vFrame.vInventory["Equiped" .. sPanelName])
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon:SetSize(SW(96), SH(99))
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon:SetPos(SW(15), SH(13))
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon:SetModel(WQ_Inventory:GetItem(tItem).sModel)
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon.LayoutEntity = function(self, Entity)
                    return
                end
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon.Entity:SetPos(self.vFrame.vInventory
                    ["Equiped" .. sPanelName].Icon.Entity:GetPos() - Vector(0, 0, 0))
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon:SetCamPos(Vector(15, -6, 60))
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon:SetLookAt(Vector(0, 0, 60))
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon:SetFOV(50)
                local iMin, iMax = self.vFrame.vInventory["Equiped" .. sPanelName].Icon.Entity:GetRenderBounds()
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon:SetCamPos(iMin:Distance(iMax) * Vector(.7, .7, .7))
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon:SetLookAt((iMax + iMin) / 2)
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon:SetMouseInputEnabled(true)
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon:SetCursor("hand")
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon:Droppable("inventory_item")

                self.vFrame.vInventory["Equiped" .. sPanelName].Icon.vPanel = self.vFrame.vInventory
                    ["Equiped" .. sPanelName]
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon.sItem = tItem
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon.sCategorie = sCategorie
            elseif WQ_Inventory:GetItem(tItem).sMaterial then
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon = vgui.Create("DPanel",
                    self.vFrame.vInventory["Equiped" .. sPanelName])
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon:SetSize(SW(96), SH(99))
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon:SetPos(SW(15), SH(13))
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon:SetMouseInputEnabled(true)
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon:Droppable("inventory_item")
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon:SetCursor("hand")
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon.vPanel = self.vFrame.vInventory
                    ["Equiped" .. sPanelName]
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon.sItem = tItem
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon.sCategorie = sCategorie
                self.vFrame.vInventory["Equiped" .. sPanelName].Icon.Paint = function(s, w, h)
                    surface.SetDrawColor(255, 255, 255)
                    surface.SetMaterial(WQ_Inventory.Materials[WQ_Inventory:GetItem(tItem).sMaterial])
                    surface.DrawTexturedRect(0, 0, w, h)
                end
            end

            local fcOldMousePressOldEquip = self.vFrame.vInventory["Equiped" .. sPanelName].Icon.OnMousePressed

            self.vFrame.vInventory["Equiped" .. sPanelName].Icon.OnMousePressed = function(self, keyCode)
                if keyCode ~= MOUSE_RIGHT then
                    fcOldMousePressOldEquip(self, keyCode)
                    return
                end
                surface.PlaySound("solve_naruto_base/ui/menu_selection_v2.wav")
                if IsValid(WQ_Inventory.RightPanelEquiped) then
                    WQ_Inventory.RightPanelEquiped:Remove()
                end

                local x, y = self:GetParent():GetPos()
                WQ_Inventory:OpenRightPanelEquiped(x, y, WQ_Inventory:GetItem(tItem), pOwner)
            end

            self.vFrame.vInventory["Equiped" .. sPanelName].Icon.OnStartDragging = function(self)
                surface.PlaySound("solve_naruto_base/ui/menu_selection_v2.wav")
            end

            self.vFrame.vInventory["Equiped" .. sPanelName].Icon.OnStopDragging = function(self)
                surface.PlaySound("solve_naruto_base/ui/menu_selection_v2.wav")
            end
        end

        self.vFrame.vInventory["Equiped" .. sPanelName]:Receiver(sCategorie, function(pnl, tbl, bDropped, _, iX, iY)
            if not bDropped then return end
            surface.PlaySound("solve_naruto_base/ui/wear_equipement_inv.wav")


            if tbl[1].sItem.sItemType == sCategorie then
                if bEquiped then
                    net.Start("LVM:WQ_Inventory:UnequipItem:Admin")
                    net.WriteEntity(pPlayer)
                    net.WriteString(tItem)
                    net.WriteString(sCategorie)
                    net.SendToServer()
                end


                net.Start("LVM:WQ_Inventory:EquipItem:Admin")
                net.WriteEntity(pPlayer)
                net.WriteString(tbl[1].sItem.sClassName)
                net.WriteString(sCategorie)
                net.SendToServer()

                timer.Simple(0.2, function()
                    WQ_Inventory:CreateCategorieAdmin(sCategorie)
                    net.Start("LVM:WQ_Inventory:OpenInventory:Admin")
                    net.WriteEntity(pPlayer)
                    net.SendToServer()
                end)
            elseif string.find(sCategorie, "accesoire") then
                if bEquiped then
                    net.Start("LVM:WQ_Inventory:UnequipItem:Admin")
                    net.WriteString(tItem)
                    net.WriteString(sCategorie)
                    net.SendToServer()
                end

                net.Start("LVM:WQ_Inventory:EquipItem:Admin")
                net.WriteEntity(pPlayer)
                net.WriteString(tbl[1].sItem.sClassName)
                net.WriteString(sCategorie)
                net.SendToServer()

                timer.Simple(0.2, function()
                    WQ_Inventory:CreateCategorieAdmin(sCategorie)
                    net.Start("LVM:WQ_Inventory:OpenInventory:Admin")
                    net.WriteEntity(pPlayer)
                    net.SendToServer()
                end)
            end
        end)
    end

    WQ_Inventory:CreateCategorieAdmin("mask")
    WQ_Inventory:CreateCategorieAdmin("helmet")
    WQ_Inventory:CreateCategorieAdmin("cloth")
    WQ_Inventory:CreateCategorieAdmin("armor")
    WQ_Inventory:CreateCategorieAdmin("pant")
    WQ_Inventory:CreateCategorieAdmin("shoes")
    WQ_Inventory:CreateCategorieAdmin("weapon")
    WQ_Inventory:CreateCategorieAdmin("eye")
    WQ_Inventory:CreateCategorieAdmin("accesoire1")
    WQ_Inventory:CreateCategorieAdmin("accesoire2")
    WQ_Inventory:CreateCategorieAdmin("accesoire3")


    function WQ_Inventory:UpdateModelAdmin(model)
        if not IsValid(self.vFrameAdmin) then return end
        if IsValid(self.vFrameAdmin.vInventory.ModelPlayer) then
            self.vFrameAdmin.vInventory.ModelPlayer:Remove()
        end

        self.vFrameAdmin.vInventory.ModelPlayer = vgui.Create("LVM.ModelPanel", self.vFrameAdmin)
        self.vFrameAdmin.vInventory.ModelPlayer:SetSize(SW(700), SH(800))
        self.vFrameAdmin.vInventory.ModelPlayer:SetPos(SW(825), SH(50))
        self.vFrameAdmin.vInventory.ModelPlayer:SetModel(model)

        self.vFrameAdmin.vInventory.ModelPlayer:SetAnimated(true)
        self.vFrameAdmin.vInventory.ModelPlayer:SetVisible(true)
        self.vFrameAdmin.vInventory.ModelPlayer:SetMouseInputEnabled(false)
        self.vFrameAdmin.vInventory.ModelPlayer:SetPlayerChildren(pPlayer)

        local eEnt = self.vFrameAdmin.vInventory.ModelPlayer:GetEntity()
        eEnt:SetSequence(eEnt:LookupSequence("idle_all_01"))
        self.vFrameAdmin.vInventory.ModelPlayer.LayoutEntity = function(eEntity)
            if eEnt:GetCycle() == 1 then eEnt:SetCycle(0) end
            self.vFrameAdmin.vInventory.ModelPlayer:RunAnimation()
        end

        local tCharAdmin = pPlayer:LVM_GetCharacter()
        if not tCharAdmin then return end

        local iLastX = 0

        eEnt:SetAngles(Angle(0, 20, 0))

        self.vFrameAdmin.vInventory.ModelPlayer.Think = function(self)
            if input.IsKeyDown(KEY_RIGHT) then
                local angCurrent = eEnt:GetAngles()
                eEnt:SetAngles(Angle(0, angCurrent.y + 1, 0))
            end

            if input.IsKeyDown(KEY_LEFT) then
                local angCurrent = eEnt:GetAngles()
                eEnt:SetAngles(Angle(0, angCurrent.y - 1, 0))
            end
        end
    end

    WQ_Inventory:UpdateModelAdmin(pPlayer:GetModel() or WQ_Inventory.TenuBase)
end

net.Receive("LVM:WQ_Inventory:OpenInventory:Admin", function()
    local pPlayer = net.ReadEntity()

    local iCount = net.ReadUInt(16)
    local tPlayerInventory = {}
    local tEquipedItems = {}

    for i = 1, iCount do
        local sItem = net.ReadString()
        local iAmmount = net.ReadInt(32)
        local iCharId = net.ReadInt(32)
        table.insert(tPlayerInventory, { sItem = sItem, iAmmount = iAmmount, iCharId = iCharId })
    end

    iCount = net.ReadUInt(16)

    for i = 1, iCount do
        local sItem = net.ReadString()
        local sCategorie = net.ReadString()
        local iCharId = net.ReadInt(32)
        table.insert(tEquipedItems, { sItem = sItem, sCategorie = sCategorie, iCharId = iCharId })
    end

    local tCharAdmin = net.ReadTable()

    WQ_Inventory:OpenInventoryAdmin(tPlayerInventory, tEquipedItems, pPlayer, tCharAdmin)
end)

-- Fin de fichier - fermeture propre



