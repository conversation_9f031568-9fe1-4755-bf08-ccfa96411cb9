LVM_V2.Lvl = LVM_V2.Lvl or {};

LVM_V2.Lvl.Config = {
    ["Base"] = 300,
    ["XPEach"] = {82.5, 126.5},
    ["XPTo"] = 90,
    ["XPTime"] = 2,
}

--[[
    r = 300
    f(x) = (r + ((x-1)*2) * r)
    
    lvl1 = 300
    lvl2 = 900
    lvl3 = 1800
    lvl4 = 3000
]]

---@param x integer
---@return integer
local function f(x)
    return (LVM_V2.Lvl.Config["XPTo"] + ((x) * ((x-1)*LVM_V2.Lvl.Config["XPTo"])))
end

---@param x integer
---@return integer
local function f2(x)
    if x < LVM_V2.Lvl.Config["XPTo"] then
        return 0;
    end

    local discriminant = 1 + (4 * (x - LVM_V2.Lvl.Config["XPTo"])) / LVM_V2.Lvl.Config["XPTo"]
    
    if discriminant < 0 then
        return 0;
    end
    
    local racine = math.sqrt(discriminant)
    
    return (1 + racine) / 2
end

---@param xp integer
---@return integer
function LVM_V2.Lvl:GetLevelByXP(xp)
    local lvl = f2(xp)
    
    return math.floor(lvl);
end

---@param lvl integer
---@return integer
function LVM_V2.Lvl:GetXPFromLevel(lvl)
    return f(lvl);
end

---@param xp integer
---@return integer
function LVM_V2.Lvl:GetXPToNextLevel(xp)
    local lvl = self:GetLevelByXP(xp)
    local nextLvl = self:GetXPFromLevel(lvl+1)
    
    return (nextLvl - lvl);
end

local PLAYER = FindMetaTable("Player")

---@return integer|boolean
function PLAYER:GetXP()
    if self:GetNWInt("LVMXP", 0) == 0 then
        if CLIENT then
            return 0
        else
            return false;
        end
    else
        return self:GetNWInt("LVMXP", 0)
    end
end

---@param xp integer
function PLAYER:AddXP(xp)
    self:SetNWInt("LVMXP", self:GetXP() + xp);
end

---@param xp integer
function PLAYER:RemoveXP(xp)
    self:SetNWInt("LVMXP", self:GetXP() - xp);
end

---@return integer
function PLAYER:GetLvl()
    local lvl = LVM_V2.Lvl:GetLevelByXP(self:GetXP())
    
    return lvl;
end

---@param lvl integer
function PLAYER:SetLvl(lvl)
    self:SetXP(LVM_V2.Lvl:GetXPFromLevel(lvl));
end

---@param xp integer
function PLAYER:SetXP(xp)
    self:SetNWInt("LVMXP", xp);
end