---------------------------------------
   ---@author: WQ
   ---@time: 18/05/2025 15:20
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------

LVM_V2.Competences = LVM_V2.Competences or {}

local PLAYER = FindMetaTable("Player")


---@return boolean "Le joueur est-il porter ?"
function PLAYER:IsCarry()
    if IsValid(self:GetNetworkVar("PorterPar", "")) then
        return true
    end
    
    return false;
end

---@return Entity|nil "Par qui le joueur est porter ?"
function PLAYER:CarryBy()
    if IsValid(self:GetNetworkVar("PorterPar", "")) then
        return self:GetNetworkVar("PorterPar", "")
    end
  
    return nil;
end

---@return boolean "Le joueur porte-il un autre joueur ?"
function PLAYER:IsCarrying()
    if <PERSON><PERSON>ali<PERSON>(self:GetNetworkVar("Porte", "")) then
        return true
    end
    
    return false;
end

---@return Entity|nil "Qu'elle est le joueur qu'il porte ?"
function PLAYER:CarriedPlayer()
    if IsValid(self:GetNetworkVar("Porte", "")) then
        return self:GetNetworkVar("Porter", "")
    end
  
    return nil;
end

---@return integer "Qu'elle est le style de combat du joueur ?"
function PLAYER:GetFightStyle()
    if CLIENT then
        return DLVM.Binds.FightStyle:GetInt()
    else
        return self:GetInfoNum("fightstyle", 1)
    end
end

hook.Add( "ShouldCollide", "LVM:Carry", function( ent1, ent2 )
	if ( ent1:IsCarry() and ent2:IsPlayer() )then return false end
end )

LVM_V2.Animations = {
    { Act = "act_bras_dos_idle", Name = "Bras dans le dos", Loop = true, CanWalk = true },
    { Act = "act_bras_croise_idle", Name = "Bras croisé", Loop = true, CanWalk = true },
    { Act = "nrp_base_bigsword_idle_loop", Name = "Poing vers le ciel", Loop = false, CanWalk = true },
    { Act = "nrp_beaten_bellydown_large_loop", Name = "A terre", Loop = true, CanWalk = true },
    { Act = "nrp_lobby_ay_etc_win_type01_start", Name = "Etirement d'épaule", Loop = false, CanWalk = true },
    { Act = "nrp_lobby_boruto_etc_team_fin", Name = "J'approuve", Loop = false, CanWalk = false },
    { Act = "nrp_lobby_etc_emotion_itachi_sitting_loop", Name = "Assis #2", Loop = true, CanWalk = false },
    { Act = "nrp_lobby_etc_emotion_combination_type03_a_loop", Name = "Tendre quelque chose", Loop = false, CanWalk = true },
    { Act = "nrp_lobby_etc_emotion_cry_type02_loop", Name = "Pleurer", Loop = false, CanWalk = false },
    { Act = "nrp_lobby_etc_emotion_d32_loop", Name = "Acte de présence", Loop = false, CanWalk = false },
    { Act = "nrp_lobby_etc_emotion_dying_type03_loop", Name = "Allongé dos contre terre", Loop = true, CanWalk = false },
    { Act = "nrp_lobby_etc_emotion_festivals_type02_loop", Name = "Récolter quelque chose", Loop = false, CanWalk = true },
    { Act = "nrp_lobby_etc_emotion_festivals_type04_loop", Name = "Applaudir étrangement", Loop = false, CanWalk = true },
    { Act = "nrp_lobby_etc_emotion_jiraiya_etc_win_type01_loop", Name = "J'ai gagné !", Loop = false, CanWalk = true },
    { Act = "nrp_lobby_etc_emotion_gorilla", Name = "Gorille", Loop = false, CanWalk = true },
    { Act = "nrp_lobby_etc_emotion_d34_start", Name = "Je suis trop beau", Loop = false, CanWalk = false },
    { Act = "nrp_ninjutsu_heal_d51nj2_shadowclone_idle_type03", Name = "Bras croisé rentré", Loop = true, CanWalk = true },
    { Act = "nrp_ninjutsu_heal_d51nj2_shadowclone_idle_type02", Name = "Baka", Loop = true, CanWalk = false },
    { Act = "gesture_salute", Name = "Salut militaire", Loop = false, CanWalk = false },
    { Act = "nrp_lobby_etc_emotion_sports_type02_loop", Name = "Abdos", Loop = true, CanWalk = false },
    { Act = "nrp_lobby_etc_emotion_wave_type01_loop", Name = "Danse de la vague", Loop = true, CanWalk = false },
    { Act = "nrp_beaten_d45nj3_loop", Name = "Vaincu", Loop = true, CanWalk = false },
    { Act = "nrp_lobby_etc_emotion_dance_type08_loop", Name = "Danse russe", Loop = true, CanWalk = true },
    { Act = "nrp_lobby_etc_emotion_d41_loop", Name = "Révérence", Loop = true, CanWalk = false },
    { Act = "nrp_lobby_etc_emotion_pose_type01_loop", Name = "Bras croisé détendu", Loop = true, CanWalk = false },
    { Act = "nrp_lobby_etc_emotion_happy_type01_loop", Name = "Mort de rire", Loop = true, CanWalk = false },
    { Act = "nrp_lobby_etc_emotion_happy_type01_loop", Name = "Mort de rire", Loop = true, CanWalk = false },
    { Act = "LVM_naruto_combatant_walk", Name = "Prêt au combat", Loop = true, CanWalk = false },
    { Act = "nrp_lobby_etc_emotion_d41_loop", Name = "Salutation distinguée", Loop = true, CanWalk = false },
    { Act = "nrp_lobby_etc_emotion_sleep_type01_loop", Name = "Repos", Loop = true, CanWalk = false },
    { Act = "nrp_lobby_etc_emotion_sitting_pattern_01_loop", Name = "Assise charismatique", Loop = true, CanWalk = false },
    { Act = "nrp_ninjutsu_heal_d24nj2_handseal", Name = "Fake Jutsu", Loop = true, CanWalk = false },
    { Act = "nrp_ninjutsu_heal_d51nj2_shadowclone_idle_type02", Name = "Blessé", Loop = true, CanWalk = false },
    { Act = "pose_ducking_01", Name = "Pose un genou au sol", Loop = true, CanWalk = false },
    { Act = "pose_standing_02 ", Name = "Pose chill", Loop = true, CanWalk = false },
}

-- "nrp_lobby_etc_emotion_d41_loop
-- "nrp_lobby_etc_emotion_pose_type01_loop",
-- "nrp_lobby_etc_emotion_happy_type01_loop",

-- nrp_ninjutsu_heal_d51nj2_shadowclone_idle_type03
-- nrp_ninjutsu_heal_d51nj2_shadowclone_idle_type02