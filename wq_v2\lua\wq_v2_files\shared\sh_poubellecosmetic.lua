---------------------------------------
   ---@author: WQ
   ---@time: 02/06/2025 23:38
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------

LVM_V2.PoubelleCosmetic = LVM_V2.PoubelleCosmetic or {};

LVM_V2.PoubelleCosmetic.Config = {
    ["Upgrade"] = {
        [1] = 2,
        [2] = 2,
        [3] = 2,
        [4] = 2,
    },
    ["DefaultPourcent"] = {
        [1] = 30,
        [2] = 40,
        [3] = 50,
        [4] = 55
    },
    ["UpgradePourcentAdd"] = {
        [1] = { [2] = 10, [3] = 5, [4] = 2.5, [5] = 1 },
        [2] = { [3] = 10, [4] = 5, [5] = 2.5 },
        [3] = { [4] = 10, [5] = 5 },
        [4] = { [5] = 10 }
    },
    ["MaxItems"] = 4
}

function LVM_V2.PoubelleCosmetic:ChangeItem(items)
    local rarityLead = 0;
    local rarityss = {}
    
    if #items > LVM_V2.PoubelleCosmetic.Config.MaxItems then
        return false, "Vous ne pouvez pas poser plus de " .. LVM_V2.PoubelleCosmetic.Config.MaxItems .. " items.";
    end
    
    local typeGlobal = items[1].Type;
    
    for i = 1, #items do
        local item = items[i]
        if not item or not item.Rarity then continue end
        if not item or not item.Type then continue end
        
        if typeGlobal != item.Type  then
            return false, "Vous ne pouvez pas poser des items de types différents.";
        end

        if not rarityss[item.Rarity] then
            rarityss[item.Rarity] = 0
        end

        rarityss[item.Rarity] = rarityss[item.Rarity] + 1
    end
    
    for rarity, count in SortedPairsByValue(rarityss, true) do
        rarityLead = rarity;
        break
    end
    
    for i = rarityLead, 1, -1 do
        if rarityss[i] != nil and i < rarityLead then
            if rarityss[i] != nil then
                return false, "Vous avez posé un item qui dépasse la rareté ou est égale a la rareté que vous pourriez obtenir.";
            end
        end
    end
    
    
    if rarityLead == 1 then
        return false, "Cette rareté ne peut pas être changée.";
    end
    
    local rarityResult = rarityLead-1
    local pourcent = LVM_V2.PoubelleCosmetic.Config.DefaultPourcent[rarityResult] or 0
    
    for rarity, count in SortedPairsByValue(rarityss, false) do
        pourcent = pourcent + ((LVM_V2.PoubelleCosmetic.Config.UpgradePourcentAdd[rarityResult][rarity] or 0) * count)
    end
    
    return pourcent, rarityResult, typeGlobal
    
end

print(LVM_V2.PoubelleCosmetic:ChangeItem({{ Rarity = 2, Type = "cloth" }, { Rarity = 2, Type = "cloth" }}))


hook.Add("SetupMove", "DisableSwimming", function(ply, mv, cmd)
    if ply:WaterLevel() > 1 then
        -- print("water")
        -- mv:SetVelocity(Vector(mv:GetVelocity().x, mv:GetVelocity().y, 0) + ply:GetUp() * 200) -- Pas de nage
    end
end)

hook.Add("Think", "PlayerWalkOnWater", function()
    -- for _, ply in ipairs(player.GetAll()) do
    --     if ply:WaterLevel() > 0 then -- Contact avec l’eau
    --         local pos = ply:GetPos()
    --         local tr = util.TraceLine({
    --             start = pos,
    --             endpos = pos - Vector(0, 0, 100),
    --             filter = ply
    --         })

    --         if tr.Hit then
            
    --             print("here")
    --             -- Positionner le joueur juste au-dessus de la surface
    --             ply:SetVelocity(Vector(0, 0, 10)) -- Légère poussée vers le haut
    --             local newZ = tr.HitPos.z + 5
    --             if pos.z < newZ then
    --                 ply:SetPos(Vector(pos.x, pos.y, newZ))
    --             end
    --         end
    --     end
    -- end
end)