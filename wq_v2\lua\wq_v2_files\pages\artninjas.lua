---------------------------------------
   ---@author: WQ
   ---@time: 18/05/2025 16:09
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------
 
LVM_V2.Pages["Library"]["Art Ninja"] = {};

LVM_V2.Pages["Library"]["Art Ninja"].Paint = function(self, w, h)
    surface.SetMaterial(LVM_V2:Material("learning/artninja.png"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(SW(-100), SH(-90), SW(1700), SH(950))
    
    local speciality = LocalPlayer():GetSpeciality()
    
    draw.SimpleText("Votre spécialité actuel : "..(speciality == 0 and "Aucune" or speciality == 1 and "Taijutsu" or "Kenjutsu"), LVM_V2.<PERSON><PERSON><PERSON>("LVM", 10, 500), SW(100), SH(50), color_white, TEXT_ALIGN_LEFT)
end

local frame;

local function UpgradeKenjutsu(technic)
    if IsValid(frame) then return end
    
    local jutsu_star = LocalPlayer().LVM_Kenjutsu
    
    frame = vgui.Create("DFrame")
    frame:SetSize(SW(344), SH(594))
    frame:Center()
    frame:SetTitle("")
    frame:MakePopup()
    frame:ShowCloseButton(false)
    frame:SetDraggable(false)
    frame:RequestFocus()
    frame:MoveToFront()
    frame.Paint = function(self, w, h)
        Derma_DrawBackgroundBlur(self)
        self:MoveToFront()
        surface.SetMaterial(LVM_V2:Material("learning/upgrade_frame.png"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(0, 0, w, h)
        
        masks.Start()
            if technic.Icon != "" then
                surface.SetMaterial(util.ImgurToMaterial(LVM_V2.Pages.GetIcon(technic), "noclamp smooth"))
            else
                surface.SetMaterial(LVM_V2:Material("learning/no_icon.png", "noclamp smooth"))
            end
            surface.SetDrawColor(255, 255, 255, 255)
            surface.DrawTexturedRect(SW(97), SH(55), SW(150), SH(150))
        masks.Source()
            LVM_V2.Pages.DrawCircle(SW(172), SH(143), SH(120/2), 360, color_white)
        masks.End()
        
        draw.SimpleText(technic.Name, LVM_V2.Fonts("LVM", 10, 500), SW(30), SH(18), color_white, TEXT_ALIGN_LEFT)
        
        if jutsu_star[technic.Id] != nil then
            draw.SimpleText("Niv. "..(jutsu_star[technic.Id] == 5 and "Max" or jutsu_star[technic.Id]), LVM_V2.Fonts("LVM", 5, 500), SW(112), SH(210), color_white, TEXT_ALIGN_LEFT)
            for i = 1, 5 do
                surface.SetMaterial(LVM_V2:Material("learning/star.png"))
                surface.SetDrawColor(jutsu_star[technic.Id] >= i and color_white or Color(100, 100, 100, 100))
                surface.DrawTexturedRect(SW(112) + ((i-1) * SW(25)), SH(230), SW(13), SH(13))
            end 
        end
        
        if jutsu_star[technic.Id] != nil then
            if tonumber(jutsu_star[technic.Id])+1 <= 5 then
                draw.SimpleText(DLVM.KenjutsuUpgrades[tonumber(jutsu_star[technic.Id])+1].." PDC", LVM_V2.Fonts("LVM", 5, 500), SW(312), SH(268), Color(35, 162, 42, 200), TEXT_ALIGN_RIGHT)
            else
                draw.SimpleText("Technique niveau max", LVM_V2.Fonts("LVM", 5, 500), SW(312), SH(268), Color(35, 162, 42, 200), TEXT_ALIGN_RIGHT)
            end
        end
        draw.SimpleText(technic.Name, LVM_V2.Fonts("LVM", 5, 500), SW(30), SH(268), Color(130, 130, 130, 200), TEXT_ALIGN_LEFT)
    end
    
    local richtext = vgui.Create("RichText", frame)
    richtext:SetSize(SW(283), SH(100))
    richtext:SetPos(SW(30), SH(300))
    function richtext:PerformLayout()

        self:SetFontInternal( LVM_V2.Fonts("LVM", 5, 500) )
        
    end
    richtext:InsertColorChange(255, 255, 255, 255)
    richtext:AppendText(technic.Description)
    
    local upgrade = vgui.Create("DButton", frame)
    upgrade:SetSize(SW(272), SH(49))
    upgrade:SetPos(SW(34), SH(436))
    upgrade:SetText("")
    upgrade.Paint = nil;
    upgrade.DoClick = function()
        if jutsu_star[technic.Id] == nil or jutsu_star[technic.Id] >= 5 then return end
    
        net.Start("LVM_V2:UpgradeKenjutsu")
            net.WriteUInt(technic.Id, 3)
        net.SendToServer()

        if IsValid(frame) then 
            frame:Remove()
        end
        
        timer.Simple(0.3, function()
            jutsu_star = LocalPlayer().LVM_Kenjutsu
        end)
    end
    
    local close = vgui.Create("DButton", frame)
    close:SetSize(SW(272), SH(49))
    close:SetPos(SW(34), SH(514))
    close:SetText("")
    close.Paint = nil;
    close.DoClick = function()
        if IsValid(frame) then 
            frame:Remove()
        end
    end
    
    local close = vgui.Create("DButton", frame)
    close:SetSize(SW(27), SH(27))
    close:SetPos(SW(300), SH(21))
    close:SetText("")
    close.Paint = nil;
    close.DoClick = function()
        if IsValid(frame) then 
            frame:Remove()
        end
    end
end

local function UnlockKenjutsu(technic)
    if IsValid(frame) then return end
    frame = vgui.Create("DFrame")
    frame:SetSize(SW(344), SH(594))
    frame:Center()
    frame:SetTitle("")
    frame:MakePopup()
    frame:ShowCloseButton(false)
    frame:SetDraggable(false)
    frame:RequestFocus()
    frame:MoveToFront()
    frame.Paint = function(self, w, h)
        Derma_DrawBackgroundBlur(self)
        self:MoveToFront()
        surface.SetMaterial(LVM_V2:Material("learning/unlock_frame.png"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(0, 0, w, h)
        
        masks.Start()
            if technic.Icon != "" then
                surface.SetMaterial(util.ImgurToMaterial(LVM_V2.Pages.GetIcon(technic), "noclamp smooth"))
            else
                surface.SetMaterial(LVM_V2:Material("learning/no_icon.png", "noclamp smooth"))
            end
            surface.SetDrawColor(255, 255, 255, 255)
            surface.DrawTexturedRect(SW(97), SH(88), SW(150), SH(150))
        masks.Source()
            LVM_V2.Pages.DrawCircle(SW(172), SH(163), SH(150/2), 360, color_white)
        masks.End()
        
        draw.SimpleText(technic.Name, LVM_V2.Fonts("LVM", 10, 500), SW(30), SH(18), color_white, TEXT_ALIGN_LEFT)
        
        draw.SimpleText(DLVM.KenjutsuUnlock[technic.Id].." PDC", LVM_V2.Fonts("LVM", 5, 500), SW(312), SH(268), Color(35, 162, 42, 200), TEXT_ALIGN_RIGHT)
        draw.SimpleText(technic.Name, LVM_V2.Fonts("LVM", 5, 500), SW(30), SH(268), Color(130, 130, 130, 200), TEXT_ALIGN_LEFT)
    end
    
    local richtext = vgui.Create("RichText", frame)
    richtext:SetSize(SW(283), SH(100))
    richtext:SetPos(SW(30), SH(300))
    function richtext:PerformLayout()

        self:SetFontInternal( LVM_V2.Fonts("LVM", 5, 500) )
        
    end
    richtext:InsertColorChange(255, 255, 255, 255)
    richtext:AppendText(technic.Description)
    
    local unlock = vgui.Create("DButton", frame)
    unlock:SetSize(SW(272), SH(49))
    unlock:SetPos(SW(34), SH(436))
    unlock:SetText("")
    unlock.Paint = nil;
    unlock.DoClick = function()
        net.Start("LVM_V2:BuyKenjutsu")
            net.WriteUInt(technic.Id, 3)
        net.SendToServer()
        if IsValid(frame) then 
            frame:Remove()
        end
    end
    
    local close = vgui.Create("DButton", frame)
    close:SetSize(SW(272), SH(49))
    close:SetPos(SW(34), SH(514))
    close:SetText("")
    close.Paint = nil;
    close.DoClick = function()
        if IsValid(frame) then 
            frame:Remove()
        end
    end
    
    local close = vgui.Create("DButton", frame)
    close:SetSize(SW(27), SH(27))
    close:SetPos(SW(300), SH(21))
    close:SetText("")
    close.Paint = nil;
    close.DoClick = function()
        if IsValid(frame) then 
            frame:Remove()
        end
    end
end

LVM_V2.Pages["Library"]["Art Ninja"].After = function(panel)
    local button_pos = {
        { x = 100, y = -111 }, -- 1
        
        { x = 135, y = -40 }, -- 2
        
        { x = 150, y = 45 }, -- 3
        
        { x = 110, y = 121 }, -- 4
        
        { x = 45, y = 170 }, -- 5
        
        { x = -45, y = 170 }, -- 6
        
        { x = -110, y = 121 }, -- 7
        
        { x = -150, y = 40 }, -- 8
        
        { x = -135, y = -45 }, -- 9
    
        { x = -100, y = -111 }, -- 10
    }
    
    local tree_pos = {
        ["Taijutsu"] = { x = 310, y = 370 },
    }
    
    local tree_data = {Taijutsu = {}}
    
    for i = 1, #DLVM.Technics do
        if tree_data[DLVM.Technics[i].Nature] != nil then
            local tech = DLVM.Technics[i]
            
            tree_data[tech.Nature][#tree_data[tech.Nature] + 1] = {
                Id = i,
                Name = tech.Name,
                Description = tech.Description,
                Icon = tech.Icon,
                PDC = tech.PDC,
                Rang = tech.Rang
            }
        end
    end
    
    local GetIcon = LVM_V2.Pages.GetIcon
    local DrawCircle = LVM_V2.Pages.DrawCircle
    local UpgradeSkill = LVM_V2.Pages.UpgradeSkill
    local UnlockSkill = LVM_V2.Pages.UnlockSkill
    
    local tooltip;
    local function ShowTooltip(bool, name, rang)
        
        if not bool then panel.PaintOver = nil; return end
        
        surface.SetFont(LVM_V2.Fonts("LVM", 9, 500))
        local w, h = surface.GetTextSize(name)
        
        panel.PaintOver = function(self)
            local x, y = panel:LocalCursorPos()
            x = x + SW(10)
        
            draw.RoundedBox(20, x, y, w + SW(20), SH(60), Color(50, 50, 50))
            draw.SimpleText(name, LVM_V2.Fonts("LVM", 9, 500), x + SW(8), y + SH(5), color_white, TEXT_ALIGN_LEFT)
            draw.SimpleText("Rareté:", LVM_V2.Fonts("LVM", 7, 500), x + SW(8), y + SH(30), Color(200, 200, 200), TEXT_ALIGN_LEFT)
            draw.SimpleText(rang, LVM_V2.Fonts("LVM", 7, 500), x + SW(60), y + SH(30), DLVM.RarityColors[rang], TEXT_ALIGN_LEFT)
        end
        
    end
    
    for tree_name,tree in pairs(tree_pos) do
        for i = 1, #button_pos do
            local w, h = SW(50), SH(50)
            
            local technic = tree_data[tree_name][i] == nil and {
                Id = -1,
                Name = "An error occured",
                Description = "Error",
                Icon = "",
                Rang = "?",
                PDC = -1
            } or tree_data[tree_name][i]
            
            local is_posses = LocalPlayer():HasJutsu(technic.Id)
            local dbutton = vgui.Create("DButton", panel)
            dbutton:SetSize(w, h)
            dbutton:SetPos(SW(tree.x) + SW(button_pos[i].x) - w/2, SH(tree.y) + SH(button_pos[i].y) - h/2)
            dbutton:SetText("")
            dbutton.Icon = GetIcon(technic)
            dbutton.Paint = function(self, w, h)
            
                is_posses = LocalPlayer():HasJutsu(technic.Id)
            
                DrawCircle(w/2, h/2, h/2, 360, (is_posses and color_white or Color(100, 100, 100)))
            
                local icon = self.Icon
            
                masks.Start()
                    if icon != nil then
                        surface.SetMaterial(util.ImgurToMaterial(icon, "smooth noclamp"))
                    else
                        surface.SetMaterial(LVM_V2:Material("learning/no_icon.png"))
                    end
                    surface.SetDrawColor((is_posses and Color(255,255,255) or Color(100,100,100)))
                    surface.DrawTexturedRect(0, 0, w, h)
                masks.Source()
                    DrawCircle(w/2, h/2, h*0.45, 360, color_white)
                masks.End()
            end
            dbutton.OnCursorEntered = function()
                local x, y = gui.MousePos()
            
                ShowTooltip(true, technic.Name, technic.Rang)
            end
            dbutton.OnCursorExited = function()
            
                ShowTooltip(false)
                
            end
            dbutton.DoClick = function()
                if LocalPlayer():GetSpeciality() != 1 then return end
                if not is_posses then
                    UnlockSkill(technic)
                else
                    UpgradeSkill(technic)
                end
            end
        end
    end
    
    local myKenjutsu = (LocalPlayer().Kenjutsu != nil and LocalPlayer().Kenjutsu.sClass or nil)
    
    local startX = SW(953)
    local startY = SH(430)
    
    if myKenjutsu != nil and DLVM.Kenjutsu[myKenjutsu] != nil then
        for i = 1, #DLVM.Kenjutsu[myKenjutsu].Technics do
            local w, h = SW(70), SH(70)
            
            local v = DLVM.Kenjutsu[myKenjutsu].Technics[i]
            
            local technic = {
                Id = i,
                Name = v.Name,
                Description = v.Description,
                Icon = v.Icon,
                Rang = "Kenjutsu",
                PDC = v.PDC
            }
            
            local is_posses = LocalPlayer():HasKenjutsu(technic.Id)
            local dbutton = vgui.Create("DButton", panel)
            dbutton:SetSize(w, h)
            dbutton:SetPos(startX + SW(130 * (i-1)), startY)
            dbutton:SetText("")
            dbutton.Icon = GetIcon(technic)
            dbutton.Paint = function(self, w, h)
            
                is_posses = LocalPlayer():HasKenjutsu(technic.Id)
            
                DrawCircle(w/2, h/2, h/2, 360, (is_posses and color_white or Color(100, 100, 100)))
            
                local icon = self.Icon
            
                masks.Start()
                    if icon != nil then
                        surface.SetMaterial(util.ImgurToMaterial(icon, "smooth noclamp"))
                    else
                        surface.SetMaterial(LVM_V2:Material("learning/no_icon.png"))
                    end
                    surface.SetDrawColor((is_posses and Color(255,255,255) or Color(100,100,100)))
                    surface.DrawTexturedRect(0, 0, w, h)
                masks.Source()
                    DrawCircle(w/2, h/2, h*0.45, 360, color_white)
                masks.End()
            end
            dbutton.OnCursorEntered = function()
                local x, y = gui.MousePos()
            
                ShowTooltip(true, technic.Name, technic.Rang)
            end
            dbutton.OnCursorExited = function()
            
                ShowTooltip(false)
                
            end
            dbutton.DoClick = function()
                if not is_posses then
                    UnlockKenjutsu(technic)
                else
                    UpgradeKenjutsu(technic)
                end
            end
        end
    end
    
    local chose_speciality = vgui.Create("DButton", panel)
    chose_speciality:SetSize(SW(200), SH(40))
    chose_speciality:SetPos(SW(210), SH(645))
    chose_speciality:SetText("")
    chose_speciality.Paint = function(self, w, h)
        local speciality = LocalPlayer():GetSpeciality()
    
        if self:IsHovered() then
            draw.RoundedBox(2, 0, 0, w, h, Color(46, 46, 46, (speciality == 0 and 200 or 100)))
            draw.RoundedBox(2, 0, 0, w, h-SH(1), Color(81, 81, 81, (speciality == 0 and 200 or 100)))
        else
            draw.RoundedBox(2, 0, 0, w, h, Color(81, 81, 81, (speciality == 0 and 255 or 100)))        
        end

        draw.SimpleText("Choisir cette spécialité", LVM_V2.Fonts("LVM", 8, 500), w/2, h/2, color_white, 1, 1)
    end
    chose_speciality.DoClick = function()
        if LocalPlayer():GetSpeciality() != 0 then return end
        net.Start("LVM:SelectMySpeciality")
            net.WriteUInt(1, 2)
        net.SendToServer()
    end
    
    local chose_speciality = vgui.Create("DButton", panel)
    chose_speciality:SetSize(SW(200), SH(40))
    chose_speciality:SetPos(SW(1040), SH(645))
    chose_speciality:SetText("")
    chose_speciality.Paint = function(self, w, h)
        local speciality = LocalPlayer():GetSpeciality()
    
        if self:IsHovered() then
            draw.RoundedBox(2, 0, 0, w, h, Color(46, 46, 46, (speciality == 0 and 200 or 100)))
            draw.RoundedBox(2, 0, 0, w, h-SH(1), Color(81, 81, 81, (speciality == 0 and 200 or 100)))
        else
            draw.RoundedBox(2, 0, 0, w, h, Color(81, 81, 81, (speciality == 0 and 255 or 100)))        
        end

        draw.SimpleText("Choisir cette spécialité", LVM_V2.Fonts("LVM", 8, 500), w/2, h/2, color_white, 1, 1)
    end
    chose_speciality.DoClick = function()
        if LocalPlayer():GetSpeciality() != 0 then return end

        local frameweapon = vgui.Create("DFrame")
        frameweapon:SetSize(SW(950), SH(350))
        frameweapon:Center()
        frameweapon:SetTitle("")
        frameweapon:ShowCloseButton(false)
        frameweapon:SetDraggable(false)
        frameweapon:MakePopup()
        frameweapon:SetAlpha(0)
        frameweapon:RequestFocus()
        frameweapon:MoveToFront()
        frameweapon.Close = function(self)
            
            self:AlphaTo(0, 0.3, 0, function(_, self)

                self:Remove()

            end)

        end
        frameweapon.Paint = function(self, w, h)
            self:MoveToFront()
            Derma_DrawBackgroundBlur(self)
            draw.RoundedBox(8, 0, 0, w, h, Color(28, 28, 28, 245))
            draw.SimpleText("Choisis ton arme Kenjutsu :", LVM_V2.Fonts("LVM", 8, 500), w/2, SH(30), color_white, 1, 1)
        end
        frameweapon:AlphaTo(255, 0.3, 0)

        local armes = {
            { name = "Lance",    icon = "lance.png",    class = "kenjutsu_common_ninja_lance"},
            { name = "Katana",   icon = "katana.png",    class = "kenjutsu_common_ninja_katana"},
            { name = "Dague",    icon = "dague.png",   class = "kenjutsu_common_ninja_dague"},
            { name = "Kama",     icon = "kama.png",   class = "kenjutsu_common_kama"},
            { name = "Massue",   icon = "massue.png",     class = "kenjutsu_common_ninja_massue"},
            { name = "Griffe",   icon = "griffes.png",     class = "kenjutsu_common_ninja_griffes"},
        }

        for i, arme in ipairs(armes) do
            local card = vgui.Create("DButton", frameweapon)
            card:SetSize(SW(130), SH(180))
            card:SetPos(SW(70) + SW(140)*(i-1), SH(70))
            card:SetText("")
            card.Paint = function(self, w, h)
                draw.RoundedBox(12, 0, 0, w, h, self:IsHovered() and Color(42, 135, 255, 150) or Color(44,44,44,180))
                surface.SetDrawColor(255,255,255,255)
                surface.SetMaterial(LVM_V2:Material("kenjutsu/"..arme.icon, "noclamp smooth"))
                surface.DrawTexturedRect(SW(2), SH(20), SW(128), SH(128))
                draw.SimpleText(arme.name, LVM_V2.Fonts("LVM", 7, 500), w/2, SH(90), color_white, 1, 1)
            end
            card.DoClick = function()
                net.Start("LVM:SelectMySpeciality")
                    net.WriteUInt(2, 2) 
                    net.WriteString(arme.class) 
                net.SendToServer()
                frameweapon:Close()
            end
        end


        local cancel = vgui.Create("DButton", frameweapon)
        cancel:SetSize(SW(180), SH(40))
        cancel:SetPos(frameweapon:GetWide()/2 - SW(90), frameweapon:GetTall() - SH(55))
        cancel:SetText("")
        cancel.Paint = function(self, w, h)
            draw.RoundedBox(8, 0, 0, w, h, Color(180,40,40,220))
            draw.SimpleText("Annuler", LVM_V2.Fonts("LVM", 7, 500), w/2, h/2, color_white, 1, 1)
        end
        cancel.DoClick = function()
            frameweapon:Close()
        end
    end
        
end


LVM_V2.Pages["F4"]["Kenjutsu"] = {};

LVM_V2.Pages["F4"]["Kenjutsu"].Paint = function(self, w, h)
    surface.SetMaterial(LVM_v2:Material("f4/jutsu.png"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(SW(110), SH(50), SW(1383), SH(710))
    
    if self.Kenjutsu != nil then
        for i = 1, 3 do
            
            local x, y = SW(180) + SW(170 * (i-1)), SH(640)
            local technic = DLVM.Kenjutsu[self.Kenjutsu].Technics[i]
    
            if technic != nil then
            
                masks.Start()
                    if technic.Icon != "" then
                        surface.SetMaterial(util.ImgurToMaterial(technic.Icon, "smooth mips"))
                    else
                        surface.SetMaterial(LVM_V2:Material("learning/no_icon.png"))
                    end
                    surface.SetDrawColor((LocalPlayer():HasKenjutsu(i) and Color(200, 200, 200) or color_white))
                    surface.DrawTexturedRect(x, y, SW(80), SH(80))
                masks.Source()
                    LVM_V2.Pages.DrawCircle(x + SW(37) + SW(2)*0.8, y + SH(37.5), SH(35), 360, nil)
                masks.End()
        
            end
            
        end
    end
    
    draw.RoundedBoxEx(6, SW(115), SH(57), SW(688+343), SH(34), Color(29, 29, 29), true, true, false, false)
    
    draw.SimpleText("Kenjutsu", LVM_V2.Fonts("LVM", 7, 500), SW(130), SH(63), color_white, TEXT_ALIGN_LEFT)
    
    if self.TechClicked != nil then
        
        local tech = DLVM.Kenjutsu[self.Kenjutsu].Technics[self.TechClicked]
        if tech == nil then return end
        
        if tech.Icon != "" then
            surface.SetMaterial(util.ImgurToMaterial(tech.Icon, "smooth mips"))
        else
            surface.SetMaterial(LVM_V2:Material("learning/no_icon.png"))
        end
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(SW(1255), SH(120), SW(128), SH(128))
        
        draw.SimpleText(tech.Name, LVM_V2.Fonts("LVM", 8, 500), SW(1180), SH(70), color_white, TEXT_ALIGN_LEFT)
        
        draw.SimpleText("Description", LVM_V2.Fonts("LVM", 5, 500), SW(1180), SH(320), Color(200, 200, 200, 30), TEXT_ALIGN_LEFT)
    end
    
    if LocalPlayer():GetSpeciality() != 2 then
        draw.SimpleText("Vous n'avez pas la spécialité Kenjutsu", LVM_V2.Fonts("LVM", 8, 500), SW(500), SH(200), Color(200, 200, 200, 30))
    end
    
    -- draw.RoundedBox(0, SW(500), SH(630), SW(300), SH(100), Color(32, 32, 32, 200))
end

LVM_V2.Pages["F4"]["Kenjutsu"].After = function(panel)
    panel.jutsu_actual = {}
    
    local scrollpanel = vgui.Create("DScrollPanel", panel)
    scrollpanel:SetSize(SW(1010), SH(491))
    scrollpanel:SetPos(SW(130), SH(110))
    
    local vbar = scrollpanel:GetVBar()
    vbar:SetWide(SW(4))
    function vbar.btnUp:Paint(w, h)
    end
    
    function vbar.btnDown:Paint(w, h)
    end
    
    function vbar.btnGrip:Paint(w, h)
        draw.RoundedBox(8, 0, 0, w, h, Color(100, 100, 100, 200))
    end
    
    function vbar:Paint(w, h)
        draw.RoundedBox(8, 0, 0, w, h, Color(0, 0, 0, 200))
    end
    
    local function ShowTooltip(bool, name, rang)
        
        if not bool then panel.PaintOver = nil; return end
        
        surface.SetFont(LVM_V2.Fonts("LVM", 9, 500))
        local w, h = surface.GetTextSize(name)
        
        panel.PaintOver = function(self)
            local x, y = panel:LocalCursorPos()
            x = x + SW(10)
        
            draw.RoundedBox(20, x, y, w + SW(20), SH(60), Color(50, 50, 50))
            draw.SimpleText(name, LVM_V2.Fonts("LVM", 9, 500), x + SW(8), y + SH(5), color_white, TEXT_ALIGN_LEFT)
            draw.SimpleText("Rareté:", LVM_V2.Fonts("LVM", 7, 500), x + SW(8), y + SH(30), Color(200, 200, 200), TEXT_ALIGN_LEFT)
            draw.SimpleText(rang, LVM_V2.Fonts("LVM", 7, 500), x + SW(60), y + SH(30), (DLVM.RarityColors[rang] == nil and color_white or DLVM.RarityColors[rang]), TEXT_ALIGN_LEFT)
        end
        
    end
    
    local richtext = vgui.Create("RichText", panel)
    richtext:SetSize(SW(270), SH(110))
    richtext:SetPos(SW(1180), SH(340))
    function richtext:PerformLayout()
        self:SetFontInternal(LVM_V2.Fonts("LVM", 5, 500))
    end
    richtext:InsertColorChange( 255, 255, 255, 255 )
    
    local function SetDescription(text)
        richtext:SetText("")
        
        richtext:InsertColorChange( 255, 255, 255, 255 )
        richtext:AppendText(text)
    end
    
    local myKenjutsu = (LocalPlayer().Kenjutsu != nil and LocalPlayer().Kenjutsu.sClass or nil)
    
    panel.Kenjutsu = myKenjutsu
    
    if myKenjutsu == nil then return end
    
    -- local name = DLVM.Kenjutsu[LocalPlayer().Kenjutsu.sClass].Name
    for k,v in pairs(DLVM.Kenjutsu) do
        local name = v.Name
        local is_posses = (LocalPlayer():IsDev() and true or (myKenjutsu == k))
        local menu = vgui.Create("DPanel", scrollpanel)
        menu:SetSize(SW(1010), SH(150))
        menu:DockMargin(0, 0, 0, SH(10))
        menu:Dock(TOP)
        menu.Paint = function(self, w, h)
            if is_posses then
                surface.SetMaterial(LVM_V2:Material("F4/bar.png"))
                surface.SetDrawColor(color_white)
                surface.DrawTexturedRect(0, 0, w, h)
                
                draw.SimpleText(name, LVM_V2.Fonts("LVM", 6, 500), w/2, SH(10), color_white, TEXT_ALIGN_CENTER)
            else
                surface.SetMaterial(LVM_V2:Material("F4/bar.png"))
                surface.SetDrawColor(color_white)
                surface.DrawTexturedRect(0, 0, w, h)
                
                draw.SimpleText(name, LVM_V2.Fonts("LVM", 6, 500), w/2, SH(10), Color(255, 255, 255, 100), TEXT_ALIGN_CENTER)
                
                surface.SetMaterial(LVM_V2:Material("F4/bar_locked.png"))
                surface.SetDrawColor(color_white)
                surface.DrawTexturedRect(0, 0, w, h)
            end
        end
        
        if is_posses then
                
            local nature_tech = DLVM.Kenjutsu[myKenjutsu].Technics
            
            for i = 1, #nature_tech do
                local dbutton = vgui.Create("DButton", menu)
                dbutton:SetSize(SW(75), SH(75))
                dbutton:SetPos(SW(23) + SW(99*(i-1)), SH(40))
                dbutton:SetText("")
                -- dbutton.TechID = nature_tech[i].TechID
                dbutton.Paint = function(s, w, h)
                    surface.SetMaterial(LVM_V2:Material("f4/jutsu_slot.png"))
                    surface.SetDrawColor(color_white)
                    surface.DrawTexturedRect(0, 0, w, h)
                    
                    if nature_tech[i].Icon != "" then
                        masks.Start()
                            surface.SetMaterial(util.ImgurToMaterial(nature_tech[i].Icon, "smooth mips"))
                            surface.SetDrawColor((LocalPlayer():HasKenjutsu(i) and color_white or Color(100,100,100)))
                            surface.DrawTexturedRect(0, 0, w, h)
                        masks.Source()
                            LVM_V2.Pages.DrawCircle(SW(38), SH(37), SH(32), 360, nil)
                        masks.End()
                    end
                end
                dbutton.DoClick = function()
                    panel.TechClicked = i
                    SetDescription(nature_tech[i].Description)
                end
                dbutton.OnCursorEntered = function()
                    ShowTooltip(true, nature_tech[i].Name, "Kenjutsu")
                end
                dbutton.OnCursorExited = function()
                    ShowTooltip(false)
                end
            end
            
        end
    end
    
    for i = 1, 3 do
    
        local button = vgui.Create("DButton", panel)
        button:SetSize(SW(90), SH(80))
        button:SetPos(SW(180) + SW(170 * (i-1)), SH(640))
        button:SetText("")
        button.Paint = nil;
        button.Tech = panel.jutsu_actual[i]
        button.OnCursorEntered = function(self)
            if DLVM.Kenjutsu[myKenjutsu] == nil then return end
            ShowTooltip(true, DLVM.Kenjutsu[myKenjutsu].Technics[i].Name, "Kenjutsu")
        end
        button.DoRightClick = function(self)
            ShowTooltip(false)
        end
        button.OnCursorExited = function()
            ShowTooltip(false)
        end
        
        local binder = vgui.Create("DBinder", panel)
        binder:SetSize(SW(25), SH(25))
        binder:SetPos(SW(230) + SW(170 * (i-1)), SH(700))
        binder:SetConVar("kkenjutsu_"..i)
        binder:SetFontInternal(LVM_V2.Fonts("LVM", 5, 500))
        binder:SetTextColor(color_white)
        binder.Paint = function(self, w, h)
            -- local key = input.GetKeyName(tonumber(self:GetValue()))
            draw.RoundedBox(8, 0, 0, w, h, Color(71, 71, 71))
            -- draw.SimpleText(key, LVM_V2.Fonts("LVM", 5, 500), w/2, h/2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        end
        
    end
    
    local button = vgui.Create("DButton", panel)
    button:SetSize(SW(280), SH(50))
    button:SetPos(SW(1180), SH(638))
    button:SetText("")
    button.Paint = function(self, w, h)
        -- draw.RoundedBox(0, 0, 0, w, h, color_white)
    end
    button.DoClick = function()
        panel.jutsu_actual = table.Copy(jutsu_old)
    end
    
    local button2 = vgui.Create("DButton", panel)
    button2:SetSize(SW(280), SH(50))
    button2:SetPos(SW(1180), SH(558))
    button2:SetText("")
    button2.Paint = function(self, w, h)
      --  draw.RoundedBox(6, 0, 0, w, h, Color(71, 71, 71))
    end
    button2.DoClick = function()
        DLVM:SaveBinds()
    end
    
    button2.OnRemove = button2.DoClick

end

LVM_V2.Pages["F4"]["Art Ninja"] = {};

local function DrawCircle(xPos, yPos, radius, max, mat)

    local segments = {}
    
    segments[#segments+1] = { x = xPos, y = yPos }
    
    for i = 1, max+1 do
        local x, y = math.cos(-math.rad(90 + i)), math.sin(-math.rad(90 + i))
        
        segments[#segments+1] = { x = xPos + (x *radius), y = yPos - (y * radius) }
    end
    
    
    surface.SetDrawColor(color_white)
    if mat == nil then
        draw.NoTexture()
    else
        surface.SetMaterial(mat)
    end
    surface.DrawPoly(segments)
    
end

LVM_V2.Pages["F4"]["Art Ninja"].Paint = function(self, w, h)
    surface.SetMaterial(LVM_V2:Material("redwings/ui/overlay.png"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(SW(110), SH(50), SW(1383), SH(710))
    
    if self.jutsu_actual == nil then return end
    for i = 1, 6 do
        
        local x, y = SW(180) + SW(170 * (i-1)), SH(640)
        local jutsu_id = self.jutsu_actual[i]

        if DLVM.Technics[jutsu_id] != nil then
            masks.Start()
            if DLVM.Technics[jutsu_id].Icon != "" then
                surface.SetMaterial(util.ImgurToMaterial(DLVM.Technics[jutsu_id].Icon, "smooth mips"))
            else
                surface.SetMaterial(LVM_V2:Material("learning/no_icon.png"))
            end
            surface.SetDrawColor(color_white)
            surface.DrawTexturedRect(x - SW(1) * 1.2, y - SH(3), SW(80), SH(80))
            masks.Source()
            DrawCircle(x + SW(37) + SW(2) * 0.8, y + SH(37.5), SH(35), 360, nil)
            masks.End()
        end
        
    end
    
    if self.TechClicked != nil then
        
        local tech = DLVM.Technics[self.TechClicked]
        if tech == nil then return end
        
        if tech.Icon != "" then
            surface.SetMaterial(util.ImgurToMaterial(tech.Icon, "smooth mips"))
            surface.SetDrawColor(color_white)
            surface.DrawTexturedRect(SW(1255), SH(120), SW(128), SH(128))
        else
            surface.SetMaterial(LVM_V2:Material("learning/no_icon.png"))
            surface.SetDrawColor(color_white)
            surface.DrawTexturedRect(SW(1255), SH(120), SW(128), SH(128))
        end
        
        draw.SimpleText(tech.Name, LVM_V2.Fonts("LVM", 8, 500), SW(1180), SH(70), color_white, TEXT_ALIGN_LEFT)
        
        draw.SimpleText("Description", LVM_V2.Fonts("LVM", 5, 500), SW(1180), SH(320), Color(200, 200, 200, 30), TEXT_ALIGN_LEFT)
        
    end
    
    draw.RoundedBoxEx(6, SW(115), SH(57), SW(688+343), SH(34), Color(29, 29, 29), true, true, false, false)
    
    draw.SimpleText("Art Ninja", LVM_V2.Fonts("LVM", 7, 500), SW(130), SH(63), color_white, TEXT_ALIGN_LEFT)
end

LVM_V2.Pages["F4"]["Art Ninja"].After = function(panel)
    
    
     
    local jutsu_old;
    panel.jutsu_actual = {}
    timer.Simple(0.1, function()
        jutsu_old = {
            DLVM.Binds["Jutsu1"]:GetInt(),
            DLVM.Binds["Jutsu2"]:GetInt(),
            DLVM.Binds["Jutsu3"]:GetInt(),
            DLVM.Binds["Jutsu4"]:GetInt(),
            DLVM.Binds["Jutsu5"]:GetInt(),
            DLVM.Binds["Jutsu6"]:GetInt()
        }
        
        panel.jutsu_actual = jutsu_old
    end)
    
    --panel.jutsu_actual = table.Copy(jutsu_old)
    
    local scrollpanel = vgui.Create("DScrollPanel", panel)
    scrollpanel:SetSize(SW(1010), SH(491))
    scrollpanel:SetPos(SW(130), SH(110))
    
    for i = 1, 3 do
        local dbutton = vgui.Create("DButton", panel)
        dbutton:SetSize(SW(30), SH(30))
        dbutton:SetPos(SW(70), SH(640 + (i-1)*35))
        dbutton:SetText("")
        dbutton.Paint = function(s, w, h)
            draw.SimpleText(i, LVM_V2.Fonts("LVM", 7, 500), w/2, h/2, (DLVM.Binds["Tree"]:GetInt() == i and color_white or Color(200, 200, 200, 200)), 1, 1)
        end
        dbutton.DoClick = function()
            DLVM.Binds["Tree"]:SetInt(i)
            DLVM:RetrieveBinds(LocalPlayer():GetNWInt("CharacterID", 1), i)
            
            panel.jutsu_actual = {
                DLVM.Binds["Jutsu1"]:GetInt(),
                DLVM.Binds["Jutsu2"]:GetInt(),
                DLVM.Binds["Jutsu3"]:GetInt(),
                DLVM.Binds["Jutsu4"]:GetInt(),
                DLVM.Binds["Jutsu5"]:GetInt(),
                DLVM.Binds["Jutsu6"]:GetInt()
            }
        end
     end
    
    local vbar = scrollpanel:GetVBar()
    vbar:SetWide(SW(4))
    function vbar.btnUp:Paint(w, h)
    end
    
    function vbar.btnDown:Paint(w, h)
    end
    
    function vbar.btnGrip:Paint(w, h)
        draw.RoundedBox(8, 0, 0, w, h, Color(100, 100, 100, 200))
    end
    
    function vbar:Paint(w, h)
        draw.RoundedBox(8, 0, 0, w, h, Color(0, 0, 0, 200))
    end
    
    local speciality = LocalPlayer():GetSpeciality()
    
    local tooltip;
    local selected_id = 0;

    local function ShowTooltip(bool, name, rang)
        
        if not bool then panel.PaintOver = nil; return end
        
        surface.SetFont(LVM_V2.Fonts("LVM", 9, 500))
        local w, h = surface.GetTextSize(name)
        
        panel.PaintOver = function(self)
            local x, y = panel:LocalCursorPos()
            x = x + SW(10)
        
            draw.RoundedBox(20, x, y, w + SW(20), SH(60), Color(50, 50, 50))
            draw.SimpleText(name, LVM_V2.Fonts("LVM", 9, 500), x + SW(8), y + SH(5), color_white, TEXT_ALIGN_LEFT)
            draw.SimpleText("Rareté:", LVM_V2.Fonts("LVM", 7, 500), x + SW(8), y + SH(30), Color(200, 200, 200), TEXT_ALIGN_LEFT)
            draw.SimpleText(rang, LVM_V2.Fonts("LVM", 7, 500), x + SW(60), y + SH(30), DLVM.RarityColors[rang], TEXT_ALIGN_LEFT)
        end
        
    end
    
    local richtext = vgui.Create("RichText", panel)
    richtext:SetSize(SW(270), SH(110))
    richtext:SetPos(SW(1180), SH(340))
    function richtext:PerformLayout()
        self:SetFontInternal(LVM_V2.Fonts("LVM", 5, 500))
    end
    richtext:InsertColorChange( 255, 255, 255, 255 )
    
    local function SetDescription(text, level)
        richtext:SetText("")

        richtext:InsertColorChange(255, 255, 255, 255)
        richtext:AppendText(text.."\n")

        richtext:InsertColorChange(255, 255, 0, 255)
        richtext:AppendText("Niveau : " .. level .. "\n")
        
        local id = selected_id;

        
        LVM_V2:GetConfigJutsu(id, function(config)
            if id != selected_id then print("id not valid") return end
            if not IsValid(richtext) then print("richtext not valid") return end
            local jutsuConfig = LVM_V2:GetConfigJutsuLevel(config, level)
            
            for key, value in pairs(jutsuConfig) do
                local color = LVM_V2.Equilibrage.Config["Color"][key] or color_white
                local text = (LVM_V2.Equilibrage.Config["Key"][key] or key)
    
                richtext:InsertColorChange(color.r, color.g, color.b, 255)
                richtext:AppendText(text.." : ")
    
                richtext:AppendText(value)
                
                richtext:AppendText("\n")
            end
        end)

    end
    
    local natures = DLVM.Arts
    
    local function AddNature(name)
        if name == "none" then return end
        
        local is_not_speciality = (name == "Taijutsu" and LocalPlayer():GetSpeciality() != 1)
        
        local is_posses = (LocalPlayer():IsDev() and true or (speciality == 1))
    
        local menu = vgui.Create("DPanel", scrollpanel)
        menu:SetSize(SW(1010), SH(150))
        menu:DockMargin(0, 0, 0, SH(10))
        menu:Dock(TOP)
        menu.Paint = function(self, w, h)
            if is_not_speciality then
                surface.SetMaterial(LVM_V2:Material("F4/bar.png"))
                surface.SetDrawColor(color_white)
                surface.DrawTexturedRect(0, 0, w, h)
                
                draw.SimpleText(name, LVM_V2.Fonts("LVM", 6, 500), w/2, SH(10), color_white, TEXT_ALIGN_CENTER)
                
                draw.SimpleText("Vous devez vous spécialisé en Taijutsu pour l'utiliser !", LVM_V2.Fonts("LVM", 8, 500), w/2, SH(60), color_white, TEXT_ALIGN_CENTER)
            elseif is_posses then
                surface.SetMaterial(LVM_V2:Material("F4/bar.png"))
                surface.SetDrawColor(color_white)
                surface.DrawTexturedRect(0, 0, w, h)
                
                draw.SimpleText(name, LVM_V2.Fonts("LVM", 6, 500), w/2, SH(10), color_white, TEXT_ALIGN_CENTER)
            else
                surface.SetMaterial(LVM_V2:Material("F4/bar.png"))
                surface.SetDrawColor(color_white)
                surface.DrawTexturedRect(0, 0, w, h)
                
                draw.SimpleText(name, LVM_V2.Fonts("LVM", 6, 500), w/2, SH(10), Color(255, 255, 255, 100), TEXT_ALIGN_CENTER)
                
                surface.SetMaterial(LVM_V2:Material("F4/bar_locked.png"))
                surface.SetDrawColor(color_white)
                surface.DrawTexturedRect(0, 0, w, h)
            end
        end
        
        if is_posses and not is_not_speciality then
            
            local nature_tech = {}
            for i = 1, #DLVM.Technics do
                if DLVM.Technics[i].Nature == name then
                    local index = #nature_tech+1
                    nature_tech[index] = DLVM.Technics[i]
                    nature_tech[index].TechID = i;
                end
            end
            
            for i = 1, #nature_tech do
                local dbutton = vgui.Create("DButton", menu)
                dbutton:SetSize(SW(75), SH(75))
                dbutton:SetPos(SW(23) + SW(99*(i-1)), SH(40))
                dbutton:SetText("")
                dbutton.TechID = nature_tech[i].TechID
                dbutton.Paint = function(s, w, h)
                    surface.SetMaterial(LVM_V2:Material("f4/jutsu_slot.png"))
                    surface.SetDrawColor(color_white)
                    surface.DrawTexturedRect(0, 0, w, h)
                    
                    if nature_tech[i].Icon != "" then
                        masks.Start()
                            surface.SetMaterial(util.ImgurToMaterial(nature_tech[i].Icon, "smooth mips"))
                            surface.SetDrawColor((LocalPlayer():HasJutsu(nature_tech[i].TechID) and color_white or Color(100,100,100)))
                            surface.DrawTexturedRect(0, 0, w, h)
                        masks.Source()
                            DrawCircle(SW(38), SH(37), SH(32), 360, nil)
                        masks.End()
                    end
                end
                dbutton.DoClick = function()
                    panel.TechClicked = nature_tech[i].TechID

                    local techid = nature_tech[i].TechID
         
                    local jutsulevel = (LocalPlayer().LVM_Jutsu == nil and 0 or (LocalPlayer().LVM_Jutsu[techid] or 0))
                    
                    selected_id = techid;

                   SetDescription(nature_tech[i].Description, jutsulevel)

                    if input.IsKeyDown(KEY_LSHIFT) then 
                        techid = nature_tech[i].TechID
                        if techid and LocalPlayer():HasJutsu(techid) then
                            for i = 1, 6 do
                                if panel.jutsu_actual[i] == 0 then 
                                    panel.jutsu_actual[i] = techid
                                    DLVM.Binds["Jutsu" .. i]:SetInt(techid)
                                    return
                                end
                            end
                        end
                    end
                end
                if LocalPlayer():HasJutsu(nature_tech[i].TechID) then
                    dbutton:Droppable("abre_jutsu")
                end
                dbutton.OnCursorEntered = function()
                    ShowTooltip(true, nature_tech[i].Name, nature_tech[i].Rang)
                end
                dbutton.OnCursorExited = function()
                    ShowTooltip(false)
                end
            end
            
        end
    end
    
    local function AddNatures(tbl)
        local tbl2 = {}
        for i = 1, #tbl do
            if tbl2[tbl[i]] == nil then
                AddNature(tbl[i])
                tbl2[tbl[i]] = true
            end
        end
    end
    
    AddNatures(natures)

    
    for i = 1, 6 do
    
        local button = vgui.Create("DButton", panel)
        button:SetSize(SW(90), SH(80))
        button:SetPos(SW(180) + SW(170 * (i-1)), SH(640))
        button:SetText("")
        button.Paint = nil;
        button.Tech = panel.jutsu_actual[i]
        button.OnCursorEntered = function(self)
            if DLVM.Technics[self.Tech] == nil then return end
            ShowTooltip(true, DLVM.Technics[self.Tech].Name, DLVM.Technics[self.Tech].Rang)
        end
        button.DoRightClick = function(self)
            panel.jutsu_actual[i] = 0;
            self.Tech = 0
            ShowTooltip(false)
        end
        button.OnCursorExited = function()
            ShowTooltip(false)
        end
        button:Receiver("abre_jutsu", function(self, tbl, dropped, menu, x, y)
            if not dropped then return end
            
            local jutsu = tbl[1]
            if jutsu == nil then return end
            
            if !LocalPlayer():HasJutsu(jutsu.TechID) then return end
            
            panel.jutsu_actual[i] = jutsu.TechID
            self.Tech = jutsu.TechID
        end)
        
        local binder = vgui.Create("DBinder", panel)
        binder:SetSize(SW(25), SH(25))
        binder:SetPos(SW(230) + SW(170 * (i-1)), SH(700))
        binder:SetConVar("kjutsu_"..i)
        binder:SetFontInternal(LVM_V2.Fonts("LVM", 5, 500))
        binder:SetTextColor(color_white)
        binder.Paint = function(self, w, h)
            -- local key = input.GetKeyName(tonumber(self:GetValue()))
            draw.RoundedBox(8, 0, 0, w, h, Color(71, 71, 71))
            -- draw.SimpleText(key, LVM_V2.Fonts("LVM", 5, 500), w/2, h/2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        end
        
    end
    
    local button = vgui.Create("DPanel", panel)
    button:SetSize(SW(296), SH(70))
    button:SetPos(SW(1175), SH(625))
    -- button:SetConVar("kpermutation")
    button:SetText("")
    button.Paint = function(self, w, h)
        draw.RoundedBox(0, 0, 0, w, h, Color(29, 29, 29))
        
        surface.SetMaterial(util.ImgurToMaterial("ReyiIP4.png", "smooth"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(SW(30), SH(15), SW(40), SH(40))
    
        draw.SimpleText("Permutation: ", LVM_V2.Fonts("LVM", 7, 500), SW(130), h / 2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
    
    local dbinder = vgui.Create("DBinder", button)
    dbinder:SetSize(SW(80), SH(40))
    dbinder:SetPos(SW(180), SH(15))
    dbinder:SetConVar("kpermutation")
    dbinder:SetFontInternal(LVM_V2.Fonts("LVM", 7, 500))
    dbinder:SetTextColor(color_white)
    dbinder.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(71, 71, 71, 200))
    end

    local button2 = vgui.Create("DPanel", panel)
    button2:SetSize(SW(296), SH(70))
    button2:SetPos(SW(1175), SH(545))
    button2:SetText("")
    button2.Paint = function(self, w, h)
       draw.RoundedBox(6, 0, 0, w, h, Color(29, 29, 29))
       
       draw.SimpleText("Style de combat: ", LVM_V2.Fonts("LVM", 7, 500), SW(100), h / 2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
       draw.SimpleText("*Le style de combat n'affecte que les animations*", LVM_V2.Fonts("LVM", 4, 500), SW(40), h / 2 + SH(25), Color(200, 200, 200, 30), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
    end
    
    local dstyle = vgui.Create("DButton", button2)
    dstyle:SetSize(SW(80), SH(40))
    dstyle:SetPos(SW(180), SH(15))
    dstyle:SetText("")
    dstyle.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(71, 71, 71, 200))
        draw.SimpleText(DLVM.Binds.FightStyle:GetInt() == 1 and "Bandit" or (DLVM.Binds.FightStyle:GetInt() == 2 and "Conventionnel" or "Technique"), LVM_V2.Fonts("LVM", 5, 500), w/2, h/2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
    dstyle.DoClick = function()
       local menu = DermaMenu(false, button2)
       menu:SetPos(SW(1175), SH(545))
       menu:SetSize(SW(296), SH(70))
       
       local dbutton = menu:AddOption("Bandit", function()
           DLVM.Binds.FightStyle:SetInt(1)
       end):SetIcon("icon16/arrow_right.png")
       local dbutton = menu:AddOption("Conventionnel", function()
           DLVM.Binds.FightStyle:SetInt(2)
       end):SetIcon("icon16/arrow_right.png")
       local dbutton = menu:AddOption("Technique", function()
           DLVM.Binds.FightStyle:SetInt(3)
       end):SetIcon("icon16/arrow_right.png")

       
       menu:Open()
    end
    local function Save()
        for i = 1, 6 do
            DLVM.Binds["Jutsu" .. i]:SetInt(panel.jutsu_actual[i])
            jutsu_old[i] = panel.jutsu_actual[i]
        end

        DLVM:SaveBinds()
    end
    
    button2.OnRemove = Save
end