---------------------------------------
   ---@author: WQ
   ---@time: 06/04/2025 00:29
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------

LVM_V2.Reroll = {};

LVM_V2.Reroll.Config = {
    ["Chances"] = {},
    ["ChancesKeikkei"] = {},
    ["RerollByJob"] = {
		eleve = "base",
		genin1 = "base",
		genin2 = "base",
		chunin1 = "base",
		chunin2 = "base",
		chunin3 = "base",
		grandchunin = "base",
		tokubetsu = "reroll",
		jonin = "reroll",
		commandant = "reroll",
		conseiller = "reroll",
		kage = "gived"
    }
}

function LVM_V2.Reroll:CanByJob(type, job)
    local job = LVM_V2.Reroll.Config.RerollByJob[job]
    
    if job == type then
        return true
    elseif job == "reroll" and type == "base" then
        return true
    elseif job == "gived" and type == "reroll" or type == "base" then
        return true
    end
    return false
end

function LVM_V2.Reroll:SetChance(name, chance)
    self.Config.Chances[name] = chance;
end

function LVM_V2.Reroll:SetChanceKeikkei(name, chance)
    self.Config.ChancesKeikkei[name] = chance;
end

function LVM_V2.Reroll:GetRandom(id)
    math.randomseed(os.time() + os.clock() * 1000000 + math.Rand(0, 1000))
    
    local chances = (id == 1 and self.Config.Chances or self.Config.ChancesKeikkei)

    if id == 2 then
        local total = 0
        for _, v in pairs(chances) do
            total = total + v
        end

        local roll = math.random(1, total)
        local cumul = 0

        for k, v in pairs(chances) do
            cumul = cumul + v
            if roll <= cumul then
                return k
            end
        end
    else
        -- Fonctionnement standard (id == 1) basé sur pourcentages
        local natures = {"Katon", "Doton", "Futon", "Raiton", "Suiton"};
    
        return table.Random(natures);
    end
end


if CLIENT then
    LVM_V2.Reroll:SetChanceKeikkei("none", 20); -- Rien
    
    LVM_V2.Reroll:SetChanceKeikkei("Futton", 1.2*2); -- Epique
    LVM_V2.Reroll:SetChanceKeikkei("Shakuton", 0.7*2); -- Epique
    LVM_V2.Reroll:SetChanceKeikkei("Kiminari", 0.5*2); -- Epique
    
    LVM_V2.Reroll:SetChanceKeikkei("Jiton", 0.35*5); -- Légendaire
    LVM_V2.Reroll:SetChanceKeikkei("Bakuton", 0.3*5); -- Légendaire
    LVM_V2.Reroll:SetChanceKeikkei("Inkuton", 0.25*5); -- Légendaire
    
    LVM_V2.Reroll:SetChanceKeikkei("Hyoton", 0.10*4); -- Mythique
    LVM_V2.Reroll:SetChanceKeikkei("Mokuton", 0.04*3); -- Mythique
end