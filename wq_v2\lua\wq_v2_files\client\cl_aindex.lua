---------------------------------------
   ---@author: WQ
   ---@time: 01/06/2025 19:44
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------


 LVM_V2.Fonts:Add("Albert Sans", 10, 500)
 LVM_V2.Fonts:Add("Albert Sans Bold", 9, 500)
 
 local paints = {
    ["Library"] = function(self, w, h)
        surface.SetMaterial(LVM_V2:Material("learning/frame.png"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(0, 0, w, h)
        
        local pdc = LocalPlayer():GetPDC()
        local cColor = Color(255, 255, 72)

        if pdc < 1 then
            cColor = Color(255, 0, 0)
        end
        
        surface.SetFont("LVM:WQ:REROLL:1:70")
        local iTextWidth, iTextHeight = surface.GetTextSize(pdc)
        local iTextx = SW(50) + iTextWidth + SW(10)
        
        
        draw.SimpleText(LocalPlayer():GetPDC(), "LVM:WQ:REROLL:1:70", SW(50), SH(20), color_white, TEXT_ALIGN_LEFT)
        draw.SimpleText("Point de compétence(s)", "LVM:WQ:REROLL:2:30", iTextx, SH(35), color_white, TEXT_ALIGN_LEFT)
        draw.SimpleText("disponible", "LVM:WQ:REROLL:3:20", iTextx, SH(60), cColor, TEXT_ALIGN_LEFT)
        
        masks.Start()
            surface.SetMaterial(LVM_V2:Material("learning/xp_bar_color.png"))
            surface.SetDrawColor(color_white)
            surface.DrawTexturedRect(SW(1119), SH(41) + SH(1)*0.2, SW(425), SH(26))
        masks.Source()
            draw.RoundedBox(0, SW(1119), SH(41) + SH(1)*0.2, SW(425) * math.Remap(LocalPlayer():GetXP(), LVM_V2.Lvl:GetXPFromLevel(LocalPlayer():GetLvl()), LVM_V2.Lvl:GetXPToNextLevel(LocalPlayer():GetXP()), 0, 1), SH(26), color_white)
        masks.End()
    
        draw.SimpleText("Niveau : "..LocalPlayer():GetLvl(), LVM_V2.Fonts("LVM", 7, 500), SW(1330), SH(45), color_white, TEXT_ALIGN_CENTER)
    end,
    
    ["F4"] = function(self, w, h)
        surface.SetMaterial(LVM_V2:Material(""))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(0, 0, w, h)
        -- draw.SimpleText("F4", LVM_V2.Fonts("Albert Sans Bold", 9, 500), w/2, h/2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end,
    ["Reroll"] = function(self, w, h, index)
        surface.SetMaterial(LVM_V2:Material(""))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(0, 0, w, h)
        
        -- print(index)
        
        local pdc = (index == "Basique" and LocalPlayer().LVM_Boutique.Reroll or LocalPlayer().LVM_Boutique.Reroll_Keikkei)
        local cColor = Color(255, 255, 72)

        if pdc < 1 then
            cColor = Color(255, 0, 0)
        end
        
        surface.SetFont("LVM:WQ:REROLL:1:70")
        local iTextWidth, iTextHeight = surface.GetTextSize(pdc)
        local iTextx = SW(50) + iTextWidth + SW(10)
        
        
        draw.SimpleText(pdc, "LVM:WQ:REROLL:1:70", SW(50), SH(20), color_white, TEXT_ALIGN_LEFT)
        draw.SimpleText("Reroll(s)", "LVM:WQ:REROLL:2:30", iTextx, SH(35), color_white, TEXT_ALIGN_LEFT)
        draw.SimpleText("Disponible", "LVM:WQ:REROLL:3:20", iTextx+SW(2), SH(60), cColor, TEXT_ALIGN_LEFT)
    end
 }

function LVM_V2.Pages:Open(config)

    if LocalPlayer():AdminMode() and not LocalPlayer():IsAnimateur() then return end

    if IsValid(self.Frame) then return end
    
    self.Frame = vgui.Create("DFrame")
    self.Frame:SetSize(SW(1662), SH(931))
    self.Frame:Center()
    self.Frame:SetTitle("")
    self.Frame:ShowCloseButton(false)
    self.Frame:SetAlpha(0)
    self.Frame:AlphaTo(255, 0.3, 0)
   -- self.Frame:SetKeyBoardInputEnabled(true)
--    if config == "F4" then
--         self.Frame:SetMouseInputEnabled(true)
--         gui.EnableScreenClicker(true)
--         self.Frame:MoveToFront()    
--    else
        self.Frame:MakePopup()
        self.Frame:SetKeyboardInputEnabled(false)
 --  end
    self.Frame.Paint = function(self, w, h)
        Derma_DrawBackgroundBlur(self)
        paints[config](self, w, h, LVM_V2.Pages.Index)
    end
    
    local panel;
    local function PANEL(label, paint, after)
        if IsValid(panel) then panel:Remove() end
        
        panel = vgui.Create("DPanel", self.Frame)
        panel:SetSize(SW(1579), SH(840))
        panel:SetPos(SW(41), SH(91))
        panel.Paint = paint;
        
        self.Index = label;
        
        after(panel)
    end
    
    local X = 0;
    local NAVBAR = {}
    local MARGIN_X = SW(8)
    
    for k,v in pairs(LVM_V2.Pages.Config[config]["Navbar"]) do
        
        surface.SetFont(LVM_V2.Fonts("Albert Sans", 10, 500))
        local sw, sh = surface.GetTextSize(k)
        sw = sw + SW(20)
        
        X = X + sw + MARGIN_X;
        
        NAVBAR[#NAVBAR + 1] = { Order = v, Label = k, W = sw, Paint = function(...)
            if LVM_V2.Pages[config][k] and LVM_V2.Pages[config][k].Paint then
                LVM_V2.Pages[config][k].Paint(...)
            end
        end, After = function(...)
            if LVM_V2.Pages[config][k] and LVM_V2.Pages[config][k].After then
                LVM_V2.Pages[config][k].After(...)
            end
        end }
        
    end
    
    table.sort(NAVBAR, function(a, b) return a.Order < b.Order end)
    
    local startPos = SW(LVM_V2.Pages.Config[config]["Pos"]) - X/2
    local N_Pos = 0;
    
    for i = 1, #NAVBAR do
        local beforeN_Size = (NAVBAR[i-1] == nil and 0 or (NAVBAR[i-1].W + MARGIN_X))
        
        local navbar = vgui.Create("DButton", self.Frame)
        navbar:SetSize(NAVBAR[i].W, SH(48))
        navbar:SetPos(startPos + N_Pos + beforeN_Size, SH(36))
        navbar:SetText("")
        navbar.Paint = function(s, w, h)
            if s:IsHovered() or self.Index == NAVBAR[i].Label then
                draw.RoundedBox(32, 0, 0, w, h, Color(38, 38, 38))
            end
            draw.SimpleText(NAVBAR[i].Label, LVM_V2.Fonts("Albert Sans Bold", 9, 500), w/2, h*0.48, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        end
        navbar.DoClick = function()
            PANEL(NAVBAR[i].Label, NAVBAR[i].Paint, NAVBAR[i].After)
        end
        
        N_Pos = N_Pos + (i == 1 and 0 or beforeN_Size)
    end
    
    PANEL(NAVBAR[1].Label, NAVBAR[1].Paint, NAVBAR[1].After)
    
    local remove = vgui.Create("DButton", self.Frame)
    remove:SetSize(SW(36), SH(36))
    remove:SetPos(SW(1584),SH(37))
    remove:SetText("")
    remove.Paint = nil;
    remove.DoClick = function()
        if IsValid(self.Frame) then
            self.Frame:AlphaTo(0, 0.2, 0, function()
                if IsValid(self.Frame) then
                    self.Frame:Remove()
                end
            end)

        end
    end
    
    if config == "Reroll" then
        local buy_reroll = vgui.Create("DButton", self.Frame)
        buy_reroll:SetSize(SW(276), SH(54))
        buy_reroll:SetPos(SW(1280),SH(34))
        buy_reroll:SetText("")
        buy_reroll.Paint = function(self, w, h)
            surface.SetMaterial(LVM_V2:Material("reroll/reroll_buy.png"))
            surface.SetDrawColor((self:IsHovered() and Color(255, 255, 255, 200) or color_white))
            surface.DrawTexturedRect(0, 0, w, h)
        end;
        buy_reroll.DoClick = function()
            gui.OpenURL("http://www.LVMrp.fr/")
        end
    end
    
    -- if config == "F4" then

    --     self.Frame.OnKeyCodePressed = function(self, key)
    --         if key == KEY_F4 then
    --             if IsValid(self) then
    --                 self:AlphaTo(0, 0.2, 0, function()
    --                     if IsValid(self) then
    --                         self:Remove()
    --                     end
    --                 end)
    --             end
    --         end
    --     end
    -- end

  
end

hook.Add("PlayerButtonDown", "LVM_V2.Pages", function(ply, key)
    if ply != LocalPlayer() then return end
    if not IsFirstTimePredicted() then return end
    if key != KEY_F4 then return end
    
    if IsValid(LVM_V2.Pages.Frame) then
        LVM_V2.Pages.Frame:AlphaTo(0, 0.2, 0, function()
            if IsValid(LVM_V2.Pages.Frame) then
                LVM_V2.Pages.Frame:Remove()
            end
        end)
    else
        LVM_V2.Pages:Open("F4")
    end
    

end)

concommand.Add("reroll", function()
    LVM_V2.Pages:Open("Reroll")
    
end)