---------------------------------------
   ---@author: WQ
   ---@time: 03/05/2025 15:41
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------

 LVM_V2.Pages["Library"]["Nature"] = {};

LVM_V2.Pages["Library"]["Nature"].Paint = function(self, w, h)
    surface.SetMaterial(LVM_V2:Material("learning/nature.png"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(w/2 - SW(1472)/2, h/2 - SH(704)/2, SW(1472), SH(704))
    

end






local CIRCLE = {}
local function DrawCircle(xPos, yPos, radius, max, color)

    if CIRCLE[xPos] != nil and istable(CIRCLE[xPos][yPos]) and istable(CIRCLE[xPos][yPos][radius]) then
        draw.NoTexture()
        surface.SetDrawColor((color == nil and color_white or color))
        surface.DrawPoly(CIRCLE[xPos][yPos][radius])
    else
        local segments = {}
    
        for i = 1, max, 3 do
            local x, y = math.cos(-math.rad(90 + i)), math.sin(-math.rad(90 + i))
            
            segments[#segments+1] = { x = xPos + (x *radius), y = yPos - (y * radius) }
        end
        
        segments[#segments+1] = { x = xPos, y = yPos }
        
        -- PrintTable(segments)
        
        draw.NoTexture()
        surface.SetDrawColor((color == nil and color_white or color))
        surface.DrawPoly(segments)
        
        if CIRCLE[xPos] == nil then
            CIRCLE[xPos] = {}
        end
        if CIRCLE[xPos][yPos] == nil then
            CIRCLE[xPos][yPos] = {}
        end
        if CIRCLE[xPos][yPos][radius] == nil then
            CIRCLE[xPos][yPos][radius] = segments
        end
    end

    
end

LVM_V2.Pages.DrawCircle = DrawCircle

local function GetIcon(tech)
    local default = nil

    if tech == nil then
        return default
    else
        if tech.Icon == "" then
            return default
        end
    end
    
    return tech.Icon
end

LVM_V2.Pages.GetIcon = GetIcon

local frame
local jutsuInfo = {} 

local function UpgradeSkill(technic)
    if IsValid(frame) then return end
    
    local jutsu_star = LocalPlayer().LVM_Jutsu
    
    frame = vgui.Create("DFrame")
    frame:SetSize(SW(344), SH(594))
    frame:Center()
    frame:SetTitle("")
    frame:MakePopup()
    frame:ShowCloseButton(false)
    frame:SetDraggable(false)
    frame:RequestFocus()
    frame:MoveToFront()
    frame.Paint = function(self, w, h)
        Derma_DrawBackgroundBlur(self)
        self:MoveToFront()
        surface.SetMaterial(LVM_V2:Material("learning/upgrade_frame.png"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(0, 0, w, h)
        
        masks.Start()
            if technic.Icon != "" then
                surface.SetMaterial(util.ImgurToMaterial(GetIcon(technic), "noclamp smooth"))
            else
                surface.SetMaterial(LVM_V2:Material("learning/no_icon.png", "noclamp smooth"))
            end
            surface.SetDrawColor(255, 255, 255, 255)
            surface.DrawTexturedRect(SW(97), SH(55), SW(150), SH(150))
        masks.Source()
            DrawCircle(SW(172), SH(143), SH(120/2), 360, color_white)
        masks.End()
        
        draw.SimpleText(technic.Name, LVM_V2.Fonts("LVM", 10, 500), SW(30), SH(18), color_white, TEXT_ALIGN_LEFT)
        
        
        
        if jutsu_star != nil then
            if jutsu_star[technic.Id] != nil then
                draw.SimpleText("Niv. "..(jutsu_star[technic.Id] == 5 and "Max" or jutsu_star[technic.Id]), LVM_V2.Fonts("LVM", 5, 500), SW(112), SH(210), color_white, TEXT_ALIGN_LEFT)
                for i = 1, 5 do
                    surface.SetMaterial(LVM_V2:Material("learning/star.png"))
                    surface.SetDrawColor(jutsu_star[technic.Id] >= i and color_white or Color(100, 100, 100, 100))
                    surface.DrawTexturedRect(SW(112) + ((i-1) * SW(25)), SH(230), SW(13), SH(13))
                end 
            end
            
            if not technic.Upgrades then 
                technic.Upgrades = { 1, 2, 3, 4, 5 } 
            end
            if jutsu_star[technic.Id] != nil then
                if tonumber(jutsu_star[technic.Id])+1 <= 5 then
                    draw.SimpleText(technic.Upgrades[tonumber(jutsu_star[technic.Id])+1].." PDC", LVM_V2.Fonts("LVM", 5, 500), SW(312), SH(268), Color(35, 162, 42, 200), TEXT_ALIGN_RIGHT)
                else
                    draw.SimpleText("Technique niveau max", LVM_V2.Fonts("LVM", 5, 500), SW(312), SH(268), Color(35, 162, 42, 200), TEXT_ALIGN_RIGHT)
                end
            end
        end 
        draw.SimpleText(technic.Name, LVM_V2.Fonts("LVM", 5, 500), SW(30), SH(268), Color(130, 130, 130, 200), TEXT_ALIGN_LEFT)
    end
    
    local richtext;

    local function UpdateRichText() 
        richtext = vgui.Create("RichText", frame)
        richtext:SetSize(SW(283), SH(100))
        richtext:SetPos(SW(30), SH(300))
        function richtext:PerformLayout()

            self:SetFontInternal( LVM_V2.Fonts("LVM", 5, 500) )
            
        end
        richtext:InsertColorChange(255, 255, 255, 255)
        richtext:AppendText(technic.Description)

        richtext:AppendText("\n")
        LVM_V2:GetConfigJutsu(technic.Id, function(config)
            for k, v in pairs(config) do
                local color = LVM_V2.Equilibrage.Config["Color"][k] or Color(255, 255, 255)
                local key = LVM_V2.Equilibrage.Config["Key"][k] or k
                
                local actual_value = v[jutsu_star[technic.Id]]
                local next_value = v[math.Clamp(jutsu_star[technic.Id]+1, 1, 5)]
                
                richtext:InsertColorChange(color.r, color.g, color.b, 255)
                richtext:AppendText(key.." : "..actual_value .." → "..next_value)
                
                richtext:InsertColorChange(255, 255, 255, 255)
                richtext:AppendText("\n")
            end
        end)
    end

    UpdateRichText()
        
    local upgrade = vgui.Create("DButton", frame)
    upgrade:SetSize(SW(272), SH(49))
    upgrade:SetPos(SW(34), SH(436))
    upgrade:SetText("")
    upgrade.Paint = nil;
    upgrade.DoClick = function()
        if jutsu_star[technic.Id] == nil or jutsu_star[technic.Id] >= 5 then return end
    
        net.Start("LVM_V2:UpgradeJutsu")
            net.WriteUInt(technic.Id, 8)
        net.SendToServer()

        
        if IsValid(frame) then 
            frame:Remove()
        end
        
        timer.Simple(0.3, function()
            jutsu_star = LocalPlayer().LVM_Jutsu
            

            if IsValid(richtext) then
                richtext:Remove()
            end

            timer.Simple(0.1, function()

     
                UpdateRichText()
            end)

            
        end)
    end
    
    local close = vgui.Create("DButton", frame)
    close:SetSize(SW(272), SH(49))
    close:SetPos(SW(34), SH(514))
    close:SetText("")
    close.Paint = nil;
    close.DoClick = function()
        if IsValid(frame) then 
            frame:Remove()
        end
    end
    
    local close = vgui.Create("DButton", frame)
    close:SetSize(SW(27), SH(27))
    close:SetPos(SW(300), SH(21))
    close:SetText("")
    close.Paint = nil;
    close.DoClick = function()
        if IsValid(frame) then 
            frame:Remove()
        end
    end
end

LVM_V2.Pages.UpgradeSkill = UpgradeSkill

local function UnlockSkill(technic)
    if IsValid(frame) then return end
    frame = vgui.Create("DFrame")
    frame:SetSize(SW(344), SH(594))
    frame:Center()
    frame:SetTitle("")
    frame:MakePopup()
    frame:ShowCloseButton(false)
    frame:SetDraggable(false)
    frame:RequestFocus()
    frame:MoveToFront()
    frame.Paint = function(self, w, h)
        Derma_DrawBackgroundBlur(self)
        self:MoveToFront()
        surface.SetMaterial(LVM_V2:Material("learning/unlock_frame.png"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(0, 0, w, h)
        
        masks.Start()
            if technic.Icon != "" then
                surface.SetMaterial(util.ImgurToMaterial(GetIcon(technic), "noclamp smooth"))
            else
                surface.SetMaterial(LVM_V2:Material("learning/no_icon.png", "noclamp smooth"))
            end
            surface.SetDrawColor(255, 255, 255, 255)
            surface.DrawTexturedRect(SW(97), SH(88), SW(150), SH(150))
        masks.Source()
            DrawCircle(SW(172), SH(163), SH(150/2), 360, color_white)
        masks.End()
        
        draw.SimpleText(technic.Name, LVM_V2.Fonts("LVM", 10, 500), SW(30), SH(18), color_white, TEXT_ALIGN_LEFT)
        
        draw.SimpleText(technic.PDC.." PDC", LVM_V2.Fonts("LVM", 5, 500), SW(312), SH(268), Color(35, 162, 42, 200), TEXT_ALIGN_RIGHT)
        draw.SimpleText(technic.Name, LVM_V2.Fonts("LVM", 5, 500), SW(30), SH(268), Color(130, 130, 130, 200), TEXT_ALIGN_LEFT)
    end
    
    local richtext = vgui.Create("RichText", frame)
    richtext:SetSize(SW(283), SH(100))
    richtext:SetPos(SW(30), SH(300))
    function richtext:PerformLayout()

        self:SetFontInternal( LVM_V2.Fonts("LVM", 5, 500) )

            
    end
    richtext:InsertColorChange(255, 255, 255, 255)
    richtext:AppendText(technic.Description)


    
    if jutsuInfo[technic.Id] != nil then
        for k, v in pairs(jutsuInfo[technic.Id]) do
            local color = LVM_V2.Equilibrage.Config["Color"][k] or Color(255, 255, 255)
            local key = LVM_V2.Equilibrage.Config["Key"][k] or k

            richtext:InsertColorChange(color.r, color.g, color.b, 255)


            richtext:AppendText("\n"..key..": ")

            richtext:InsertColorChange(color_white.r, color_white.g, color_white.b, 255)
            richtext:AppendText(v)

        end
    end
    
    local unlock = vgui.Create("DButton", frame)
    unlock:SetSize(SW(272), SH(49))
    unlock:SetPos(SW(34), SH(436))
    unlock:SetText("")
    unlock.Paint = nil;
    unlock.DoClick = function()
        
        net.Start("LVM_V2:BuyJutsu")
            net.WriteUInt(technic.Id, 8)
        net.SendToServer()
        if IsValid(frame) then 
            frame:Remove()
        end
    end
    
    local close = vgui.Create("DButton", frame)
    close:SetSize(SW(272), SH(49))
    close:SetPos(SW(34), SH(514))
    close:SetText("")
    close.Paint = nil;
    close.DoClick = function()
        if IsValid(frame) then 
            frame:Remove()
        end
    end
    
    local close = vgui.Create("DButton", frame)
    close:SetSize(SW(27), SH(27))
    close:SetPos(SW(300), SH(21))
    close:SetText("")
    close.Paint = nil;
    close.DoClick = function()
        if IsValid(frame) then 
            frame:Remove()
        end
    end
end

LVM_V2.Pages.UnlockSkill = UnlockSkill


local function ResetStat()
    if IsValid(frame) then return end
    frame = vgui.Create("DFrame")
    frame:SetSize(SW(344), SH(594))
    frame:Center()
    frame:SetTitle("")
    frame:MakePopup()
    frame:ShowCloseButton(false)
    frame:SetDraggable(false)
    frame:RequestFocus()
    frame:MoveToFront()
    frame.Paint = function(self, w, h)
        Derma_DrawBackgroundBlur(self)
        self:MoveToFront()
        surface.SetMaterial(LVM_V2:Material("learning/reset_frame.png"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(0, 0, w, h)
        
        draw.SimpleText(LocalPlayer():GetReset(), LVM_V2.Fonts("LVM", 25, 500), w/2, SH(100), Color(199, 225, 66), 1)
    end
    
    local unlock = vgui.Create("DButton", frame)
    unlock:SetSize(SW(275), SH(49))
    unlock:SetPos(SW(34), SH(394))
    unlock:SetText("")
    unlock.Paint = nil;
    unlock.DoClick = function()
        if LocalPlayer():GetReset() <= 0 then return end
        net.Start("LVM:ResetStat")
        net.SendToServer()
        if IsValid(frame) then 
            frame:Remove()
        end
    end
    
    local close = vgui.Create("DButton", frame)
    close:SetSize(SW(272), SH(49))
    close:SetPos(SW(34), SH(470))
    close:SetText("")
    close.Paint = nil
    close.DoClick = function()
        if IsValid(frame) then 
            frame:Remove()
        end
    end
    
    local shop = vgui.Create("DButton", frame)
    shop:SetSize(SW(160), SH(20))
    shop:SetPos(SW(90), SH(540))
    shop:SetText("")
    shop.Paint = nil
    shop.DoClick = function()
        if IsValid(frame) then 
            frame:Remove()
        end
        LVM_V2.Boutique:PurchaseResetStat()
    end
    
    local close = vgui.Create("DButton", frame)
    close:SetSize(SW(27), SH(27))
    close:SetPos(SW(300), SH(21))
    close:SetText("")
    close.Paint = function(self, w, h)
        -- draw.RoundedBox(0, 0, 0, w, h, color_white)
    end;
    close.DoClick = function()
        if IsValid(frame) then 
            frame:Remove()
        end
    end
end

LVM_V2.Pages.ResetStat = ResetStat

LVM_V2.Pages["Library"]["Nature"].After = function(panel)
    
    local button_pos = {
        { x = 100, y = -111 }, -- 1
        
        { x = 135, y = -40 }, -- 2
        
        { x = 150, y = 45 }, -- 3
        
        { x = 110, y = 121 }, -- 4
        
        { x = 45, y = 170 }, -- 5
        
        { x = -45, y = 170 }, -- 6
        
        { x = -110, y = 121 }, -- 7
        
        { x = -150, y = 40 }, -- 8
        
        { x = -135, y = -45 }, -- 9
    
        { x = -100, y = -111 }, -- 10
    }
    
    local tree_pos = {
        ["Futon"] = { x = 210, y = 210 },
        ["Katon"] = { x = 728, y = 210 },
        ["Suiton"] = { x = 1244, y = 210 },
        
        ["Doton"] = { x = 470, y = 600 },
        ["Raiton"] = { x = 985, y = 600 }
    }
    
    local tree_data = {Futon = {}, Katon = {}, Suiton = {}, Doton = {}, Raiton = {}}
    
    for i = 1, #DLVM.Technics do
        if tree_data[DLVM.Technics[i].Nature] != nil then
            local tech = DLVM.Technics[i]
            
            tree_data[tech.Nature][#tree_data[tech.Nature] + 1] = {
                Id = i,
                Name = tech.Name,
                Description = tech.Description,
                Icon = tech.Icon,
                PDC = tech.PDC,
                Rang = tech.Rang,
                Upgrades = tech.Upgrades
            }
        end
    end
    
    local tooltip;
    local function ShowTooltip(bool, name, rang)
        
        if not bool then panel.PaintOver = nil; return end
        
        surface.SetFont(LVM_V2.Fonts("LVM", 9, 500))
        local w, h = surface.GetTextSize(name)
        
        panel.PaintOver = function(self)
            local x, y = panel:LocalCursorPos()
            x = x + SW(10)
        
            draw.RoundedBox(20, x, y, w + SW(20), SH(60), Color(50, 50, 50))
            draw.SimpleText(name, LVM_V2.Fonts("LVM", 9, 500), x + SW(8), y + SH(5), color_white, TEXT_ALIGN_LEFT)
            draw.SimpleText("Rareté:", LVM_V2.Fonts("LVM", 7, 500), x + SW(8), y + SH(30), Color(200, 200, 200), TEXT_ALIGN_LEFT)
            draw.SimpleText(rang, LVM_V2.Fonts("LVM", 7, 500), x + SW(60), y + SH(30), DLVM.RarityColors[rang], TEXT_ALIGN_LEFT)
        end
        
    end
    
    for tree_name,tree in pairs(tree_pos) do
        for i = 1, #button_pos do
            local w, h = SW(50), SH(50)
            
            local technic = tree_data[tree_name][i] == nil and {
                Id = -1,
                Name = "An error occured",
                Description = "Error",
                Icon = "",
                Rang = "?",
                PDC = -1,
                Upgrades = {}
            } or tree_data[tree_name][i]
            
            local is_posses = LocalPlayer():HasJutsu(technic.Id)
            local dbutton = vgui.Create("DButton", panel)
            dbutton:SetSize(w, h)
            dbutton:SetPos(SW(tree.x) + SW(button_pos[i].x) - w/2, SH(tree.y) + SH(button_pos[i].y) - h/2)
            dbutton:SetText("")
            dbutton.Icon = GetIcon(technic)
            dbutton.Paint = function(self, w, h)
            
                is_posses = LocalPlayer():HasJutsu(technic.Id)
            
                DrawCircle(w/2, h/2, h/2, 360, (is_posses and color_white or Color(100, 100, 100)))
            
                local icon = self.Icon
            
                masks.Start()
                    if icon != nil then
                        surface.SetMaterial(util.ImgurToMaterial(icon, "smooth noclamp"))
                    else
                        surface.SetMaterial(LVM_V2:Material("learning/no_icon.png"))
                    end
                    surface.SetDrawColor((is_posses and Color(255,255,255) or Color(100,100,100)))
                    surface.DrawTexturedRect(0, 0, w, h)
                masks.Source()
                    DrawCircle(w/2, h/2, h*0.45, 360, color_white)
                masks.End()
            end
            dbutton.OnCursorEntered = function()
                local x, y = gui.MousePos()
            
                ShowTooltip(true, technic.Name, technic.Rang)
            end
            dbutton.OnCursorExited = function()
            
                ShowTooltip(false)
                
            end
            dbutton.DoClick = function()

                timer.Simple(0.1, function()
                    if is_posses then
                        UpgradeSkill(technic)
                    else
                        UnlockSkill(technic)
                    end
                                        
                end)
           
            end
        end
    end
    
    local reroll = vgui.Create("DButton", panel)
    reroll:SetSize(SW(278), SH(56))
    reroll:SetPos(SW(1228), SH(555))
    reroll:SetText("")
    reroll.Paint = function(self, w, h)
        surface.SetMaterial(LVM_V2:Material("learning/nature_".. (LocalPlayer():GetReroll() >= 1 and 1 or 0) ..".png"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(0, 0, w, h)
    end
    reroll.DoClick = function()
        if IsValid(panel:GetParent()) then
            panel:GetParent():Remove()
            LVM_V2.Boutique:Show()
            LVM_V2.Boutique:SelectIndex("Rerolls")
        end
    end
    
    local reset = vgui.Create("DButton", panel)
    reset:SetSize(SW(278), SH(56))
    reset:SetPos(SW(1228), SH(620))
    reset:SetText("")
    reset.Paint = function(self, w, h)
        -- draw.RoundedBox(0, 0, 0, w, h, color_white)
    end
    reset.DoClick = function()
        ResetStat()
    end
end