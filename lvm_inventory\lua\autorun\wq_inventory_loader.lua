local path = "wq_inventory/"


if CLIENT then
    local files, dir = file.Find(path.."shared/*.lua", "LUA")
    
    for k,v in pairs(files) do
        include(path.."shared/"..v)
    end

    local files, dir = file.Find(path.."client/*.lua", "LUA")
    
    for k,v in pairs(files) do
        include(path.."client/"..v)
    end
    
else
    
    local files, dir = file.Find(path.."shared/*.lua", "LUA")
    
    for k,v in pairs(files) do
        AddCSLuaFile(path.."shared/"..v)
        include(path.."shared/"..v)
        print("WQ_INVENTORY SHARED => "..v)
    end
    
    local files, dir = file.Find(path.."client/*.lua", "LUA")
    
    for k,v in pairs(files) do
        AddCSLuaFile(path.."client/"..v)
        print("WQ_INVENTORY CLIENT => "..v)
    end
    
    local files, dir = file.Find(path.."server/*.lua", "LUA")
    
    for k,v in pairs(files) do
        include(path.."server/"..v)
        print("WQ_INVENTORY SERVER => "..v)
    end

end
