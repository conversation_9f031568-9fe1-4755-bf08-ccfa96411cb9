---------------------------------------
   ---@author: WQ
   ---@time: 31/05/2025 18:24
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------

LVM_V2.Pages["F4"]["Binds"] = {};

DLVM.Binds.AnimationMenu = CreateClientConVar("animmenu", "0", true, true, "")
DLVM.Binds.MenuJutsu = CreateClientConVar("jutsumenu", "95", true, true, "")
DLVM.Binds.Ticket = CreateClientConVar("faireticket", "93", true, true, "")
DLVM.Binds.Admin = CreateClientConVar("adminmenu", "0", true, true, "")
DLVM.Binds.VoiceMode = CreateClientConVar("voicetouche", "18", true, true, "")

LVM_V2.Pages["F4"]["Binds"].Paint = function(self, w, h)
    -- draw.RoundedBoxEx(6, SW(115), SH(57), SW(688), SH(34), Color(29, 29, 29), true, false, false, false)

    draw.SimpleText("Paramètres de touche", LVM_V2.Fonts("LVM", 11, 500), SW(50), SH(63), color_white, TEXT_ALIGN_LEFT)
end

local keys = {
    ["animmenu"] = "Touche pour ouvrir le menu d'animation",
    ["jutsumenu"] = "Touche pour ouvrir le menu de jutsu",
    ["faireticket"] = "Touche pour ouvrir le menu de ticket",
    ["adminmenu"] = "Touche pour ouvrir le menu staff (reservé au staff)",
    ["voicetouche"] = "Touche pour changer de mode de voix",
    ["kChangeDeck"] = "Touche pour changer d'arbre de combat",
    ["kpermutation"] = "Toucher pour faire une permutation"
}

LVM_V2.Pages["F4"]["Binds"].After = function(panel)
    local dscrollpanel = vgui.Create("DScrollPanel", panel)
    dscrollpanel:SetSize(SW(688), SH(500))
    dscrollpanel:SetPos(SW(40), SH(140))

    for k,v in pairs(keys) do
        local dpanel = vgui.Create("DPanel", dscrollpanel)
        dpanel:Dock(TOP)
        dpanel:SetTall(SH(50))
        dpanel:DockMargin(0, 0, 0, SH(10))
        dpanel.Paint = function(self,w, h)
            draw.SimpleText(v, LVM_V2.Fonts("LVM", 8, 500), SW(10), SH(10), color_white, TEXT_ALIGN_LEFT)
            draw.RoundedBox(8, 0, h*0.95, w, h*0.05, Color(71, 71, 71))
        end
        
        local dbinder = vgui.Create("DBinder", dpanel)
        dbinder:SetSize(SW(100), SH(30))
        dbinder:SetPos(SW(570), SH(10))
        dbinder:SetConVar(k)
        dbinder:SetFontInternal(LVM_V2.Fonts("LVM", 6, 500))
        dbinder:SetTextColor(color_white)
        dbinder.Paint = function(self, w, h)
            draw.RoundedBox(4, 0, 0, w, h, (self:IsHovered() and Color(80, 80, 80) or Color(71, 71, 71)))
        end
    end
end