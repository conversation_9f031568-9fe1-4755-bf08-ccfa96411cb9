AddCSLuaFile('cl_init.lua')
AddCSLuaFile('shared.lua')
include('shared.lua')



function ENT:Initialize()

    self:SetModel("models/wissem/solve/solve_naruto_pnj/pnj_mask.mdl")
    self:SetHullType(HULL_HUMAN)
    self:SetHullSizeNormal()
    self:SetNPCState(NPC_STATE_SCRIPT)
    self:SetSolid(SOLID_BBOX)
    self:CapabilitiesAdd(CAP_ANIMATEDFACE)
    self:SetUseType(SIMPLE_USE)
    self:DropToFloor()
    self:DrawShadow(false)
    self:SetMaxYawSpeed(90)

    self:SetCollisionGroup(COLLISION_GROUP_WORLD)

    self:SetMoveType(MOVETYPE_NONE)
    self:SetVelocity(Vector(0, 0, 0))
end


function ENT:OnTakeDamage()
	return false
end

function ENT:Use(activator)
	return false
end

