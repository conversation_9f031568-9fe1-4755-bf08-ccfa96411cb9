---------------------------------------
   ---@author: WQ
   ---@time: 03/05/2025 15:41
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------

 local IS_SPINNING = false;
 local SCROLL_OFFSET = 0;
 local SHOW_LUEURS = false;
 local WIN_PANEL = nil;
 local RANDOM_PADDING = 0;
 local PASS = false;
 local SEC_CD = nil;
 
 LVM_V2.Pages["Reroll"]["Keikkei Genkai"] = {};
 LVM_V2.Pages["Reroll"]["Keikkei Genkai"].Paint = function(self, w, h)
 
     surface.SetMaterial(LVM_V2:Material("reroll/reroll_drop_taux.png"))
     surface.SetDrawColor(color_white)
     surface.DrawTexturedRect(SW(180), SH(490), SW(406), SH(247))
     
 end
 
 local notif
 local function NotifReroll(nature)
     if IsValid(notif) then
         notif:Remove()
         timer.Remove("Notif_Reroll:WQ")
     end
     
     if nature == "none" then return end
 
     notif = vgui.Create("DFrame")
     notif:SetSize(SW(569 * 1.1), SH(85 * 1.1))
     notif:SetPos(SW(650), SH(0))
     notif:SetDraggable(false)
     notif:ShowCloseButton(false  )
     notif:SetTitle("")
     notif:MakePopup()
     notif:SetAlpha(0)
     notif:AlphaTo(255, 0.3, 0)
     notif:MoveTo(SW(650), SH(50), 0.2, 0, 1)
 
     notif.Paint = function(self, w, h)
         surface.SetMaterial(LVM_V2:Material("reroll/reroll_notif_"..nature:lower()..".png"))
         surface.SetDrawColor(color_white)
         surface.DrawTexturedRect(0, 0, w, h)
 
     end
 
     timer.Create("Notif_Reroll:WQ", 2.5, 1, function()
         if not IsValid(notif) then return end
         notif:MoveTo(SW(650), SH(0), 0.3, 0, 1, function()
             notif:Remove()
         end)
     end)
 end
 
 LVM_V2.Pages["Reroll"]["Keikkei Genkai"].After = function(panel)
 
     local dpanels = {}
     local dpanel = vgui.Create("DPanel", panel)
     dpanel:SetSize(SW(1466), SH(447))
     dpanel:SetPos(SW(50), SH(10))
     dpanel.Paint = function(self, w, h)
        surface.SetMaterial(LVM_V2:Material("reroll/reroll_logo_background.png"))
     
         if SHOW_LUEURS then
             if IsValid(WIN_PANEL) then
                 surface.SetMaterial(LVM_V2:Material("reroll/reroll_lueurs_".. WIN_PANEL.Reroll:lower() ..".png"))
             end
         else
             if IS_SPINNING then
                 surface.SetMaterial(LVM_V2:Material("reroll/reroll_logo_background_on_cours.png"))
             else
                 surface.SetMaterial(LVM_V2:Material("reroll/reroll_logo_background.png"))
             end
         end
 
         surface.SetDrawColor(color_white)
         surface.DrawTexturedRect(0, 0, w, h)
         -- draw.RoundedBox(0, w/2, SH(130), SW(1), SH(210), color_white)
     end
     
     local index = 0;
     local dhorizontal = vgui.Create("DHorizontalScroller", dpanel)
     dhorizontal:SetSize(SW(1180), SH(153))
     dhorizontal:SetPos(SW(150), SH(140))
     dhorizontal:SetOverlap( -30 )
     dhorizontal:SetUseLiveDrag( false )
     dhorizontal.Think = function(self)
         if IS_SPINNING then
             local x, y = self:GetCanvas():GetChildPosition( WIN_PANEL )
             x = x - (SW(430) + SW(RANDOM_PADDING))
             
             WIN_PANEL.Win = true;
             
             if SCROLL_OFFSET == 0 then
                 index = 0;
             end
             
             SCROLL_OFFSET = Lerp(FrameTime() * (PASS and 15 or 1.5), SCROLL_OFFSET, x)
             
             self.OffsetX = SCROLL_OFFSET;
             
             self:SetScroll(self.OffsetX)
             
             if self.OffsetX / SW(152) > index then
                 index = index + 1
                 surface.PlaySound("npc/turret_floor/ping.wav")
             end
         end
     end
     
     local function FinishSpinning(WIN_PANEL)
         SHOW_LUEURS = true;
         NotifReroll(WIN_PANEL.Reroll)
     end
     
     for i = 1, 100 do
         dpanels[i] = vgui.Create("DPanel", dhorizontal)
         dpanels[i]:SetSize(SW(151), SH(153))
         dpanels[i].Reroll = LVM_V2.Reroll:GetRandom(2)
         dpanels[i].Win = false;
         dpanels[i].Paint = function(self, w, h)
             surface.SetMaterial(LVM_V2:Material("reroll/".. (self.Reroll == "none" and "reroll_case_none" or self.Reroll:lower()) ..".png"))
             surface.SetDrawColor(color_white)
             surface.DrawTexturedRect(0, 0, w, h)
         end
         dhorizontal:AddPanel(dpanels[i])
     end
     
     local nuage = vgui.Create("DPanel", panel)
     nuage:SetSize(SW(1422), SH(146))
     nuage:SetPos(SW(80), SH(150))
     nuage.Paint = function(self, w, h)
         surface.SetMaterial(LVM_V2:Material("reroll/reroll_nuage.png"))
         surface.SetDrawColor(color_white)
         surface.DrawTexturedRect(0, 0, w, h)
     end
     
     local dscrollpanel = vgui.Create("DScrollPanel", panel)
     dscrollpanel:SetSize(SW(406), SH(190))
     dscrollpanel:SetPos(SW(180), SH(540))
     dscrollpanel:GetVBar():SetWide(0)
     
     for k,v in SortedPairsByValue(LVM_V2.Reroll.Config["ChancesKeikkei"], true) do
         local dpanel = vgui.Create("DPanel", dscrollpanel)
         dpanel:Dock(TOP)
         dpanel:DockMargin(0, 0, 0, SH(8))
         dpanel:SetSize(SW(406), SH(60))
         dpanel.Paint = function(self, w, h)
             surface.SetMaterial(LVM_V2:Material("reroll/".. (k == "none" and "reroll_case_none" or k:lower()) ..".png"))
             surface.SetDrawColor(color_white)
             surface.DrawTexturedRect(SW(5), 0, SW(62), h)
         
             draw.SimpleText((k == "none" and "Rien" or k), LVM_V2.Fonts("Albert Sans Bold", 9, 500), w*0.2, SH(15), color_white, TEXT_ALIGN_LEFT)
         
         
             draw.SimpleText(v.."%", LVM_V2.Fonts("Albert Sans Bold", 9, 500), w*0.98, SH(15), color_white, TEXT_ALIGN_RIGHT)
         end
     end
     
     
     local tick = vgui.Create("DPanel", panel)
     tick:SetSize(SW(1), SH(230))
     tick:SetPos(SW(780), SH(110))
     tick.Paint = function(self, w, h)
         draw.RoundedBox(0, 0, 0, w, h, color_white)
     end
     
     local spin = vgui.Create("DButton", panel)
     spin:SetSize(SW(240), SH(104))
     spin:SetPos(SW(661), SH(340))
     spin:SetText("")
     spin.Paint = function(self, w, h)
         if IS_SPINNING then
             surface.SetMaterial(LVM_V2:Material("reroll/reroll_skip_rerolls.png"))
         else
             surface.SetMaterial(LVM_V2:Material("reroll/reroll_open_rerolls.png"))
         end
         surface.SetDrawColor(color_white)
         surface.DrawTexturedRect(0, 0, w, h)
     end
     spin.DoClick = function()
         if SEC_CD != nil and SEC_CD > CurTime() then return end
     
         if IS_SPINNING then
            PASS = true;
            
            timer.Remove("FinishSpinning:WQ")
            timer.Create("FinishSpinning:WQ", 0.4, 1, function()
                IS_SPINNING = false;
                FinishSpinning(WIN_PANEL)
                PASS = false;
                SEC_CD = CurTime() + 1;
            end)
        else
            if LocalPlayer().LVM_Boutique.Reroll_Keikkei <= 0 then
                notification.AddLegacy("Vous n'avez plus de rerolls", 1, 5)
                return
            end
            
            SEC_CD = CurTime() + 0.5;
            
            net.Start("LVM:Reroll:Start")
                net.WriteUInt(2, 2)
            net.SendToServer()
        
            net.Receive("LVM:Reroll:Spin", function()
                local nature = net.ReadUInt(5)

    
                PASS = false;
                SHOW_LUEURS = false;
                RANDOM_PADDING = math.random(1, 20)
                
                dhorizontal:SetScroll(0)
                
                if IsValid(WIN_PANEL) then
                    WIN_PANEL.Win = false;
                    WIN_PANEL.Reroll = LVM_V2.Reroll:GetRandom(2)
                end
                
                WIN_PANEL = dpanels[math.random(60, #dpanels-10)];
                WIN_PANEL.Reroll = DLVM:GetKeikkeiGenkaiById(nature)
                IS_SPINNING = true;
                SCROLL_OFFSET = 0;
                timer.Create("FinishSpinning:WQ", 5, 1, function()
                    if PASS then return end
                    IS_SPINNING = false;
                    FinishSpinning(WIN_PANEL)
                    SEC_CD = CurTime() + 1;
                end)
            end)
        end
     end
     
 end