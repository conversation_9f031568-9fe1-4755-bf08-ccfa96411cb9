LocalPlayer().WQ_Inventory_tInventory = {}
LocalPlayer().WQ_Inventory_tEquiped = {}


net.Receive("LVM:WQ_Inventory:SyncInventory", function()
    local iAmount = net.ReadUInt(7)
    local tInventory = {}

    for i = 1, iAmount do
        local sItem = net.ReadString()
        local iAmount = net.ReadInt(10)
        local iCharid = net.ReadInt(2)

        table.insert(tInventory, {
            item = sItem,
            iAmount = iAmount,
            iCharid = iCharid,
        })
    end

    local iAmount = net.ReadUInt(7)

    local tEquiped = {}

    for i = 1, iAmount do
        local sItem = net.ReadString()
        local iCharid = net.ReadInt(2)
        local sCategory = net.ReadString()

        table.insert(tEquiped, {
            item = sItem,
            iCharid = iCharid,
            category = sCategory,
        })
    end


    LocalPlayer().WQ_Inventory_tEquiped = tEquiped
    LocalPlayer().WQ_Inventory_tInventory = tInventory

    if IsValid(WQ_Inventory.vFrame) then
        WQ_Inventory.vFrame:Remove()

        WQ_Inventory:fcOpenInventory(true)
    end
end)

net.Receive("LVM:WQ_Inventory:SetModel", function()
    local sModel = net.ReadString()
    local pPlayer = net.ReadPlayer()
    if not IsValid(pPlayer) then return end


    if pPlayer == LocalPlayer() then
        LocalPlayer():SetModel(sModel)
    
        timer.Simple(0.2, function()
            CTG_UpdateHUD()
        end)
    
        if IsValid(LocalPlayer().Kenjutsu) then
            if isfunction(LocalPlayer().Kenjutsu.changeBone) then
                LocalPlayer().Kenjutsu.changeBone(LocalPlayer())
            end
        end
        
        if IsValid(WQ_Inventory.vFrame) then
            print(sModel)
            WQ_Inventory:UpdateModel(sModel)
        end
    end
        
    if isfunction(pPlayer.ForceThinkAttachedEntity) then
        pPlayer:ForceThinkAttachedEntity()
    end
end)

net.Receive("LVM:WQ_Inventory:EquipMask", function()
    local bEquip = net.ReadBool()
    local sClass, pOwner
    if bEquip then
        sClass = net.ReadString()
        pOwner = net.ReadEntity()
    else
        pOwner = net.ReadEntity()
    end

    
    if not IsValid(pOwner) then return end

    if IsValid(pOwner.WQ_Inventory_Mask) then
        pOwner.WQ_Inventory_Mask:Remove()
        pOwner.WQ_Inventory_Mask = nil
    end


    pOwner:SetNWBool("LVM:WQ_Inventory:Mask", false)

    if pOwner == LocalPlayer() then
        timer.Simple(0.2, function()
            CTG_UpdateHUD()
            if IsValid(WQ_Inventory.vFrame) then
                WQ_Inventory:UpdateModel(LocalPlayer():GetModel())
            end
        end)
    end

    if bEquip then

        
        local mask = WQ_Inventory:GetItem(sClass)
        if not mask then return end

        pOwner:SetNWBool("LVM:WQ_Inventory:Mask", true)

        local boneTarget = pOwner:LookupBone("ValveBiped.Bip01_Head1")

        local matrix = pOwner:GetBoneMatrix(boneTarget)
        if not matrix then return end

        local model = ClientsideModel(mask.sModel)
        model:Spawn()
        model:FollowBone(pOwner, boneTarget)
        model:SetLocalPos(mask.vPos)
        model:SetLocalAngles(mask.aAngle)
        --model:SetPredictable(true)
        
        pOwner:DisableDetachEntity(model)

        local sc = pOwner:GetModelScale()

        model:SetModelScale(sc)
            
        pOwner:CallOnRemove("DeleteMask", function()
            if IsValid(model) then
                model:Remove()
            end
        end)

        pOwner.WQ_Inventory_Mask = model
    end
end)


net.Receive("LVM:WQ_Inventory:EquipKenjutsu", function()
    local bEquip = net.ReadBool()
    local sClass, pOwner
    if bEquip then
        sClass = net.ReadString()
        pOwner = net.ReadEntity()
        pos = net.ReadVector()
        aAngle = net.ReadAngle()
    else
        pOwner = net.ReadEntity()
    end

    if IsValid(pOwner.Kenjutsu) then
        pOwner.Kenjutsu:Remove()
        pOwner.Kenjutsu = nil
    end

    --pOwner:SetNWBool("LVM:WQ_Inventory:Mask", false)

    if pOwner == LocalPlayer() then
        timer.Simple(0.2, function()
            CTG_UpdateHUD()
            if IsValid(WQ_Inventory.vFrame) then
                WQ_Inventory:UpdateModel(LocalPlayer():GetModel())
            end
        end)
    end

    if bEquip then
        local mask = WQ_Inventory:GetItem(sClass)
        if not mask then return end

        -- pOwner:SetNWBool("LVM:WQ_Inventory:Mask", true)
        
        if mask.sBone == "" then return end
        if mask.sBone == nil then return end

        if not IsValid(pOwner) then return end
        local boneTarget = pOwner:LookupBone(mask.sBone)
        if boneTarget == false then return end

        local matrix = pOwner:GetBoneMatrix(boneTarget)
        if not matrix then return end
        
        local pos = pos or mask.vPos
        local aAngle = aAngle or mask.aAngle

        local model = ClientsideModel(mask.sModel)
        model:Spawn()
        model:FollowBone(pOwner, boneTarget)
        model:SetLocalPos(pos)
        model:SetLocalAngles(aAngle)
       -- model:SetPredictable(true)
        
        local sc = pOwner:GetModelScale()
        model.changeBone = function(pOwner)
            local boneTarget = pOwner:LookupBone(mask.sBone)
            if boneTarget == false then return end

            local matrix = pOwner:GetBoneMatrix(boneTarget)
            if not matrix then return end
            model:SetParent(nil)
            model:FollowBone(pOwner, boneTarget)
            model:SetLocalPos(pos)
            model:SetLocalAngles(aAngle)
        end
        
        pOwner:DisableDetachEntity(model)

        model:SetModelScale(sc)
        model.sClass = sClass
        pOwner:CallOnRemove("DeleteKenjutsu", function()
            if IsValid(model) then
                model:Remove()
            end
        end)

        pOwner.Kenjutsu = model
    end
end)

net.Receive("LVM:WQ_Inventory:EquipAccesory", function()
    local bEquip = net.ReadBool()
    local sClass, pOwner
    local iNumAcc = tonumber(net.ReadString())
    if bEquip then
        sClass = net.ReadString()
        pOwner = net.ReadEntity()
    else
        pOwner = net.ReadEntity()
    end
    if not IsValid(pOwner) then return end

    if not pOwner.WQ_Inventory_Accesory then
        pOwner.WQ_Inventory_Accesory = {}
    end


    if not pOwner.WQ_Inventory_Accesory[iNumAcc] then
        pOwner.WQ_Inventory_Accesory[iNumAcc] = nil
    end
    if IsValid(pOwner.WQ_Inventory_Accesory[iNumAcc]) then
        pOwner.WQ_Inventory_Accesory[iNumAcc]:Remove()
        pOwner.WQ_Inventory_Accesory[iNumAcc] = nil
    end

    if pOwner == LocalPlayer() then
        timer.Simple(0.2, function()
            CTG_UpdateHUD()
            if IsValid(WQ_Inventory.vFrame) then
                WQ_Inventory:UpdateModel(LocalPlayer():GetModel())
            end
        end)
    end

    if bEquip then

        local mask = WQ_Inventory:GetItem(sClass)
        if not mask then return end

        local boneTarget = pOwner:LookupBone(mask.sBone)
        if not boneTarget then return end

        local matrix = pOwner:GetBoneMatrix(boneTarget)
        if not matrix then return end

        local model = ClientsideModel(mask.sModel)
        model:Spawn()
        model:FollowBone(pOwner, boneTarget)
        model:SetLocalPos(mask.vPos)
        model:SetLocalAngles(mask.aAngle)
        model.NextChangePos = CurTime() + 20
        --model:SetPredictable(true)
        
        local sc = pOwner:GetModelScale() - (mask.iScale  or 0)
        model:SetModelScale(sc)
 
        pOwner:DisableDetachEntity(model)

        pOwner:CallOnRemove("DeleteAccesory", function()
            if IsValid(model) then
                model:Remove()
            end
        end)

        pOwner.WQ_Inventory_Accesory[iNumAcc] = model
    end
end)



local function OpenConfigMasque()
    local pOwner = LocalPlayer()
    if not pOwner:IsSuperAdmin() then return end


    local vFrame = vgui.Create("DFrame")
    vFrame:SetSize(ScrW() * 0.7, ScrH() * 0.7)
    vFrame:Center()
    vFrame:SetTitle("")
    vFrame:ShowCloseButton(false)
    vFrame:MakePopup()
    vFrame.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(49, 49, 49))
        draw.RoundedBox(4, 0, 0, w, h * 0.06, Color(46, 46, 46))
        draw.SimpleText("Mask Creator", LVM_V2.Fonts("LVM", 10, 500), SW(20), SH(7), color_white, TEXT_ALIGN_LEFT)
    end

    local AnglePitch, AngleYaw, AngleRoll, PosX, PosY, PosZ, ModelEntry, Scale;
    local CameraFOV = 70;
    local AngleCamera = Angle(0, 90, 0)


    local closebutton = vgui.Create("DButton", vFrame)
    closebutton:SetSize(SW(50), SH(50))
    closebutton:SetPos(SW(1290), SH(-3))
    closebutton:SetText("")
    closebutton.Paint = function(self, w, h)
        draw.SimpleText("✕", LVM_V2.Fonts("LVM", 10, 500), w / 2, h / 2, color_white, TEXT_ALIGN_CENTER,
            TEXT_ALIGN_CENTER)
    end
    closebutton.DoClick = function()
        if not IsValid(vFrame) then return end
        vFrame:Remove()
    end

    local resetCam = vgui.Create("DButton", vFrame)
    resetCam:SetSize(SW(200), SH(50))
    resetCam:SetPos(SW(500), SH(600))
    resetCam:SetText("")
    resetCam.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(46, 46, 46, (self:IsHovered() and 200 or 255)))
        draw.SimpleText("Reinitialiser la position", LVM_V2.Fonts("LVM", 6, 500), w / 2, h * 0.36, color_white,
            TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        draw.SimpleText("de la camera", LVM_V2.Fonts("LVM", 5, 500), w / 2, h * 0.64, Color(200, 200, 200),
            TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
    resetCam.DoClick = function()
        ModelPlayer:GetEntity():SetPos(Vector(0, 0, 0))
        ModelPlayer:GetEntity():SetAngles(Angle(0, 40, 0))
        ModelPlayer:SetLookAt(Vector(0, 0, 40))
        ModelPlayer:SetFOV(70)
    end

    local dtextentry

    function UpdateModel()
        if IsValid(ModelPlayer) then
            ModelPlayer:Remove()
        end

        local ang = Angle(AnglePitch:GetValue(), AngleYaw:GetValue(), AngleRoll:GetValue())
        local pos = Vector(PosX:GetValue(), PosY:GetValue(), PosZ:GetValue())

        local mask = ModelEntry:GetValue() or "models/models/props/mao/models/m_mask_demon_v_daichi.mdl"

        model = pOwner:GetModel()

        ModelPlayer = vgui.Create("LVM.ModelPanel", vFrame)
        ModelPlayer:SetSize(SW(700), SH(800))
        ModelPlayer:SetPos(SW(-75), SH(-65))
        ModelPlayer:SetModel(model)
        ModelPlayer:SetPlayerChildren(pOwner)

        ModelPlayer:SetAnimated(true)
        ModelPlayer:SetVisible(true)
        ModelPlayer:SetMouseInputEnabled(true)


        ModelPlayer.Think = function()
            if ModelPlayer:IsDown() then
                local x, y = gui.MousePos()

                local ang = ModelPlayer:GetEntity():GetAngles()

                local sin = math.sin(CurTime()) * 90

                local lerp = Lerp(math.Clamp(y / 891, 0, 1), 70, 20)

                ModelPlayer:GetEntity():SetPos(Vector(0, 1.3, 0.2))
                ModelPlayer:GetEntity():SetAngles(Angle(0, x - 90, 0))
                ModelPlayer:SetFOV(lerp)
                ModelPlayer:SetLookAt(Vector(0, 0, 70))

                CameraFOV = lerp;
                AngleCamera = ModelPlayer:GetEntity():GetAngles()
            else
                ModelPlayer:GetEntity():SetPos(Vector(0, 1.3, 0.2))
                ModelPlayer:GetEntity():SetAngles(AngleCamera)
                ModelPlayer:SetFOV(CameraFOV)
                ModelPlayer:SetLookAt(Vector(0, 0, 70))
            end
        end




        local eEnt = ModelPlayer:GetEntity()
        eEnt:SetSequence(eEnt:LookupSequence("detente"))
        ModelPlayer.LayoutEntity = function(eEntity)
            if eEnt:GetCycle() == 1 then eEnt:SetCycle(0) end
            ModelPlayer:RunAnimation()
        end

        local iLastX = 0

        eEnt:SetAngles(Angle(0, 20, 0))


        local boneTarget = eEnt:LookupBone("ValveBiped.Bip01_Head1")

        local matrix = eEnt:GetBoneMatrix(boneTarget)

        eMask = ClientsideModel(mask)
        eMask:SetNoDraw(true)
        eMask:Spawn()
        eMask:FollowBone(eEnt, boneTarget)
        eMask:SetLocalPos(pos)
        eMask:SetLocalAngles(ang)
        eMask:SetPredictable(true)
        eMask:SetRenderMode(RENDERMODE_TRANSALPHA)
        eMask:SetColor(Color(255, 255, 255, 200))

        ModelPlayer:AddChildren(eMask)
    end

    ModelEntry = vgui.Create("DTextEntry", vFrame)
    ModelEntry:SetSize(SW(400), SH(30))
    ModelEntry:SetPos(SW(700), SH(450))
    ModelEntry:SetText("models/models/props/mao/models/m_mask_demon_v_daichi.mdl")
    ModelEntry:SetPlaceholderText("Model du mask")
    ModelEntry:SetTextColor(color_white)
    ModelEntry:SetFont(LVM_V2.Fonts("LVM", 5, 500))
    ModelEntry:SetCursorColor(Color(255, 255, 255, 100))
    ModelEntry:SetDrawLanguageID(false)
    ModelEntry:SetPaintBackground(false)
    ModelEntry.OnEnter = UpdateModel


    AnglePitch = vgui.Create("DNumSlider", vFrame)
    AnglePitch:SetSize(SW(400), SH(30))
    AnglePitch:SetPos(SW(700), SH(100))
    AnglePitch:SetText("Angle Pitch")
    AnglePitch:SetMin(-180)
    AnglePitch:SetMax(180)
    AnglePitch:SetDecimals(0)
    AnglePitch:SetValue(0)

    AnglePitch.OnValueChanged = UpdateModel

    AngleYaw = vgui.Create("DNumSlider", vFrame)
    AngleYaw:SetSize(SW(400), SH(30))
    AngleYaw:SetPos(SW(700), SH(150))
    AngleYaw:SetText("Angle Yaw")
    AngleYaw:SetMin(-180)
    AngleYaw:SetMax(180)
    AngleYaw:SetDecimals(0)
    AngleYaw:SetValue(90)

    AngleYaw.OnValueChanged = UpdateModel

    AngleRoll = vgui.Create("DNumSlider", vFrame)
    AngleRoll:SetSize(SW(400), SH(30))
    AngleRoll:SetPos(SW(700), SH(200))
    AngleRoll:SetText("Angle Roll")
    AngleRoll:SetMin(-180)
    AngleRoll:SetMax(180)
    AngleRoll:SetDecimals(0)
    AngleRoll:SetValue(90)

    AngleRoll.OnValueChanged = UpdateModel


    PosX = vgui.Create("DNumSlider", vFrame)
    PosX:SetSize(SW(400), SH(30))
    PosX:SetPos(SW(700), SH(250))
    PosX:SetText("Pos X")
    PosX:SetMin(-10)
    PosX:SetMax(10)
    PosX:SetDecimals(0)
    PosX:SetValue(2)

    PosX.OnValueChanged = UpdateModel

    PosY = vgui.Create("DNumSlider", vFrame)
    PosY:SetSize(SW(400), SH(30))
    PosY:SetPos(SW(700), SH(300))
    PosY:SetText("Pos Y")
    PosY:SetMin(-10)
    PosY:SetMax(10)
    PosY:SetDecimals(0)
    PosY:SetValue(3)

    PosY.OnValueChanged = UpdateModel

    PosZ = vgui.Create("DNumSlider", vFrame)
    PosZ:SetSize(SW(400), SH(30))
    PosZ:SetPos(SW(700), SH(350))
    PosZ:SetText("Pos Z")
    PosZ:SetMin(-10)
    PosZ:SetMax(10)
    PosZ:SetDecimals(0)
    PosZ:SetValue(0)

    PosZ.OnValueChanged = UpdateModel

    Scale = vgui.Create("DNumSlider", vFrame)
    Scale:SetSize(SW(400), SH(30))
    Scale:SetPos(SW(700), SH(400))
    Scale:SetText("Scale")
    Scale:SetMin(0.1)
    Scale:SetMax(2)
    Scale:SetDecimals(1)
    Scale:SetValue(0.5)

    Scale.OnValueChanged = function(self)
        if IsValid(eMask) then
            local scale = Vector(self:GetValue(), self:GetValue(), self:GetValue())
            local mat = Matrix()
            mat:Scale(scale)
            eMask:EnableMatrix("RenderMultiply", mat)
        end
    end


    local ButtonCopy = vgui.Create("DButton", vFrame)
    ButtonCopy:SetSize(SW(400), SH(100))
    ButtonCopy:SetPos(SW(800), SH(500))
    ButtonCopy:SetText("")
    ButtonCopy.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(46, 46, 46, (self:IsHovered() and 200 or 255)))
        draw.SimpleText("Copier la position et l'angle", LVM_V2.Fonts("LVM", 6, 500), w / 2, h / 2, color_white,
            TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end

    ButtonCopy.DoClick = function()
        local ang = math.Round(AnglePitch:GetValue(), 1)
        local ang2 = math.Round(AngleYaw:GetValue(), 1)
        local ang3 = math.Round(AngleRoll:GetValue(), 1)
        local pos = math.Round(PosX:GetValue(), 1)
        local pos2 = math.Round(PosY:GetValue(), 1)
        local pos3 = math.Round(PosZ:GetValue(), 1)

        local IAngleCopy = "aAngle = Angle(" .. ang .. ", " .. ang2 .. ", " .. ang3 .. "),"
        local IPosCopy = "vPos = Vector(" .. pos .. ", " .. pos2 .. ", " .. pos3 .. "),"

        SetClipboardText(IAngleCopy .. "\n" .. IPosCopy)
    end


    UpdateModel()
end

concommand.Add("LVM_config_masque", function()
    OpenConfigMasque()
end)

-- hook.Add("HUDPaint", "LVMPRIOTIES", function()
--     local ply = LocalPlayer()
--     if ply.Cinematique then return end
-- end)

local function OpenConfigKenjutsu()
    local pOwner = LocalPlayer()
    if not pOwner:IsSuperAdmin() then return end

    local ModelPlayer = nil;
    local bone = "ValveBiped.Bip01_Head1"

    local vFrame = vgui.Create("DFrame")
    vFrame:SetSize(ScrW() * 0.7, ScrH() * 0.7)
    vFrame:Center()
    vFrame:SetTitle("")
    vFrame:ShowCloseButton(false)
    vFrame:MakePopup()
    vFrame.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(49, 49, 49))
        draw.RoundedBox(4, 0, 0, w, h * 0.06, Color(46, 46, 46))
        draw.SimpleText("Kenjutsu Creator", LVM_V2.Fonts("LVM", 10, 500), SW(20), SH(7), color_white, TEXT_ALIGN_LEFT)
    end

    local AnglePitch, AngleYaw, AngleRoll, PosX, PosY, PosZ, ModelEntry;


    local closebutton = vgui.Create("DButton", vFrame)
    closebutton:SetSize(SW(50), SH(50))
    closebutton:SetPos(SW(1290), SH(-3))
    closebutton:SetText("")
    closebutton.Paint = function(self, w, h)
        draw.SimpleText("✕", LVM_V2.Fonts("LVM", 10, 500), w / 2, h / 2, color_white, TEXT_ALIGN_CENTER,
            TEXT_ALIGN_CENTER)
    end
    closebutton.DoClick = function()
        if not IsValid(vFrame) then return end
        vFrame:Remove()
    end

    local resetCam = vgui.Create("DButton", vFrame)
    resetCam:SetSize(SW(200), SH(50))
    resetCam:SetPos(SW(500), SH(600))
    resetCam:SetText("")
    resetCam.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(46, 46, 46, (self:IsHovered() and 200 or 255)))
        draw.SimpleText("Reinitialiser la position", LVM_V2.Fonts("LVM", 6, 500), w / 2, h * 0.36, color_white,
            TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        draw.SimpleText("de la camera", LVM_V2.Fonts("LVM", 5, 500), w / 2, h * 0.64, Color(200, 200, 200),
            TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
    resetCam.DoClick = function()
        ModelPlayer:GetEntity():SetPos(Vector(0, 0, 0))
        ModelPlayer:GetEntity():SetAngles(Angle(0, 40, 0))
        ModelPlayer:SetLookAt(Vector(0, 0, 40))
        ModelPlayer:SetFOV(70)
    end

    local baseAngle = Angle(0, 20, 0)
    function UpdateModelKenjutsu()

        local ang = Angle(AnglePitch:GetValue(), AngleYaw:GetValue(), AngleRoll:GetValue())
        local pos = Vector(PosX:GetValue(), PosY:GetValue(), PosZ:GetValue())

        local mask = ModelEntry:GetValue() or "models/skylyxx/ctg/props/swords/ninja_katana.mdl"

        model = pOwner:GetModel()
        
        local oldSequence = IsValid(ModelPlayer) and ModelPlayer:GetEntity():GetSequence() or "walk_all"
        
        if IsValid(eMask) then
            SafeRemoveEntity(eMask)
        end
        
        if IsValid(ModelPlayer) then
            ModelPlayer:Remove()
        end
        

        ModelPlayer = vgui.Create("LVM.ModelPanel", vFrame)
        ModelPlayer:SetSize(SW(700), SH(800))
        ModelPlayer:SetPos(SW(-75), SH(-65))
        ModelPlayer:SetModel(model)
        ModelPlayer.Bone = bone;

        ModelPlayer:SetAnimated(true)
        ModelPlayer:SetVisible(true)
        ModelPlayer:SetMouseInputEnabled(true)


        local eEnt = ModelPlayer:GetEntity()
        eEnt:SetSequence(oldSequence)
        ModelPlayer.LayoutEntity = function(eEntity)
            if eEnt:GetCycle() == 1 then eEnt:SetCycle(0) end
            ModelPlayer:RunAnimation()
        end

        eEnt:SetAngles(baseAngle)
        
        local boneTarget = eEnt:LookupBone(bone or "ValveBiped.Bip01_Head1")

        local matrix = eEnt:GetBoneMatrix(boneTarget)

        eMask = ClientsideModel(mask)
        eMask:SetNoDraw(true)
        eMask:Spawn()
        eMask:FollowBone(eEnt, boneTarget)
        eMask:SetLocalPos(pos)
        eMask:SetLocalAngles(ang)
        eMask:SetPredictable(true)
        eMask:SetRenderMode(RENDERMODE_TRANSALPHA)
        eMask:SetColor(Color(255, 255, 255, 200))

        ModelPlayer:AddChildren(eMask)


        ModelPlayer.Think = function(self)
            if input.IsKeyDown(KEY_RIGHT) then
                baseAngle = Angle(0, baseAngle.y + 2, 0)
                eEnt:SetAngles(Angle(0, baseAngle.y + 2, 0))
            end

            if input.IsKeyDown(KEY_LEFT) then
                baseAngle = Angle(0, baseAngle.y - 2, 0)
                eEnt:SetAngles(Angle(0, baseAngle.y - 2, 0))
            end
        end
    end

    local comboBox = vgui.Create("DComboBox", vFrame)
    comboBox:SetSize(SW(300), SH(50))
    comboBox:SetPos(SW(700), SH(400))
    comboBox:SetValue("All Bone")

    comboBox.OnSelect = function(_, _, value)
        bone = value
        UpdateModelKenjutsu()
    end

    for i = 1, LocalPlayer():GetBoneCount() do
        comboBox:AddChoice(LocalPlayer():GetBoneName(i))
    end

    ModelEntry = vgui.Create("DTextEntry", vFrame)
    ModelEntry:SetSize(SW(400), SH(30))
    ModelEntry:SetPos(SW(700), SH(450))
    ModelEntry:SetText("models/skylyxx/ctg/props/swords/ninja_katana.mdl")
    ModelEntry:SetPlaceholderText("Model du mask")
    ModelEntry:SetTextColor(color_white)
    ModelEntry:SetFont(LVM_V2.Fonts("LVM", 5, 500))
    ModelEntry:SetCursorColor(Color(255, 255, 255, 100))
    ModelEntry:SetDrawLanguageID(false)
    ModelEntry:SetPaintBackground(false)
    ModelEntry.OnEnter = UpdateModelKenjutsu


    AnglePitch = vgui.Create("DNumSlider", vFrame)
    AnglePitch:SetSize(SW(400), SH(30))
    AnglePitch:SetPos(SW(700), SH(100))
    AnglePitch:SetText("Angle Pitch")
    AnglePitch:SetMin(-180)
    AnglePitch:SetMax(180)
    AnglePitch:SetDecimals(0)
    AnglePitch:SetValue(0)

    AnglePitch.OnValueChanged = UpdateModelKenjutsu

    AngleYaw = vgui.Create("DNumSlider", vFrame)
    AngleYaw:SetSize(SW(400), SH(30))
    AngleYaw:SetPos(SW(700), SH(150))
    AngleYaw:SetText("Angle Yaw")
    AngleYaw:SetMin(-180)
    AngleYaw:SetMax(180)
    AngleYaw:SetDecimals(0)
    AngleYaw:SetValue(0)

    AngleYaw.OnValueChanged = UpdateModelKenjutsu

    AngleRoll = vgui.Create("DNumSlider", vFrame)
    AngleRoll:SetSize(SW(400), SH(30))
    AngleRoll:SetPos(SW(700), SH(200))
    AngleRoll:SetText("Angle Roll")
    AngleRoll:SetMin(-180)
    AngleRoll:SetMax(180)
    AngleRoll:SetDecimals(0)
    AngleRoll:SetValue(0)

    AngleRoll.OnValueChanged = UpdateModelKenjutsu


    PosX = vgui.Create("DNumSlider", vFrame)
    PosX:SetSize(SW(400), SH(30))
    PosX:SetPos(SW(700), SH(250))
    PosX:SetText("Pos X")
    PosX:SetMin(-100)
    PosX:SetMax(100)
    PosX:SetDecimals(0)
    PosX:SetValue(0)

    PosX.OnValueChanged = UpdateModelKenjutsu

    PosY = vgui.Create("DNumSlider", vFrame)
    PosY:SetSize(SW(400), SH(30))
    PosY:SetPos(SW(700), SH(300))
    PosY:SetText("Pos Y")
    PosY:SetMin(-100)
    PosY:SetMax(100)
    PosY:SetDecimals(0)
    PosY:SetValue(0)

    PosY.OnValueChanged = UpdateModelKenjutsu

    PosZ = vgui.Create("DNumSlider", vFrame)
    PosZ:SetSize(SW(400), SH(30))
    PosZ:SetPos(SW(700), SH(350))
    PosZ:SetText("Pos Z")
    PosZ:SetMin(-100)
    PosZ:SetMax(100)
    PosZ:SetDecimals(0)
    PosZ:SetValue(0)

    PosZ.OnValueChanged = UpdateModelKenjutsu


    local ButtonAnim = vgui.Create("DButton", vFrame)
    ButtonAnim:SetSize(SW(400), SH(100))
    ButtonAnim:SetPos(SW(800), SH(600))
    ButtonAnim:SetText("")
    ButtonAnim.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(46, 46, 46, (self:IsHovered() and 200 or 255)))
        draw.SimpleText("Animer le joueur", LVM_V2.Fonts("LVM", 6, 500), w / 2, h / 2, color_white,
            TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end

    ButtonAnim.DoClick = function()
        if IsValid(ModelPlayer) then
            if ModelPlayer:GetEntity():GetSequence() == ModelPlayer:GetEntity():LookupSequence("oldjimmy_tanjiro_a_p0001_v00_c00_basecharge01_1") then
                ModelPlayer:GetEntity():SetSequence(ModelPlayer:GetEntity():LookupSequence("idle_all_01"))
            else
                ModelPlayer:GetEntity():SetSequence(ModelPlayer:GetEntity():LookupSequence("oldjimmy_tanjiro_a_p0001_v00_c00_basecharge01_1"))
            end
        end
    end

    local ButtonCopy = vgui.Create("DButton", vFrame)
    ButtonCopy:SetSize(SW(400), SH(100))
    ButtonCopy:SetPos(SW(800), SH(500))
    ButtonCopy:SetText("")
    ButtonCopy.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(46, 46, 46, (self:IsHovered() and 200 or 255)))
        draw.SimpleText("Copier la position et l'angle", LVM_V2.Fonts("LVM", 6, 500), w / 2, h / 2, color_white,
            TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end

    ButtonCopy.DoClick = function()
        local ang = math.Round(AnglePitch:GetValue(), 1)
        local ang2 = math.Round(AngleYaw:GetValue(), 1)
        local ang3 = math.Round(AngleRoll:GetValue(), 1)
        local pos = math.Round(PosX:GetValue(), 1)
        local pos2 = math.Round(PosY:GetValue(), 1)
        local pos3 = math.Round(PosZ:GetValue(), 1)

        local IAngleCopy = "aAngle = Angle(" .. ang .. ", " .. ang2 .. ", " .. ang3 .. "),"
        local IPosCopy = "vPos = Vector(" .. pos .. ", " .. pos2 .. ", " .. pos3 .. "),"
        local IBone = 'sBone = "' .. bone .. '"'

        SetClipboardText(IAngleCopy .. "\n" .. IPosCopy .. "\n" .. IBone)
    end

    UpdateModelKenjutsu()
end

concommand.Add("LVM_config_kenjutsu", function()
    OpenConfigKenjutsu()
end)


local function OpenConfigAcc()
    local pOwner = LocalPlayer()
    if not pOwner:IsSuperAdmin() then return end


    local vFrame = vgui.Create("DFrame")
    vFrame:SetSize(ScrW() * 0.7, ScrH() * 0.7)
    vFrame:Center()
    vFrame:SetTitle("")
    vFrame:ShowCloseButton(false)
    vFrame:MakePopup()
    vFrame.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(49, 49, 49))
        draw.RoundedBox(4, 0, 0, w, h * 0.06, Color(46, 46, 46))
        draw.SimpleText("Acc Creator", LVM_V2.Fonts("LVM", 10, 500), SW(20), SH(7), color_white, TEXT_ALIGN_LEFT)
    end

    local AnglePitch, AngleYaw, AngleRoll, PosX, PosY, PosZ, ModelEntry, Scale, Bone;
    local CameraFOV = 70;
    local AngleCamera = Angle(0, 90, 0)


    local closebutton = vgui.Create("DButton", vFrame)
    closebutton:SetSize(SW(50), SH(50))
    closebutton:SetPos(SW(1290), SH(-3))
    closebutton:SetText("")
    closebutton.Paint = function(self, w, h)
        draw.SimpleText("✕", LVM_V2.Fonts("LVM", 10, 500), w / 2, h / 2, color_white, TEXT_ALIGN_CENTER,
            TEXT_ALIGN_CENTER)
    end
    closebutton.DoClick = function()
        if not IsValid(vFrame) then return end
        vFrame:Remove()
    end

    local resetCam = vgui.Create("DButton", vFrame)
    resetCam:SetSize(SW(200), SH(50))
    resetCam:SetPos(SW(500), SH(600))
    resetCam:SetText("")
    resetCam.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(46, 46, 46, (self:IsHovered() and 200 or 255)))
        draw.SimpleText("Reinitialiser la position", LVM_V2.Fonts("LVM", 6, 500), w / 2, h * 0.36, color_white,
            TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        draw.SimpleText("de la camera", LVM_V2.Fonts("LVM", 5, 500), w / 2, h * 0.64, Color(200, 200, 200),
            TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
    resetCam.DoClick = function()
        ModelPlayer:GetEntity():SetPos(Vector(0, 0, 0))
        ModelPlayer:GetEntity():SetAngles(Angle(0, 40, 0))
        ModelPlayer:SetLookAt(Vector(0, 0, 40))
        ModelPlayer:SetFOV(70)
    end


    local comboBox = vgui.Create("DComboBox", vFrame)
    comboBox:SetSize(SW(150), SH(100))
    comboBox:SetPos(SW(700), SH(500))
    comboBox:SetValue("All Bone")

    comboBox.OnSelect = function(_, _, value)
        Bone = value
        UpdateModel()
    end

    for i = 1, LocalPlayer():GetBoneCount() - 1 do
        comboBox:AddChoice(LocalPlayer():GetBoneName(i))
    end

    local dtextentry

    Bone = "ValveBiped.Bip01_Head1"

    function UpdateModel()
        if IsValid(ModelPlayer) then
            ModelPlayer:Remove()
        end

        local ang = Angle(AnglePitch:GetValue(), AngleYaw:GetValue(), AngleRoll:GetValue())
        local pos = Vector(PosX:GetValue(), PosY:GetValue(), PosZ:GetValue())

        local mask = ModelEntry:GetValue() or "models/naruto_solve/give_lda/kankuro_dos.mdl"

        model = pOwner:GetModel()

        ModelPlayer = vgui.Create("LVM.ModelPanel", vFrame)
        ModelPlayer:SetSize(SW(700), SH(800))
        ModelPlayer:SetPos(SW(-75), SH(-65))
        ModelPlayer:SetModel(model)
        ModelPlayer:SetPlayerChildren(pOwner)

        ModelPlayer:SetAnimated(true)
        ModelPlayer:SetVisible(true)
        ModelPlayer:SetMouseInputEnabled(true)


        ModelPlayer.Think = function()
            if ModelPlayer:IsDown() then
                local x, y = gui.MousePos()

                local ang = ModelPlayer:GetEntity():GetAngles()

                local sin = math.sin(CurTime()) * 90

                local lerp = Lerp(math.Clamp(y / 891, 0, 1), 70, 20)

                ModelPlayer:GetEntity():SetPos(Vector(0, 1.3, 0.2))
                ModelPlayer:GetEntity():SetAngles(Angle(0, x - 90, 0))
                ModelPlayer:SetFOV(lerp)
                ModelPlayer:SetLookAt(Vector(0, 0, 70))

                CameraFOV = lerp;
                AngleCamera = ModelPlayer:GetEntity():GetAngles()
            else
                ModelPlayer:GetEntity():SetPos(Vector(0, 1.3, 0.2))
                ModelPlayer:GetEntity():SetAngles(AngleCamera)
                ModelPlayer:SetFOV(CameraFOV)
                ModelPlayer:SetLookAt(Vector(0, 0, 70))
            end
        end




        local eEnt = ModelPlayer:GetEntity()
        eEnt:SetSequence(eEnt:LookupSequence("detente"))
        ModelPlayer.LayoutEntity = function(eEntity)
            if eEnt:GetCycle() == 1 then eEnt:SetCycle(0) end
            ModelPlayer:RunAnimation()
        end

        local iLastX = 0

        eEnt:SetAngles(Angle(0, 20, 0))


        local boneTarget = eEnt:LookupBone(Bone or "ValveBiped.Bip01_Head1")

        local matrix = eEnt:GetBoneMatrix(boneTarget)

        eMask = ClientsideModel(mask)
        eMask:SetNoDraw(true)
        eMask:Spawn()
        eMask:FollowBone(eEnt, boneTarget)
        eMask:SetLocalPos(pos)
        eMask:SetLocalAngles(ang)
        eMask:SetPredictable(true)
        eMask:SetRenderMode(RENDERMODE_TRANSALPHA)
        eMask:SetColor(Color(255, 255, 255, 200))

        ModelPlayer:AddChildren(eMask)

        
        local scale = Vector(Scale:GetValue(), Scale:GetValue(), Scale:GetValue())
        local mat = Matrix()
        mat:Scale(scale)
        eMask:EnableMatrix("RenderMultiply", mat)
    end

    ModelEntry = vgui.Create("DTextEntry", vFrame)
    ModelEntry:SetSize(SW(400), SH(30))
    ModelEntry:SetPos(SW(700), SH(450))
    ModelEntry:SetText("models/naruto_solve/give_lda/kankuro_dos.mdl")
    ModelEntry:SetPlaceholderText("Model du mask")
    ModelEntry:SetTextColor(color_white)
    ModelEntry:SetFont(LVM_V2.Fonts("LVM", 5, 500))
    ModelEntry:SetCursorColor(Color(255, 255, 255, 100))
    ModelEntry:SetDrawLanguageID(false)
    ModelEntry:SetPaintBackground(false)
    ModelEntry.OnEnter = UpdateModel


    AnglePitch = vgui.Create("DNumSlider", vFrame)
    AnglePitch:SetSize(SW(400), SH(30))
    AnglePitch:SetPos(SW(700), SH(100))
    AnglePitch:SetText("Angle Pitch")
    AnglePitch:SetMin(-180)
    AnglePitch:SetMax(180)
    AnglePitch:SetDecimals(0)
    AnglePitch:SetValue(0)

    AnglePitch.OnValueChanged = UpdateModel

    AngleYaw = vgui.Create("DNumSlider", vFrame)
    AngleYaw:SetSize(SW(400), SH(30))
    AngleYaw:SetPos(SW(700), SH(150))
    AngleYaw:SetText("Angle Yaw")
    AngleYaw:SetMin(-180)
    AngleYaw:SetMax(180)
    AngleYaw:SetDecimals(0)
    AngleYaw:SetValue(90)

    AngleYaw.OnValueChanged = UpdateModel

    AngleRoll = vgui.Create("DNumSlider", vFrame)
    AngleRoll:SetSize(SW(400), SH(30))
    AngleRoll:SetPos(SW(700), SH(200))
    AngleRoll:SetText("Angle Roll")
    AngleRoll:SetMin(-180)
    AngleRoll:SetMax(180)
    AngleRoll:SetDecimals(0)
    AngleRoll:SetValue(90)

    AngleRoll.OnValueChanged = UpdateModel


    PosX = vgui.Create("DNumSlider", vFrame)
    PosX:SetSize(SW(400), SH(30))
    PosX:SetPos(SW(700), SH(250))
    PosX:SetText("Pos X")
    PosX:SetMin(-50)
    PosX:SetMax(50)
    PosX:SetDecimals(0)
    PosX:SetValue(2)

    PosX.OnValueChanged = UpdateModel

    PosY = vgui.Create("DNumSlider", vFrame)
    PosY:SetSize(SW(400), SH(30))
    PosY:SetPos(SW(700), SH(300))
    PosY:SetText("Pos Y")
    PosY:SetMin(-50)
    PosY:SetMax(50)
    PosY:SetDecimals(0)
    PosY:SetValue(3)

    PosY.OnValueChanged = UpdateModel

    PosZ = vgui.Create("DNumSlider", vFrame)
    PosZ:SetSize(SW(400), SH(30))
    PosZ:SetPos(SW(700), SH(350))
    PosZ:SetText("Pos Z")
    PosZ:SetMin(-50)
    PosZ:SetMax(50)
    PosZ:SetDecimals(0)
    PosZ:SetValue(0)

    PosZ.OnValueChanged = UpdateModel


    local ButtonCopy = vgui.Create("DButton", vFrame)
    ButtonCopy:SetSize(SW(400), SH(100))
    ButtonCopy:SetPos(SW(800), SH(500))
    ButtonCopy:SetText("")
    ButtonCopy.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(46, 46, 46, (self:IsHovered() and 200 or 255)))
        draw.SimpleText("Copier la position et l'angle", LVM_V2.Fonts("LVM", 6, 500), w / 2, h / 2, color_white,
            TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end

    ButtonCopy.DoClick = function()
        local ang = math.Round(AnglePitch:GetValue(), 1)
        local ang2 = math.Round(AngleYaw:GetValue(), 1)
        local ang3 = math.Round(AngleRoll:GetValue(), 1)
        local pos = math.Round(PosX:GetValue(), 1)
        local pos2 = math.Round(PosY:GetValue(), 1)
        local pos3 = math.Round(PosZ:GetValue(), 1)

        local IAngleCopy = "aAngle = Angle(" .. ang .. ", " .. ang2 .. ", " .. ang3 .. "),"
        local IPosCopy = "vPos = Vector(" .. pos .. ", " .. pos2 .. ", " .. pos3 .. "),"
        local iBone = 'sBone = "' .. Bone .. '"'
        local iScaleReductor = 'iScaleReductor = ' .. Scale:GetValue()

        SetClipboardText(IAngleCopy .. "\n" .. IPosCopy .. "\n" .. iBone .. "\n" .. iScaleReductor)
    end


    UpdateModel()
end

concommand.Add("LVM_config_acc", function()
    OpenConfigAcc()
end)

WQCam = { 
    enabled = false,
    offset = Vector(0, 0, 60),
    ang = Angle(20, 0, 0),
    zoom = 100
}

local cameraframe

function OpenWQCameraEditor()
    local frame = vgui.Create("DFrame")
    frame:SetTitle("Éditeur de Caméra")
    frame:SetSize(SW(500), SH(360))
    frame:SetPos(SW(1200), SH(400)) 
    frame:MakePopup()
    frame:ShowCloseButton(false)
    cameraframe = frame
    LocalPlayer().OnEditMode = true 

    frame.Paint = function(self, w, h)
        draw.RoundedBox(8, 0, 0, w, h, Color(25, 25, 25, 230))
    end

    local function CreateSectionedPanel(title, posY, height)
        local panel = vgui.Create("DPanel", frame)
        panel:SetPos(15, posY)
        panel:SetSize(470, height)
        panel.Paint = function(self, w, h)
            draw.RoundedBox(8, 0, 0, w, h, Color(35, 35, 35, 230))
            draw.SimpleText(title, "DermaDefaultBold", 10, 5, color_white)
        end
        return panel
    end

    local function CreateSlider(parent, label, min, max, val, posY, onChange)
        local slider = vgui.Create("DNumSlider", parent)
        slider:SetPos(10, posY)
        slider:SetSize(450, 25)
        slider:SetText(label)
        slider:SetMin(min)
        slider:SetMax(max)
        slider:SetDecimals(2)
        slider:SetValue(val)
        slider.OnValueChanged = onChange
        slider.Label:SetTextColor(Color(255, 255, 255))
        return slider
    end

    local posPanel = CreateSectionedPanel("Position Caméra", 40, 100)
    CreateSlider(posPanel, "X (Avant)", -200, 200, WQCam.offset.x, 25, function(s) WQCam.offset.x = s:GetValue() end)
    CreateSlider(posPanel, "Y (Côté)", -200, 200, WQCam.offset.y, 50, function(s) WQCam.offset.y = s:GetValue() end)
    CreateSlider(posPanel, "Z (Haut)", -200, 200, WQCam.offset.z, 75, function(s) WQCam.offset.z = s:GetValue() end)

    local angPanel = CreateSectionedPanel("Angle Caméra", 150, 90)
    CreateSlider(angPanel, "Pitch (Inclinaison)", -89, 89, WQCam.ang.p, 25, function(s) WQCam.ang.p = s:GetValue() end)
    CreateSlider(angPanel, "Yaw (Rotation)", -180, 180, WQCam.ang.y, 55, function(s) WQCam.ang.y = s:GetValue() end)

    local zoomPanel = CreateSectionedPanel("Zoom", 250, 50)
    CreateSlider(zoomPanel, "Zoom", 10, 500, WQCam.zoom, 25, function(s) WQCam.zoom = s:GetValue() end)

    frame.OnClose = function() LocalPlayer().OnEditMode = false end

    local resetcam = vgui.Create("DButton", frame)
    resetcam:SetText("")
    resetcam:SetSize(150, 30)
    resetcam:SetPos(20, 310)
    resetcam.DoClick = function()
        WQCam.offset = Vector(0, 0, 60)
        WQCam.ang = Angle(20, 0, 0)
        WQCam.zoom = 100
    end
    resetcam.Paint = function(self, w, h)
        draw.RoundedBox(8, 0, 0, w, h, Color(50, 50, 50))
        draw.SimpleText("Réinitialiser", "DermaDefaultBold", w / 2, h / 2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
end


function WQ_Inventory:OpenModifyWeaponMenu(item)
    local sModel = item.sModel
    local bone_ent = item.sBone or "ValveBiped.Bip01_Head1"
    local bone_index = LocalPlayer():LookupBone(bone_ent)
    if not bone_index then return end

    OpenWQCameraEditor()

    local defaultPos = item.vPos or Vector(0, 0, 0)
    local defaultAng = item.aAngle or Angle(0, 0, 0)
    local pos = Vector(defaultPos)
    local ang = Angle(defaultAng)

    local eModel = ClientsideModel(sModel)
    if not IsValid(eModel) then return end
    eModel:SetNoDraw(false)
    eModel:Spawn()
    eModel:FollowBone(LocalPlayer(), bone_index)
    eModel:SetLocalPos(defaultPos)
    eModel:SetLocalAngles(defaultAng)

    local frame = vgui.Create("DFrame")
    frame:SetTitle("Modification de l'Épée")
    frame:SetSize(SW(500), SH(360))
    frame:SetPos(SW(150), SH(400))
    frame:MakePopup()
    frame:ShowCloseButton(false)
    frame:SetDraggable(true)
    frame.Paint = function(self, w, h)
        draw.RoundedBox(8, 0, 0, w, h, Color(20, 20, 20, 200))
    end

    local sliders = {}
    local function CreateSlider(parent, label, min, max, val, posY, callback)
        local slider = vgui.Create("DNumSlider", parent)
        slider:SetPos(10, posY)
        slider:SetSize(450, 25)
        slider:SetText(label)
        slider:SetMin(min)
        slider:SetMax(max)
        slider:SetDecimals(2)
        slider:SetValue(val)
        slider.OnValueChanged = callback
        slider.Label:SetTextColor(Color(255, 255, 255))
        return slider
    end

    local posPanel = vgui.Create("DPanel", frame)
    posPanel:SetPos(15, 40)
    posPanel:SetSize(470, 100)
    posPanel.Paint = function(self, w, h)
        draw.RoundedBox(8, 0, 0, w, h, Color(35, 35, 35, 220))
        draw.SimpleText("Position de l'Arme", "DermaDefaultBold", 10, 5, color_white)
    end

    sliders.posX = CreateSlider(posPanel, "X (Haut Bas)", -50, 50, pos.x, 25, function(self) pos.x = self:GetValue(); eModel:SetLocalPos(pos) end)
    sliders.posY = CreateSlider(posPanel, "Y (Avant)", -50, 50, pos.y, 50, function(self) pos.y = self:GetValue(); eModel:SetLocalPos(pos) end)
    sliders.posZ = CreateSlider(posPanel, "Z (Coter)", -50, 50, pos.z, 75, function(self) pos.z = self:GetValue(); eModel:SetLocalPos(pos) end)

    local angPanel = vgui.Create("DPanel", frame)
    angPanel:SetPos(15, 150)
    angPanel:SetSize(470, 100)
    angPanel.Paint = function(self, w, h)
        draw.RoundedBox(8, 0, 0, w, h, Color(35, 35, 35, 220))
        draw.SimpleText("Orientation", "DermaDefaultBold", 10, 5, color_white)
    end

    sliders.angP = CreateSlider(angPanel, "Pitch (Inclinaison)", -180, 180, ang.p, 25, function(self) ang.p = self:GetValue(); eModel:SetLocalAngles(Angle(ang.p, ang.y, ang.r)) end)
    sliders.angY = CreateSlider(angPanel, "Yaw (Rotation)", -180, 180, ang.y, 50, function(self) ang.y = self:GetValue(); eModel:SetLocalAngles(Angle(ang.p, ang.y, ang.r)) end)
    sliders.angR = CreateSlider(angPanel, "Roll (Basculement)", -180, 180, ang.r, 75, function(self) ang.r = self:GetValue(); eModel:SetLocalAngles(Angle(ang.p, ang.y, ang.r)) end)

    local function StyleButton(btn, baseColor, hoverColor, text)
        btn:SetText("") 
        btn.Paint = function(self, w, h)
            local clr = self:IsHovered() and hoverColor or baseColor
            draw.RoundedBox(8, 0, 0, w, h, clr)
            draw.SimpleText(text, "DermaDefaultBold", w / 2, h / 2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        end
    end

    local resetBtn = vgui.Create("DButton", frame)
    resetBtn:SetSize(150, 30)
    resetBtn:SetPos(20, 290)
    resetBtn.DoClick = function()
        pos = Vector(defaultPos)
        ang = Angle(defaultAng)
        eModel:SetLocalPos(pos)
        eModel:SetLocalAngles(ang)
        sliders.posX:SetValue(pos.x)
        sliders.posY:SetValue(pos.y)
        sliders.posZ:SetValue(pos.z)
        sliders.angP:SetValue(ang.p)
        sliders.angY:SetValue(ang.y)
        sliders.angR:SetValue(ang.r)
    end
    StyleButton(resetBtn, Color(50, 50, 50), Color(100, 100, 255), "Réinitialiser")

    local cancelBtn = vgui.Create("DButton", frame)
    cancelBtn:SetSize(150, 30)
    cancelBtn:SetPos(180, 290)
    cancelBtn.DoClick = function()
        if IsValid(eModel) then eModel:Remove() end
        frame:Close()
        cameraframe:Close()
        notification.AddLegacy("Modifications annulées !", NOTIFY_GENERIC, 5)

        timer.Simple(0.1, function()
         WQ_Inventory:fcOpenInventory()
        end)

    end
    StyleButton(cancelBtn, Color(50, 50, 50), Color(255, 100, 100), "Annuler")

    local saveBtn = vgui.Create("DButton", frame)
    saveBtn:SetSize(130, 30)
    saveBtn:SetPos(340, 290)
    saveBtn.DoClick = function()
        if not IsValid(eModel) then return end
        eModel:Remove()
        frame:Close()
        cameraframe:Close()
        notification.AddLegacy("Modifications sauvegardées !", NOTIFY_GENERIC, 5)
        net.Start("WQ_Inventory:SaveWeaponModification")
        net.WriteVector(pos)
        net.WriteAngle(ang)
        net.WriteString(item.sClassName)
        net.SendToServer()

        timer.Simple(0.1, function()
         WQ_Inventory:fcOpenInventory()
        end)
    end
    StyleButton(saveBtn, Color(50, 50, 50), Color(100, 255, 100), "Sauvegarder")
end

// Fin de fichier - fermeture propre