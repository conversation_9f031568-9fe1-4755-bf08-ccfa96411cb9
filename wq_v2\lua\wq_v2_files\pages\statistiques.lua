---------------------------------------
   ---@author: WQ
   ---@time: 03/05/2025 15:41
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------
 
LVM_V2.Pages["F4"]["Statistiques"] = {};

for i = 1, 100 do 
    surface.CreateFont(("LVM:WQ:REROLL:1:%i"):format(i), {
        font = "Creato Display Black",
        extended = false,
        weight = 900,
        size = i,
    })    

    surface.CreateFont(("LVM:WQ:REROLL:2:%i"):format(i), {
        font = "Creato Display Medium",
        extended = false,
        weight = 600,
        size = i,
    })  

    surface.CreateFont(("LVM:WQ:REROLL:3:%i"):format(i), {
        font = "Creato Display Medium",
        extended = false,
        weight = 300,
        size = i,
    })  


end 

local function CircleRadius(posx, posy, radius, progress, color)
    local poly = { }
    local v = 220
    poly[1] = {x = posx, y = posy}
    for i = 0, v*progress+0.5 do
        poly[i+2] = {x = math.sin(-math.rad(i/v*360)) * radius + posx, y = math.cos(-math.rad(i/v*360)) * radius + posy}
    end
    draw.NoTexture()
    surface.SetDrawColor(color)
    surface.DrawPoly(poly)
end

 function DrawRadarChart(centerX, centerY, radius, values, maxValue, colors)
    local numPoints = 5
    if #values ~= numPoints or #colors ~= numPoints then
        print("Erreur : les valeurs ou les couleurs ne correspondent pas à 5 points.")
        return
    end

    local segmentAngle = (2 * math.pi) / numPoints
    local polyVertices = {}

    for i = 1, numPoints do
        local value = math.Clamp(values[i], 0, maxValue)
        local normalizedValue = value / maxValue
        local angle = (i - 1) * segmentAngle - math.pi / 2
        local x = centerX + math.cos(angle) * radius * normalizedValue
        local y = centerY + math.sin(angle) * radius * normalizedValue

        table.insert(polyVertices, { x = x, y = y, color = colors[i] })
    end

    surface.SetDrawColor(200, 200, 200, 100)
    for i = 1, numPoints do
        local angle = (i - 1) * segmentAngle - math.pi / 2
        local x = centerX + math.cos(angle) * radius
        local y = centerY + math.sin(angle) * radius
        surface.DrawLine(centerX, centerY, x, y)
    end

    local levels = 4
    for l = 1, levels do
        local levelRadius = radius * (l / levels)
        local previousX, previousY

        for i = 1, numPoints + 1 do
            local angle = (i - 1) * segmentAngle - math.pi / 2
            local x = centerX + math.cos(angle) * levelRadius
            local y = centerY + math.sin(angle) * levelRadius

            if previousX and previousY then
                surface.DrawLine(previousX, previousY, x, y)
            end
            previousX, previousY = x, y
        end
    end

    surface.SetDrawColor(255, 255, 255, 255)
    for i = 1, numPoints do
        local current = polyVertices[i]
        local next = polyVertices[i % numPoints + 1]
        surface.DrawLine(current.x, current.y, next.x, next.y)
    end

    for i, vertex in ipairs(polyVertices) do
        
        CircleRadius(vertex.x, vertex.y, 4, 1, vertex.color)
    end
end


LVM_V2.Pages["F4"]["Statistiques"].Paint = function(self, w, h)
    surface.SetMaterial(LVM_V2:Material("f4/stats.png"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(SW(100), SH(20), SW(1400), SH(750))

    draw.RoundedBoxEx(6, SW(107), SH(27), SW(1386), SH(35), Color(29, 29, 29), true, true, false, false)
    
    draw.SimpleText("Statistiques", LVM_V2.Fonts("LVM", 7, 500), SW(120), SH(34), color_white, TEXT_ALIGN_LEFT)

    local centerX = SW(1060)
    local centerY = SH(470)
    local radius = SH(200)
    local maxValue = 10
    
    self.values = { 
        LocalPlayer():GetSkill("speed"),
        LocalPlayer():GetSkill("chakra"),
        LocalPlayer():GetSkill("resistance"),
        LocalPlayer():GetSkill("strength"),
        LocalPlayer():GetSkill("jump")
    }
    
    local colors = {
        Color(167, 156, 69), -- Speed
        Color(60, 74, 155), -- Chakra
        Color(132, 96, 53), -- Resistance
        Color(162, 69, 69), -- Strength
        Color(47, 210, 123) -- Jump
    }
    DrawRadarChart(centerX, centerY, radius, self.values, maxValue, colors)
    
    draw.SimpleText(self.values[1] .." / 10", LVM_V2.Fonts("LVM", 6, 500), SW(540), SH(115), color_white, TEXT_ALIGN_RIGHT)

    draw.SimpleText(self.values[2] .." / 10", LVM_V2.Fonts("LVM", 6, 500), SW(540), SH(210), color_white, TEXT_ALIGN_RIGHT)
    
    draw.SimpleText(self.values[3] .." / 10", LVM_V2.Fonts("LVM", 6, 500), SW(540), SH(305), color_white, TEXT_ALIGN_RIGHT)
    
    draw.SimpleText(self.values[4] .." / 10", LVM_V2.Fonts("LVM", 6, 500), SW(540), SH(398), color_white, TEXT_ALIGN_RIGHT)
    
    draw.SimpleText(self.values[5] .." / 10", LVM_V2.Fonts("LVM", 6, 500), SW(540), SH(491), color_white, TEXT_ALIGN_RIGHT)
    
    local pdc = LocalPlayer():GetPDC()
    local cColor = Color(255, 255, 72)

    if pdc < 1 then
        cColor = Color(255, 0, 0)
    end
    
    surface.SetFont("LVM:WQ:REROLL:1:70")
    local iTextWidth, iTextHeight = surface.GetTextSize(pdc)
    local iTextx = SW(650) + iTextWidth + SW(10)
    
    
    draw.SimpleText(LocalPlayer():GetPDC(), "LVM:WQ:REROLL:1:70", SW(650), SH(80), color_white, TEXT_ALIGN_LEFT)
    draw.SimpleText("Point de compétence(s)", "LVM:WQ:REROLL:2:30", iTextx, SH(90), color_white, TEXT_ALIGN_LEFT)
    draw.SimpleText("disponible", "LVM:WQ:REROLL:3:20", iTextx, SH(120), cColor, TEXT_ALIGN_LEFT)
    
    draw.RoundedBox(6, SW(126), SH(550), SW(485), SH(182), Color(29, 29, 29))
    draw.SimpleText("Attention, les points de compétences sont limités, utilisez-les avec sagesse.", LVM_V2.Fonts("LVM", 5, 500), SW(140), SH(560), color_white, TEXT_ALIGN_LEFT)
    
end

LVM_V2.Pages["F4"]["Statistiques"].After = function(panel)
    
    for i = 1, 5 do
        
        local skill_up = vgui.Create("DButton", panel)
        skill_up:SetSize(SW(30), SH(30))
        skill_up:SetPos(SW(555), SH(110) + SH(94) * (i-1))
        skill_up:SetText("")
        skill_up.Paint = function(self, w, h)
            -- draw.RoundedBox(0, 0, 0, w, h, color_white)
        end
        skill_up.DoClick = function()
            net.Start("LVM:SKILL:UP")
                net.WriteUInt(i, 4)
            net.SendToServer()
        end
    end
    
    local reset = vgui.Create("DButton", panel)
    reset:SetSize(SW(290), SH(57))
    reset:SetPos(SW(1180), SH(84))
    reset:SetText("")
    reset.Paint = nil;
    reset.DoClick = function()
        LVM_V2.Pages.ResetStat()
    end
    
end