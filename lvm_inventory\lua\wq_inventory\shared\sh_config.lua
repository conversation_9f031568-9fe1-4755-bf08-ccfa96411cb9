WQ_Inventory = WQ_Inventory or {}
WQ_Inventory.tItemRegistered = WQ_Inventory.tItemRegistered or {}

timer.Simple(1, function()
    if (CLIENT) then
        WQ_Inventory.Materials = {
            ["WQ_inventory_background"] = Material("materials/inventory/WQ_inventory_background.png"),
            ["WQ_inventory_close"] = Material("materials/inventory/WQ_inventory_close.png"),
            ["WQ_inventory_scroll_background"] = Material(
                "materials/inventory/WQ_iventory_scroll_background.png"),
            ["WQ_inventory_scroll_grip"] = Material("materials/inventory/WQ_iventory_scroll_grip.png"),
            ["WQ_iventory_item_background"] = Material("materials/inventory/WQ_iventory_item_background.png"),
            ["WQ_inventory_item_info"] = Material("materials/inventory/WQ_inventory_item_info.png"),
            ["WQ_iventory_profile_background"] = Material(
                "materials/inventory/WQ_iventory_profile_background.png"),
            ["WQ_iventory_inventoory_background"] = Material(
                "materials/inventory/WQ_iventory_inventoory_background.png"),

            ["WQ_inventory_noequip_armor"] = Material("materials/inventory/WQ_inventory_noequip_armor.png"),
            ["WQ_inventory_noequip_eye"] = util.ImgurToMaterial("jCD6YwF.png"),
            ["WQ_inventory_noequip_helmet"] = Material("materials/inventory/WQ_inventory_noequip_casque.png"),
            ["WQ_inventory_noequip_cloth"] = Material("materials/inventory/WQ_inventory_noequip_cloth.png"),
            ["WQ_inventory_noequip_mask"] = Material("materials/inventory/WQ_inventory_noequip_mask.png"),
            ["WQ_inventory_noequip_pant"] = Material("materials/inventory/WQ_inventory_noequip_pant.png"),
            ["WQ_inventory_noequip_shoes"] = Material("materials/inventory/WQ_inventory_noequip_shoes.png"),
            ["WQ_inventory_noequip_weapon"] = Material("materials/inventory/WQ_inventory_noequip_weapon.png"),


            ["WQ_inventory_noequip_armor_select"] = Material("materials/inventory/WQ_inventory_noequip_armor_select.png"),
            ["WQ_inventory_noequip_helmet_select"] = Material("materials/inventory/WQ_inventory_noequip_casque_select.png"),
            ["WQ_inventory_noequip_cloth_select"] = Material("materials/inventory/WQ_inventory_noequip_cloth_select.png"),
            ["WQ_inventory_noequip_mask_select"] = Material("materials/inventory/WQ_inventory_noequip_mask_select.png"),
            ["WQ_inventory_noequip_pant_select"] = Material("materials/inventory/WQ_inventory_noequip_pant_select.png"),
            ["WQ_inventory_noequip_shoes_select"] = Material("materials/inventory/WQ_inventory_noequip_pant_select_shoes.png"),
            ["WQ_inventory_noequip_eye_select"] = util.ImgurToMaterial("p6J10TN.png"),
            ["WQ_inventory_noequip_weapon_select"] = Material("materials/inventory/WQ_inventory_noequip_weapon_select.png"),

            ["WQ_inventory_noequip_accesoire1"] = LVM_V2:Material("other/other.png"),
            ["WQ_inventory_noequip_accesoire1_select"] = LVM_V2:Material("other/other_no.png"),

            ["WQ_inventory_noequip_accesoire2"] = LVM_V2:Material("other/other.png"),
            ["WQ_inventory_noequip_accesoire2_select"] = LVM_V2:Material("other/other_no.png"),

            ["WQ_inventory_noequip_accesoire3"] = LVM_V2:Material("other/other.png"),
            ["WQ_inventory_noequip_accesoire3_select"] = LVM_V2:Material("other/other_no.png"),



            ["WQ_inventory_equip"] = Material("materials/inventory/WQ_inventory_equip.png"),


            ["WQ_background_item_info"] = Material("materials/marketplace/WQ_background_item_info.png"),
            ["WQ_background_blur"] = Material("materials/marketplace/WQ_background_blur.png"),



            ["item_test"] = Material("materials/inventory/WQ_inventory_equip.png"),
            ["item_test"] = Material("materials/inventory/WQ_inventory_equip.png"),

            ["planche_de_bois_brut"] = util.ImgurToMaterial("pIogLWB.png"),
            ["bois_ancestral"] = util.ImgurToMaterial("MHIzMh7.png"),
            ["bois_renforce"] = util.ImgurToMaterial("wFIgTQz.png"),
            ["bois_arbre_sacre"] = util.ImgurToMaterial("wcZi1xV.png"),

            ["minerai_de_fer_brut"] = util.ImgurToMaterial("H31V5hw.png"),
            ["lingot_de_fer_purifie"] = util.ImgurToMaterial("BcoYq9D.png"),
            ["lingot_de_fer_maudit"] = util.ImgurToMaterial("Rpy9Qwp.png"),
            ["fer_des_etoiles"] = util.ImgurToMaterial("aDn5khI.png"),

            ["tissu_simple"] = util.ImgurToMaterial("b9DjnjM.png"),
            ["tissu_raffine"] = util.ImgurToMaterial("naHiN5Q.png"),
            ["tissu_spirituel"] = util.ImgurToMaterial("AnngiQN.png"),
            ["tissu_chakra_condense"] = util.ImgurToMaterial("4ppf8q6.png"),

            ["fil_de_soie_basique"] = util.ImgurToMaterial("LNKj5qs.png"),
            ["fibres_de_chanvre"] = util.ImgurToMaterial("qtXmzQ4.png"),
            ["soie_celeste"] = util.ImgurToMaterial("MkHXhdf.png"),
            ["soie_araignee_demoniaque"] = util.ImgurToMaterial("HtJfOql.png"),



            ["item_plastron"] = util.ImgurToMaterial("14M66hk.png"),
            ["item_pantalon"] = util.ImgurToMaterial("CmRhXT3.png"),
            ["item_bottes"] = util.ImgurToMaterial("oS0qVNM.png"),
            ["item_casque"] = util.ImgurToMaterial("xDzOW63.png"),

            ["sharingan1"] = util.ImgurToMaterial("ArrG2Gd.png"),
            ["sharingan2"] = util.ImgurToMaterial("Afk7Vcp.png"),
            ["sharingan3"] = util.ImgurToMaterial("OOX15ua.png"),

            ["item_none"] = Material("materials/WQ_item_none_background.png"),
            ["item_rare"] = Material("materials/WQ_item_rare_background.png"),
            ["item_epic"] = Material("materials/WQ_item_epic_background.png"),
            ["item_legendary"] = Material("materials/WQ_item_legendary_background.png"),

            ["item_bar_none"] = Material("materials/WQ_item_none_name_bck.png"),
            ["item_bar_rare"] = Material("materials/WQ_item_rare_name_bck.png"),
            ["item_bar_epic"] = Material("materials/WQ_item_epic_name_bck.png"),
            ["item_bar_legendary"] = Material("materials/WQ_item_legendary_name_bck.png"),

            ["item_select_none"] = Material("materials/WQ_item_none_select.png"),
            ["item_select_rare"] = Material("materials/WQ_item_rare_select.png"),
            ["item_select_epic"] = Material("materials/WQ_item_epic_select.png"),
            ["item_select_legendary"] = Material("materials/WQ_item_legendary_select.png"),

            ["parchemin_vide"] = util.ImgurToMaterial("X4pxQIn.png"),
            ["stylo"] = util.ImgurToMaterial("dFKhczG.png"),
            ["plastique"] = util.ImgurToMaterial("z5RR7nW.png"),

            ["WQ_inventory_select_item_right"] = Material("materials/inventory/WQ_inventory_select_item_right.png")
        }

        print("material chargé")
    end
end)



WQ_Inventory.TenuBase = "models/skylyxx/ctg/characters/man/body/man_kakashi_gaiden.mdl"

if CLIENT then
    function SW(x) return (x / 1920) * ScrW() end

    function SH(y) return (y / 1080) * ScrH() end

    for i = 1, 100 do
        surface.CreateFont(("LVM:WQ:INVENTORY:1:%i"):format(i), {
            font = "Albert Sans",
            extended = false,
            weight = 900,
            size = i,
        })

        surface.CreateFont(("LVM:WQ:INVENTORY:2:%i"):format(i), {
            font = "Albert Sans",
            extended = false,
            weight = 400,
            size = i,
        })

        surface.CreateFont(("LVM:WQ:INVENTORY:3:%i"):format(i), {
            font = "Albert Sans",
            extended = false,
            weight = 500,
            size = i,
        })
    end

    function WQ_Inventory.fcMoneyWithSpace(iAmount)
        local sformatted = tostring(iAmount)
        local k

        while true do
            sformatted, k = string.gsub(sformatted, "^(-?%d+)(%d%d%d)", '%1 %2')
            if k == 0 then break end
        end

        return sformatted
    end

    function WQ_Inventory.IsEquipedItem(sCategorie)
        for k, v in pairs(LocalPlayer().WQ_Inventory_tEquiped) do
            if v.category == sCategorie then
                return true, v.item
            end
        end
        return false
    end

    function WQ_Inventory.GetAllRegisteredItems()
        local tRegisteredItems = {}

        for key, itemData in pairs(WQ_Inventory.tItemRegistered) do
            table.insert(tRegisteredItems, itemData)
        end

        return tRegisteredItems
    end

    function WQ_Inventory.GetAllItemBuyable()
        local tBuyableItems = {}

        for key, itemData in pairs(WQ_Inventory.tItemRegistered) do
            if itemData.bIsBuyyable then
                table.insert(tBuyableItems, itemData)
            end
        end

        return tBuyableItems
    end
end

WQ_Inventory.tItemRegistered = {}

function WQ_Inventory:GetItem(sClass)
    if WQ_Inventory.tItemRegistered[sClass] then
        return WQ_Inventory.tItemRegistered[sClass]
    end
    return false
end

function WQ_Inventory.fcRegisterItem(sClass, tData)
    if WQ_Inventory.tItemRegistered[sClass] then
        return
    end

    tData.id = #WQ_Inventory.tItemRegistered + 1

    WQ_Inventory.tItemRegistered[sClass] = tData
end

function WQ_Inventory:IfFood(sClass)
    if WQ_Inventory.tItemRegistered[sClass].sItemType == "food" then
        return true
    end
    return false
end

local fooditem = {}

function WQ_Inventory:GetFoodItem()
    if #fooditem > 0 then
        return fooditem
    else
        for k, v in pairs(WQ_Inventory.tItemRegistered) do
            if v.sItemType == "food" then
                table.insert(fooditem, v)
            end
        end
        return fooditem
    end

end

local accesoryitem = {}
local cataccesory = {
    "accesoire1",
    "accesoire2",
    "accesoire3"
}

function WQ_Inventory:GetAccesory()
    if #accesoryitem > 0 then
        return accesoryitem
    else
        for _, v in pairs(WQ_Inventory.tItemRegistered) do
            if v.sItemType and type(v.sItemType) == "table" then
                local found = false
                for _, itemType in ipairs(v.sItemType) do
                    for _, validType in ipairs(cataccesory) do
                        if itemType == validType then
                            found = true
                            break
                        end
                    end
                    if found then break end
                end

                if found then
                    table.insert(accesoryitem, v.sClassName)
                end
            elseif v.sItemType == "mask" then 
                table.insert(accesoryitem, v.sClassName)
            end
        end
        return accesoryitem
    end
end



--[[


    WQ_Inventory.fcRegisterItem("ramen_boeuf", { -- Nom de l'item (doit être unique et le même que le nom de la classe)

        sClassName = "ramen_boeuf", -- Nom de la classe de l'item
        sName = "Ramen de boeuf", -- Nom affiché de l'item
        sModel = "models/foc_props_nourriture/nourriture_ramen_boeuf/foc_nourriture_ramen_boeuf.mdl", -- Modèle 3D de l'item

        sItemType = "food", -- Type de l'item (ex: food, farm, cloth, weapon, etc.)

        iAddHungry = 20, -- Valeur de faim ajoutée lorsque consommé (si type = food)
        iPrice = 500, -- Prix de l'item s'il est achetable ou si c'est un aliment
        bIsBuyyable = false, -- L'item est-il achetable en magasin ? (false = non)

        sVillage = "konoha", -- Village où l'item est disponible (ex: konoha, ame) (si aucun village, ne pas mettre)
        sGrade = "genin1" -- Grade minimum pour l'item (si aucun grade, ne pas mettre)

        Liste des grade: 1er argument a mettre
        
        - eleve 
        - genin1 | Apprentit Genin
        - genin2 | Genin Confirmé
        - chunin1 | Tokubetsu Chunin
        - chunin2 | Chunin
        - chunin3 | Chunin Confirmé
        - tokubetsu | Tokubetsu Jonin
        - jonin | Jonin
        - commandant | Commandant Jonin
        - kage | Kage
        

        bDontDrop = true, -- L'item ne peut pas être jeté depuis l'inventaire
        sMaterial = "planche_de_bois_brut", -- Matériau si aucun modèle n'est utilisé

        sRarity = "rare", -- Rareté de l'item (ex: Rare, Epic, Legendary)

        bRemoveOnDeath = true, -- L'item est supprimé à la mort du joueur

        tBoost = { -- Boosts fournis par l'item
            ["Speed"] = 20, -- Augmente la vitesse en % (Donc la 20% de vitesse en plus)
            ["Resistance"] = 10, -- Augmente la résistance en % (Donc la 10% de résistance en plus)
            ["Damage"] = 10, -- Augmente les dégâts en % (Donc la 10% de dégâts en plus)
            ["Chakra"] = 10 -- Augmente le chakra en % (Donc la 10% de chakra en plus)
            ["Health"] = 10, -- Augmente la vie en % (Donc la 10% de vie en plus)
        } 
    })


]]

WQ_Inventory.fcRegisterItem("parchemin_vide", {
    sClassName = "parchemin_vide",
    sName = "Parhemin Vide",
    sMaterial = "parchemin_vide",
    sItemType = "util",
})

WQ_Inventory.fcRegisterItem("stylo", {
    sClassName = "stylo",
    sName = "Stylo",
    sMaterial = "stylo",
    sItemType = "util",
})

WQ_Inventory.fcRegisterItem("plastique", {
    sClassName = "plastique",
    sName = "Plastique",
    sMaterial = "plastique",
    sItemType = "util",

})


net.Receive("LVM_WQ_Missive:RegisterItem", function()
    local itemname = net.ReadString()
    local randomid = net.ReadString()

    WQ_Inventory.fcRegisterItem(randomid, {
        sClassName = randomid,
        sName = itemname,
        sMaterial = "parchemin_vide",
        sItemType = "util",

    })

end)


----------------------------------------- ACCESSOIRE -----------------------------------------

WQ_Inventory.fcRegisterItem("marionette_accesory", {
    sClassName = "marionette_accesory",
    sName = "Accesoire Marionette",
    sModel = "models/naruto_solve/give_lda/kankuro_dos.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"}, 

    aAngle = Angle(-85.9, 71.4, 104.5),
    vPos = Vector(-50, 6.6, 1.4),
    sBone = "ValveBiped.Bip01_Spine4",
})

WQ_Inventory.fcRegisterItem("jar_sable", {
    sClassName = "jar_sable",
    sName = "Accesoire Jar Sable",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/hyoutan.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"}, 

    aAngle = Angle(-61, 92.1, 90),
    vPos = Vector(-17.5, 1.4, -6.6),
    sBone = "ValveBiped.Bip01_Spine4",
    iScale  = -0.5,
})
WQ_Inventory.fcRegisterItem("naruto_fan_bashosen", {
    sClassName = "naruto_fan_bashosen",
    sName = "Eventail du Bananier",
    sModel = "models/naruto_fan_bashosen.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"}, 

	aAngle = Angle(0, 0, 90),
	vPos = Vector(5, -6, 1),
	sBone = "ValveBiped.Bip01_Spine2",
	iScaleReductor = 0.8698275862069
})
WQ_Inventory.fcRegisterItem("akatsuki_hat", {
    sClassName = "akatsuki_hat",
    sName = "Chapeau Akatsuki",
    sModel = "models/jack/solve/solve_naruto_accessories/akatsuki hat.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(181.5, 100.1, 90.0),
    vPos = Vector(3.2, 2.5, 0.1),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})
WQ_Inventory.fcRegisterItem("m_hanzo_chinoike", {
    sClassName = "m_hanzo_chinoike",
    sName = "Chinoike - Respirateur",
    sModel = "models/models/props/daichi/m_hanzo_chinoike.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
     sBone = "ValveBiped.Bip01_Head1",
    aAngle = Angle(90, 94.1, 90),
    vPos = Vector(1, 2.5, 0),
    bIsHat = true,
    iScale  = -0.5,
})
WQ_Inventory.fcRegisterItem("hokagehat", {
    sClassName = "hokagehat",
    sName = "Chapeau Hokage",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/hokagehat.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(89.8, 20.5, 0.9),
    vPos = Vector(3.4, 2.2, 0.0),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})

WQ_Inventory.fcRegisterItem("chapeauhokagedos", {
    sClassName = "chapeauhokagedos",
    sName = "Chapeau Hokage Dos",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/chapeauhokagedos.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(360.0, 13.0, 90.0),
    vPos = Vector(3.5, -4.8, 0.1),
    sBone = "ValveBiped.Bip01_Spine2",
})

WQ_Inventory.fcRegisterItem("kazehat", {
    sClassName = "kazehat",
    sName = "Chapeau Kazekage",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/kazehat.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(87.6, 273.0, 256.0),
    vPos = Vector(4.0, 2.8, -0.1),
    sBone = "ValveBiped.Bip01_Head1",
    bIsHat = true,
})

WQ_Inventory.fcRegisterItem("amekagehat", {
    sClassName = "amekagehat",
    sName = "Chapeau Amekage",
    sModel = "models/models/props/daichi/m_amekage_daichi.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(87.6, 273.0, 256.0),
    vPos = Vector(4.0, 2.8, -0.1),
    bIsHat = true,
})
 WQ_Inventory.fcRegisterItem("amekage_chinoike_hat", {
     sClassName = "amekage_chinoike_hat",
     sName = "Chapeau Chinoike Amekage",
     sModel = "models/models/props/daichi/m_amekage_chinoike.mdl",
     sItemType = {"accesoire1", "accesoire2", "accesoire3"},
     aAngle = Angle(90, 90, 81.7),
     vPos = Vector(4.3, 3.2, -0.1),
     sBone = "ValveBiped.Bip01_Head1",
    
     bIsHat = true,
})

WQ_Inventory.fcRegisterItem("mizuhat", {
    sClassName = "mizuhat",
    sName = "Chapeau Mizukage",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/mizuhat.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(87.6, 273.0, 256.0),
    vPos = Vector(4.0, 2.8, -0.1),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})

WQ_Inventory.fcRegisterItem("raihat", {
    sClassName = "raihat",
    sName = "Chapeau Raikage",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/raihat.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(87.6, 273.0, 256.0),
    vPos = Vector(4.0, 2.8, -0.1),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})

WQ_Inventory.fcRegisterItem("tsuchihat", {
    sClassName = "tsuchihat",
    sName = "Chapeau Tsuchikage",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/tsuchihat.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(87.6, 273.0, 256.0),
    vPos = Vector(4.0, 2.8, -0.1),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})

WQ_Inventory.fcRegisterItem("chaporouge", {
    sClassName = "chaporouge",
    sName = "Chapeau Rouge",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/chaporouge.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(89.7, 0.0, 345.9),
    vPos = Vector(3.2, 1.9, 0.0),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})


WQ_Inventory.fcRegisterItem("chapeaupaysan1", {
    sClassName = "chapeaupaysan1",
    sName = "Chapeau Paysan 1",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/chapeaupaysan1.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(174.7, 99.8, 90.0),
    vPos = Vector(6.2, 2.2, 0.0),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})

WQ_Inventory.fcRegisterItem("chapeaupaysan2", {
    sClassName = "chapeaupaysan2",
    sName = "Chapeau Paysan 2",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/chapeaupaysan2.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(174.7, 99.8, 90.0),
    vPos = Vector(6.2, 2.2, 0.0),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})

WQ_Inventory.fcRegisterItem("chapeaupaysan3", {
    sClassName = "chapeaupaysan3",
    sName = "Chapeau Paysan 3",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/chapeaupaysan3.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(174.7, 99.8, 90.0),
    vPos = Vector(6.2, 2.2, 0.1),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})

WQ_Inventory.fcRegisterItem("chapeaupaysan4", {
    sClassName = "chapeaupaysan4",
    sName = "Chapeau Paysan 4",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/chapeaupaysan4.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(174.7, 107.4, 90.0),
    vPos = Vector(5.4, 1.7, 0.1),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})

WQ_Inventory.fcRegisterItem("sm_christmas_capmo", {
    sClassName = "sm_christmas_capmo",
    sName = "Bonnet Noël",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/sm_christmas_capmo.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(0.0, -80.0, -90.0),
    vPos = Vector(7.0, 3.0, -0.3),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})

WQ_Inventory.fcRegisterItem("bear_hat", {
    sClassName = "bear_hat",
    sName = "Chapeau Ours",
    sModel = "models/captainbigbutt/skeyler/hats/bear_hat.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(178.8, 95.4, 89.4),
    vPos = Vector(6.8, 2.0, 0.1),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})

WQ_Inventory.fcRegisterItem("cat_hat", {
    sClassName = "cat_hat",
    sName = "Chapeau Chat",
    sModel = "models/captainbigbutt/skeyler/hats/cat_hat.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(178.8, 95.4, 89.4),
    vPos = Vector(5.7, 2.1, 0.1),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})

WQ_Inventory.fcRegisterItem("frog_hat", {
    sClassName = "frog_hat",
    sName = "Chapeau Grenouille",
    sModel = "models/captainbigbutt/skeyler/hats/frog_hat.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(178.5, 87.7, 89.4),
    vPos = Vector(6.3, 2.3, 0.0),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})

WQ_Inventory.fcRegisterItem("helmet_naruto", {
    sClassName = "helmet_naruto",
    sName = "Casque Naruto",
    sModel = "models/naruto_solve/give_lda/helmet_naruto.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(178.3, 88.5, 89.6),
    vPos = Vector(0.6, 2.9, 0.1),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})

WQ_Inventory.fcRegisterItem("kankuro_bonnet", {
    sClassName = "kankuro_bonnet",
    sName = "Chapeau Kankuro",
    sModel = "models/naruto_solve/give_lda/kankuro_bonnet.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(91.7, 83.0, 90.0),
    vPos = Vector(-82.0, 12.5, 0.3),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})

WQ_Inventory.fcRegisterItem("fedora", {
    sClassName = "fedora",
    sName = "Chapeau Fedora",
    sModel = "models/captainbigbutt/skeyler/hats/fedora.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(178.7, 99.9, 88.3),
    vPos = Vector(7.4, 2.8, 0.0),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})

WQ_Inventory.fcRegisterItem("headband_maid", {
    sClassName = "headband_maid",
    sName = "Bandeau de Servante",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/headband_maid.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(87.9, 102.7, 94.3),
    vPos = Vector(7.8, 0.0, 0.0),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("pijamhat", {
    sClassName = "pijamhat",
    sName = "Chapeau Pyjama",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/pijamhat.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(89.7, 105.3, 87.7),
    vPos = Vector(5.2, 3.6, 0.0),
    sBone = "ValveBiped.Bip01_Head1",

    bIsHat = true,
})

-- Bandeaux et protecteurs
WQ_Inventory.fcRegisterItem("konoha", {
    sClassName = "konoha",
    sName = "Bandeau Konoha",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/konoha.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(88.4, 266.2, 256.4),
    vPos = Vector(4.5, 1.8, -0.2),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("suna", {
    sClassName = "suna",
    sName = "Bandeau Suna",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/suna.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(88.4, 266.2, 256.4),
    vPos = Vector(4.5, 1.8, -0.2),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("kumo", {
    sClassName = "kumo",
    sName = "Bandeau Kumo",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/kumo.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(88.4, 266.2, 256.4),
    vPos = Vector(4.5, 1.8, -0.2),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("iwa", {
    sClassName = "iwa",
    sName = "Bandeau Iwa",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/iwa.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(88.4, 266.2, 256.4),
    vPos = Vector(4.5, 1.8, -0.2),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("kiri", {
    sClassName = "kiri",
    sName = "Bandeau Kiri",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/kiri.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(88.4, 266.2, 256.4),
    vPos = Vector(4.5, 1.8, -0.2),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("kusa", {
    sClassName = "kusa",
    sName = "Bandeau Kusa",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/kusa.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(88.4, 266.2, 256.4),
    vPos = Vector(4.5, 1.8, -0.2),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("taki", {
    sClassName = "taki",
    sName = "Bandeau Taki",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/taki.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(88.4, 266.2, 256.4),
    vPos = Vector(4.5, 1.8, -0.2),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("oto", {
    sClassName = "oto",
    sName = "Bandeau Oto",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/oto.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(88.4, 266.2, 256.4),
    vPos = Vector(4.5, 1.8, -0.2),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("shinobi", {
    sClassName = "shinobi",
    sName = "Bandeau Shinobi",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/shinobi.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(88.4, 266.2, 256.4),
    vPos = Vector(4.5, 1.8, -0.2),
    sBone = "ValveBiped.Bip01_Head1",
})

-- Lunettes et accessoires de visage
WQ_Inventory.fcRegisterItem("lunette_aburame", {
    sClassName = "lunette_aburame",
    sName = "Lunettes Aburame",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/lunette_aburame.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(180.0, 115.0, 90.0),
    vPos = Vector(4.1, -2.1, 0.1),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("goggles_shino", {
    sClassName = "goggles_shino",
    sName = "Lunettes Shino",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/goggles_shino.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(89.8, 105.3, 90.7),
    vPos = Vector(3.6, 0.3, 0.0),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("glasses", {
    sClassName = "glasses",
    sName = "Lunettes",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/glasses.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(178.2, 97.5, 91.0),
    vPos = Vector(3.7, -0.8, 0.0),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("glasses_choujyuurou", {
    sClassName = "glasses_choujyuurou",
    sName = "Lunettes Chojuro",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/glasses_choujyuurou.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(178.2, 89.8, 90.5),
    vPos = Vector(3.9, -0.8, 0.0),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("glasses_karin", {
    sClassName = "glasses_karin",
    sName = "Lunettes Karin",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/glasses_karin.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(88.7, 90.7, 85.7),
    vPos = Vector(3.9, -0.9, 0.0),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("glasses_sarada", {
    sClassName = "glasses_sarada",
    sName = "Lunettes Sarada",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/glasses_sarada.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(90.3, 88.5, 83.9),
    vPos = Vector(3.9, -1.4, 0.0),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("klienerglasses", {
    sClassName = "klienerglasses",
    sName = "Lunettes Kliener",
    sModel = "models/gmod_tower/klienerglasses.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(180.6, 96.3, 90.2),
    vPos = Vector(3.5, 0.1, 0.0),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("glasses_kabuto", {
    sClassName = "glasses_kabuto",
    sName = "Lunettes Kabuto",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/glasses_kabuto.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(181.4, 94.9, 89.7),
    vPos = Vector(3.5, -1.1, 0.0),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("glasses_heart", {
    sClassName = "glasses_heart",
    sName = "Lunettes Coeur",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/glasses_heart.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(180.0, 94.5, 90.0),
    vPos = Vector(3.8, -2.0, 0.0),
    sBone = "ValveBiped.Bip01_Head1",
})


WQ_Inventory.fcRegisterItem("glasses_bee", {
    sClassName = "glasses_bee",
    sName = "Lunettes Killer B",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/glasses_bee.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(179.1, 107.0, 90.4),
    vPos = Vector(3.8, -1.9, 0.0),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("glasses_deidara", {
    sClassName = "glasses_deidara",
    sName = "Lunettes Deidara",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/glasses_deidara.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(183.1, 98.7, 88.0),
    vPos = Vector(3.3, -1.5, -2.2),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("glasses_monocle", {
    sClassName = "glasses_monocle",
    sName = "Monocle",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/glasses_monocle.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(178.6, 101.8, 89.9),
    vPos = Vector(2.5, -0.6, -2.4),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("goggles_naruto_boy", {
    sClassName = "goggles_naruto_boy",
    sName = "Lunettes Naruto",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/goggles_naruto_boy.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(87.9, 102.7, 94.3),
    vPos = Vector(5.1, 0.1, 0.1),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("sm_chr_glasses_mizukagemo", {
    sClassName = "sm_chr_glasses_mizukagemo",
    sName = "Lunettes Mizukage",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/sm_chr_glasses_mizukagemo.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(182.4, 102.4, 90.0),
    vPos = Vector(3.0, 0.0, -0.1),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("sm_glasses_4thmo", {
    sClassName = "sm_glasses_4thmo",
    sName = "Lunettes 4ème",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/sm_glasses_4thmo.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(180.5, 105.9, 90.0),
    vPos = Vector(3.7, -1.3, 0.0),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("down_glasses_tobi", {
    sClassName = "down_glasses_tobi",
    sName = "Lunettes Tobi",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/down_glasses_tobi.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(179.8, 97.0, 90.0),
    vPos = Vector(3.4, 0.2, 0.0),
    sBone = "ValveBiped.Bip01_Head1",
})

-- Capes et accessoires de dos
WQ_Inventory.fcRegisterItem("cap_hokage", {
    sClassName = "cap_hokage",
    sName = "Cape Hokage",
    sModel = "models/naruto_solve/give_lda/player/capeho/cap_hokage.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(180.5, 271.3, 270.3),
    vPos = Vector(-45.2, 0.6, 0.6),
    sBone = "ValveBiped.Bip01_Spine2",
})

WQ_Inventory.fcRegisterItem("cap_mizukage", {
    sClassName = "cap_mizukage",
    sName = "Cape Mizukage",
    sModel = "models/naruto_solve/give_lda/player/capemizu/cap_mizukage.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(180.5, 271.3, 270.3),
    vPos = Vector(-45.2, 0.6, 0.6),
    sBone = "ValveBiped.Bip01_Spine2",
})

WQ_Inventory.fcRegisterItem("cape_tsuchikage", {
    sClassName = "cape_tsuchikage",
    sName = "Cape Tsuchikage",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/cape_tsuchikage.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(0.0, 87.0, 90.0),
    vPos = Vector(-3.2, -0.1, 0.0),
    sBone = "ValveBiped.Bip01_Spine2",
})

WQ_Inventory.fcRegisterItem("sasuke_cape", {
    sClassName = "sasuke_cape",
    sName = "Cape Sasuke",
    sModel = "models/kleyyn/solve/solve_naruto_accessories/sasuke_cape.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(270.0, 180.0, 345.0),
    vPos = Vector(-60.0, 17.8, 0.0),
    sBone = "ValveBiped.Bip01_Spine4",
})

WQ_Inventory.fcRegisterItem("capeorochimaru", {
    sClassName = "capeorochimaru",
    sName = "Cape Orochimaru",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/capeorochimaru.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(0.0, 89.3, 90.4),
    vPos = Vector(2.8, -4.2, -0.3),
    sBone = "ValveBiped.Bip01_Spine",
})

-- Accessoires de corps et de ceinture
WQ_Inventory.fcRegisterItem("bag", {
    sClassName = "bag",
    sName = "Sac",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/bag.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(357.4, 87.3, 88.3),
    vPos = Vector(5.3, -4.8, -0.3),
    sBone = "ValveBiped.Bip01_Spine2",
})

WQ_Inventory.fcRegisterItem("bag1", {
    sClassName = "bag1",
    sName = "Sac 1",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/bag1.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(180.1, 88.7, 92.4),
    vPos = Vector(6.3, -6.8, -0.3),
    sBone = "ValveBiped.Bip01_Spine2",
})

WQ_Inventory.fcRegisterItem("shur", {
    sClassName = "shur",
    sName = "Shuriken FUMA",
    sModel = "models/foc_props_arme/arme_shuriken_fuma/foc_arme_shuriken_fuma.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(-48.6, 180, 90),
    vPos = Vector(10.6, -6, 0),
    sBone = "ValveBiped.Bip01_Spine2",
    iScale = 0.2
})



WQ_Inventory.fcRegisterItem("windornaments_01", {
    sClassName = "windornaments_01",
    sName = "Ornement Vent",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/windornaments_01.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(283.9, 92.7, 88.1),
    vPos = Vector(3.3, -6.8, -0.3),
    sBone = "ValveBiped.Bip01_Spine2",
})

WQ_Inventory.fcRegisterItem("backpack_gris", {
    sClassName = "backpack_gris",
    sName = "Sac à dos Gris",
    sModel = "models/naruto_solve/give_lda/backpack_gris.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(360.0, 89.9, 88.2),
    vPos = Vector(-3.6, -4.1, -0.3),
    sBone = "ValveBiped.Bip01_Spine2",
})

WQ_Inventory.fcRegisterItem("backpack_jaune", {
    sClassName = "backpack_jaune",
    sName = "Sac à dos Jaune",
    sModel = "models/naruto_solve/give_lda/backpack_jaune.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(360.0, 89.9, 88.2),
    vPos = Vector(-3.6, -4.1, -0.3),
    sBone = "ValveBiped.Bip01_Spine2",
})

WQ_Inventory.fcRegisterItem("backpack_vert", {
    sClassName = "backpack_vert",
    sName = "Sac à dos Vert",
    sModel = "models/naruto_solve/give_lda/backpack_vert.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(360.0, 89.9, 88.2),
    vPos = Vector(-3.6, -4.1, -0.3),
    sBone = "ValveBiped.Bip01_Spine2",
})

WQ_Inventory.fcRegisterItem("bag_rin", {
    sClassName = "bag_rin",
    sName = "Sac Rin",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/bag_rin.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(360.0, 89.9, 88.2),
    vPos = Vector(4.1, -4.1, -0.1),
    sBone = "ValveBiped.Bip01_Spine2",
})

WQ_Inventory.fcRegisterItem("sacoche", {
    sClassName = "sacoche",
    sName = "Sacoche",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/sacoche.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(327.8, 95.8, 91.1),
    vPos = Vector(-0.1, -3.9, -2.8),
    sBone = "ValveBiped.Bip01_Spine",
})

WQ_Inventory.fcRegisterItem("bag2", {
    sClassName = "bag2",
    sName = "Sac 2",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/bag2.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(47.4, 98.2, 91.1),
    vPos = Vector(-0.1, -3.9, -2.8),
    sBone = "ValveBiped.Bip01_Spine",
})


WQ_Inventory.fcRegisterItem("bag5", {
    sClassName = "bag5",
    sName = "Sac 5",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/bag5.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(175.7, 98.8, 90.4),
    vPos = Vector(2.7, -5.6, -0.6),
    sBone = "ValveBiped.Bip01_Spine",
})

WQ_Inventory.fcRegisterItem("ceinture_guren", {
    sClassName = "ceinture_guren",
    sName = "Ceinture Guren",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/ceinture_guren.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(0.0, 89.3, 90.4),
    iScale  = 0.5,
    vPos = Vector(-0.9, -3.4, -0.2),
    sBone = "ValveBiped.Bip01_Spine",
})


WQ_Inventory.fcRegisterItem("hyoutan", {
    sClassName = "hyoutan",
    sName = "Gourde Hyoutan",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/hyoutan.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(1.5, 89.4, 48.7),
    vPos = Vector(-0.6, -10.9, 1.0),
    sBone = "ValveBiped.Bip01_Spine2",
})

WQ_Inventory.fcRegisterItem("kankuro_dos", {
    sClassName = "kankuro_dos",
    sName = "Marionnette de Dos",
    sModel = "models/naruto_solve/give_lda/kankuro_dos.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(268.5, 178.6, 1.3),
    vPos = Vector(-68.6, 3.0, 1.0),
    sBone = "ValveBiped.Bip01_Spine2",
})

WQ_Inventory.fcRegisterItem("ears_cats", {
    sClassName = "ears_cats",
    sName = "Oreilles de Chat",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/ears_cats.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(183.3, 98.1, 88.9),
    vPos = Vector(7.4, 1.2, 0.0),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("sk_chr_ears_dogsmo", {
    sClassName = "sk_chr_ears_dogsmo",
    sName = "Oreilles de Chien",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/sk_chr_ears_dogsmo.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(0.0, 87.8, 90.0),
    vPos = Vector(5.9, 0.9, 0.0),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("bandaid", {
    sClassName = "bandaid",
    sName = "Pansement",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/bandaid.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(210.0, 104.5, 107.6),
    vPos = Vector(2.5, -2.2, -1.5),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("medicalgantai", {
    sClassName = "medicalgantai",
    sName = "Équipement Médical",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/medicalgantai.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(168.0, 105.7, 86.9),
    vPos = Vector(3.7, -1.9, 1.4),
    sBone = "ValveBiped.Bip01_Head1",
})

WQ_Inventory.fcRegisterItem("newyear_ricecake", {
    sClassName = "newyear_ricecake",
    sName = "Gâteau de riz",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/newyear_ricecake.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(176.7, 98.3, 90.0),
    vPos = Vector(6.5, 0.7, 0.0),
    sBone = "ValveBiped.Bip01_Head1",
})

-- Rouleaux, parchemins et livres
WQ_Inventory.fcRegisterItem("sairouleau", {
    sClassName = "sairouleau",
    sName = "Rouleau Sai",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/sairouleau.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(174.4, 103.2, 87.8),
    vPos = Vector(-0.6, -12.5, -0.3),
    sBone = "ValveBiped.Bip01_Spine",
    iScale = 5,
})

WQ_Inventory.fcRegisterItem("scroll", {
    sClassName = "scroll",
    sName = "Rouleau",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/scroll.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(1.0, 2.1, 0.7),
    vPos = Vector(0.5, -3.5, -0.4),
    sBone = "ValveBiped.Bip01_Spine",
})

WQ_Inventory.fcRegisterItem("contract_scroll", {
    sClassName = "contract_scroll",
    sName = "Rouleau de Contrat",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/contract scroll.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(180.0, 111.1, 89.3),
    vPos = Vector(0.5, -6.3, 0.0),
    sBone = "ValveBiped.Bip01_Spine",
})

WQ_Inventory.fcRegisterItem("narutos_scroll", {
    sClassName = "narutos_scroll",
    sName = "Rouleau de Naruto",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/naruto's scroll.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(179.4, 91.3, 90.6),
    vPos = Vector(5.3, -5.3, 0.0),
    sBone = "ValveBiped.Bip01_Spine",
    iScale = 0.01,
})

WQ_Inventory.fcRegisterItem("narutos_scroll_1", {
    sClassName = "narutos_scroll_1",
    sName = "Rouleau de Naruto 1",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/naruto's scroll 1.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(178.7, 90.3, 89.8),
    vPos = Vector(0.3, -6.5, 0.0),
    sBone = "ValveBiped.Bip01_Spine",
})

WQ_Inventory.fcRegisterItem("narutos_scroll_2", {
    sClassName = "narutos_scroll_2",
    sName = "Rouleau de Naruto 2",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/naruto's scroll 2.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(178.7, 90.3, 89.8),
    vPos = Vector(0.3, -6.5, 0.0),
    sBone = "ValveBiped.Bip01_Spine",
})

WQ_Inventory.fcRegisterItem("narutos_scroll_3", {
    sClassName = "narutos_scroll_3",
    sName = "Rouleau de Naruto 3",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/naruto's scroll 3.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(181.9, 360.0, 84.1),
    vPos = Vector(0.8, -6.5, 0.0),
    sBone = "ValveBiped.Bip01_Spine",
})

WQ_Inventory.fcRegisterItem("three_layered_scroll", {
    sClassName = "three_layered_scroll",
    sName = "Rouleau à Trois Couches",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/three-layered scroll.mdl",
    sItemType = {"accesoire1", "accesoire2", "accesoire3"},
    aAngle = Angle(359.0, 275.4, 90.0),
    vPos = Vector(6.9, -5.5, -0.3),
    sBone = "ValveBiped.Bip01_Spine",
    iScale = 2.5,
})

------------------------- FOOD -------------------------

WQ_Inventory.fcRegisterItem("ramen_boeuf", {
    sClassName = "ramen_boeuf",
    sName = "Ramen de boeuf",
    sModel = "models/foc_props_nourriture/nourriture_ramen_boeuf/foc_nourriture_ramen_boeuf.mdl",
    sItemType = "food",
    iAddHungry = 67,
    iPrice = 67,
    bIsBuyyable = false,
    bDontDrop = true, ------- Ne peut pas être drop
})

WQ_Inventory.fcRegisterItem("ramen_bacon", {
    sClassName = "ramen_bacon",
    sName = "Ramen de Bacon",
    sModel = "models/foc_props_nourriture/nourriture_ramen_bacon/foc_nourriture_ramen_bacon.mdl",
    sItemType = "food",
    iPrice = 38,
    iAddHungry = 38,
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("assiete_melon", {
    sClassName = "assiete_melon",
    sName = "Assiette de melon",
    sModel = "models/foc_props_nourriture/nourriture_ramen_melon/foc_nourriture_ramen_melon.mdl",
    sItemType = "food",
    iPrice = 40,
    iAddHungry = 40,
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("assiete_boeuf", {
    sClassName = "assiete_boeuf",
    sName = "Assiette de boeuf",
    sModel = "models/foc_props_nourriture/nourriture_plat_boeuf/foc_nourriture_plat_boeuf.mdl",
    sItemType = "food", 
    iAddHungry = 30,
    iPrice = 30,
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("assiete_porc", {
    sClassName = "assiete_porc",
    sName = "Assiette de mouton",
    sModel = "models/foc_props_nourriture/nourriture_plat_porc/foc_nourriture_plat_porc.mdl",
    sItemType = "food",
    iPrice = 25,
    iAddHungry = 20,
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("soupe_algue", {
    sClassName = "soupe_algue",
    sName = "Soupe d'algue",
    sModel = "models/foc_props_nourriture/nourriture_ramen_healthy/foc_nourriture_ramen_healthy.mdl",
    sItemType = "food",
    iPrice = 15,
    iAddHungry = 20,
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("brochette_viande", {
    sClassName = "brochette_viande",
    sName = "Brochette de viande",
    sModel = "models/foc_props_nourriture/nourriture_brochette_viande/foc_nourriture_brochette_viande.mdl",
    sItemType = "food",
    iPrice = 20,
    iAddHungry = 20,
    bIsBuyyable = false,
})


WQ_Inventory.fcRegisterItem("bol_mochi", {
    sClassName = "bol_mochi",
    sName = "Bol de Mochi",
    sModel = "models/foc_props_nourriture/nourriture_moshi_bol/foc_nourriture_moshi_bol.mdl",
    sItemType = "food",
    iPrice = 100,
    iAddHungry = 100,
    bIsBuyyable = false,
})









-- Konohagakure --
WQ_Inventory.fcRegisterItem("konoha_eleve2", {
    sClassName = "konoha_eleve2",
    sName = "Konoha - Eleve Distingué",
    sModel = "models/godio/konoha_eleve2.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 1000,
    sVillage = "konoha",
    sGrade = "genin1",
})
-- Genin --
WQ_Inventory.fcRegisterItem("konoha_genin", {
    sClassName = "konoha_genin",
    sName = "Konoha - Genin",
    sModel = "models/godio/konoha_genin.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 5620,
    sVillage = "konoha",
    sGrade = "genin1",
})

WQ_Inventory.fcRegisterItem("konoha_genin2", {
    sClassName = "konoha_genin2",
    sName = "Konoha - Genin 2",
    sModel = "models/godio/konoha_genin2.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 5620,
    sVillage = "konoha",
    sGrade = "genin1",
})
-- Chunin --
WQ_Inventory.fcRegisterItem("konoha_chunin", {
    sClassName = "konoha_chunin",
    sName = "Konoha - Chunin",
    sModel = "models/godio/konoha_chunin.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 8240,
    sVillage = "konoha",
    sGrade = "chunin1",
})

WQ_Inventory.fcRegisterItem("konoha_chunin2", {
    sClassName = "konoha_chunin2",
    sName = "Konoha - Chunin 2",
    sModel = "models/godio/konoha_chunin2.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 8240,
    sVillage = "konoha",
    sGrade = "chunin1",
})

WQ_Inventory.fcRegisterItem("konoha_chunin3", {
    sClassName = "konoha_chunin3",
    sName = "Konoha - Chunin 3",
    sModel = "models/godio/konoha_chunin3.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 9784,
    sVillage = "konoha",
    sGrade = "chunin1",
})
WQ_Inventory.fcRegisterItem("w_tokuchunin_konoha", {
    sClassName = "w_tokuchunin_konoha",
    sName = "Konoha - Tokubetsu Chunin Femme",
    sModel = "models/daichi/w_tokuchunin_konoha.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sVillage = "konoha",
    sGrade = "chunin1",
})
WQ_Inventory.fcRegisterItem("m_tokuchunin_konoha", {
    sClassName = "m_tokuchunin_konoha",
    sName = "Konoha - Tokubetsu Chunin",
    sModel = "models/daichi/m_tokuchunin_konoha.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sVillage = "konoha",
    sGrade = "chunin1",
})
WQ_Inventory.fcRegisterItem("konoha_chunin4", {
    sClassName = "konoha_chunin4",
    sName = "Konoha - Chunin 4",
    sModel = "models/godio/konoha_chunin4.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 6410,
    sVillage = "konoha",
    sGrade = "chunin2",
})

WQ_Inventory.fcRegisterItem("konoha_chunin5", {
    sClassName = "konoha_chunin5",
    sName = "Konoha - Chunin 5",
    sModel = "models/godio/konoha_chunin5.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 6410,
    sVillage = "konoha",
    sGrade = "chunin3",
})
-- Jonin --

WQ_Inventory.fcRegisterItem("konoha_jonin", {
    sClassName = "konoha_jonin",
    sName = "Konoha - Jonin",
    sModel = "models/godio/konoha_jonin.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 12870,
    sVillage = "konoha",
    sGrade = "jonin",
})

WQ_Inventory.fcRegisterItem("konoha_jonin2", {
    sClassName = "konoha_jonin2",
    sName = "Konoha - Jonin 2",
    sModel = "models/godio/konoha_jonin2.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 9856,
    sVillage = "konoha",
    sGrade = "jonin",
})

WQ_Inventory.fcRegisterItem("konoha_jonin3", {
    sClassName = "konoha_jonin3",
    sName = "Konoha - Jonin 3",
    sModel = "models/godio/konoha_jonin3.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 11458,
    sVillage = "konoha",
    sGrade = "jonin",
})
WQ_Inventory.fcRegisterItem("kono_hc", {
    sClassName = "kono_hc",
    sName = "Konoha - HC Combat",
    sModel = "models/godio/kono_hc.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 20000,
    sVillage = "konoha",
    sGrade = "chunin3",
})
-- Hokage --
WQ_Inventory.fcRegisterItem("konoha_kage", {
    sClassName = "konoha_kage",
    sName = "Konoha - Kage",
    sModel = "models/godio/konoha_kage.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 15000,
    sVillage = "konoha",
    sGrade = "kage",
})
-- Autres --
WQ_Inventory.fcRegisterItem("konoha_anbu", {
    sClassName = "konoha_anbu",
    sName = "Konoha - Anbu",
    sModel = "models/godio/konoha_anbu.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 14856,
    sVillage = "konoha",
    sGrade = "kage",
})
WQ_Inventory.fcRegisterItem("konoha_police", {
    sClassName = "konoha_police",
    sName = "Konoha - Police",
    sModel = "models/godio/konoha_police.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sVillage = "konoha",
})
WQ_Inventory.fcRegisterItem("konoha_medic", {
    sClassName = "konoha_medic",
    sName = "Konoha - Médecin",
    sModel = "models/godio/konoha_medic.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sVillage = "konoha",
})
WQ_Inventory.fcRegisterItem("konoha_tai_f", {
    sClassName = "konoha_tai_f",
    sName = "Konoha - Femme Taijutsuman",
    sModel = "models/godio/konoha_tai_f.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 5843,
    sVillage = "konoha",
})
WQ_Inventory.fcRegisterItem("konoha_tai", {
    sClassName = "konoha_tai",
    sName = "Konoha - Taijutsuman",
    sModel = "models/godio/konoha_tai.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 2340,
    sVillage = "konoha",
})
WQ_Inventory.fcRegisterItem("konoha_tai_c", {
    sClassName = "konoha_tai_c",
    sName = "Konoha - Taijutsuman Chunin",
    sModel = "models/godio/konoha_tai_c.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 5843,
    sVillage = "konoha",
})
WQ_Inventory.fcRegisterItem("goro_logistique", {
    sClassName = "goro_logistique",
    sName = "Konoha - Logistique",
    sModel = "models/godio/goro_logistique.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 5843,
    sVillage = "konoha",
})
WQ_Inventory.fcRegisterItem("tenue_diesel", {
    sClassName = "tenue_diesel",
    sName = "Diesel Tenue",
    sModel = "models/kaesar/solve/solve_naruto_bodies/man_vest_07_m_bsi.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})

-------------------------------------- Clan Konohagakure -----------------------------------------
-- Uchiha --
WQ_Inventory.fcRegisterItem("uchiha_jm", {
    sClassName = "uchiha_jm",
    sName = "Uchiha - Jeune Membre",
    sModel = "models/godio/uchiha_jm.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("uchiha_jm2", {
    sClassName = "uchiha_jm2",
    sName = "Uchiha - Jeune Membre 2",
    sModel = "models/godio/uchiha_jm2.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("uchiha_shisui", {
    sClassName = "uchiha_shisui",
    sName = "Uchiha - Jeune Membre 3",
    sModel = "models/godio/uchiha_shisui.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("uchiha_chill", {
    sClassName = "uchiha_chill",
    sName = "Uchiha - Jeune Détente",
    sModel = "models/godio/uchiha_chill.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("uchiha_m", {
    sClassName = "uchiha_m",
    sName = "Uchiha - Membre",
    sModel = "models/godio/uchiha_m.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("uchiha_m3", {
    sClassName = "uchiha_m3",
    sName = "Uchiha - Membre 3",
    sModel = "models/godio/uchiha_m3.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("uchiha_chunin", {
    sClassName = "uchiha_chunin",
    sName = "Uchiha - Chunin",
    sModel = "models/godio/uchiha_chunin.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("eclypse_uchiha_chef", {
    sClassName = "eclypse_uchiha_chef",
    sName = "Uchiha - Chef",
    sModel = "models/godio/eclypse_uchiha_chef.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("uchiha_c", {
    sClassName = "uchiha_c",
    sName = "Uchiha - Combat",
    sModel = "models/godio/uchiha_c.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
-- Senju --
WQ_Inventory.fcRegisterItem("senju_jm", {
    sClassName = "senju_jm",
    sName = "Senju - Jeune Membre",
    sModel = "models/godio/senju_jm.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("senju_jm2", {
    sClassName = "senju_jm2",
    sName = "Senju - Jeune Membre 2",
    sModel = "models/godio/senju_jm2.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("senju_m", {
    sClassName = "senju_m",
    sName = "Senju - Membre",
    sModel = "models/godio/senju_m.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("senju_m2", {
    sClassName = "senju_m2",
    sName = "Senju - Membre 2",
    sModel = "models/godio/senju_m2.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("eclypse_senju_chef", {
    sClassName = "eclypse_senju_chef",
    sName = "Senju - Chef",
    sModel = "models/godio/eclypse_senju_chef.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("m_chef_senju", {
    sClassName = "m_chef_senju",
    sName = "Konoha - Chef Senju",
    sModel = "models/daichi/m_chef_senju.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
-- Hyuga --
WQ_Inventory.fcRegisterItem("hyuga_jm", {
    sClassName = "hyuga_jm",
    sName = "Hyuga - Jeune Membre",
    sModel = "models/godio/hyuga_jm.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("hyuga_m", {
    sClassName = "hyuga_m",
    sName = "Hyuga - Membre",
    sModel = "models/godio/hyuga_m.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})

WQ_Inventory.fcRegisterItem("hyuga_hg", {
    sClassName = "hyuga_hg",
    sName = "Hyuga - Haut Gradé",
    sModel = "models/godio/hyuga_hg.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
-- Nara --
WQ_Inventory.fcRegisterItem("nara_jm", {
    sClassName = "nara_jm",
    sName = "Nara - Jeune Membre",
    sModel = "models/godio/nara_jm.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("goro_nara_g", {
    sClassName = "goro_nara_g",
    sName = "Nara - Jeune Membre 2",
    sModel = "models/godio/goro_nara_g.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("nara_m", {
    sClassName = "nara_m",
    sName = "Nara - Membre",
    sModel = "models/godio/nara_m.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("goro_nara_c", {
    sClassName = "goro_nara_c",
    sName = "Nara - Membre 2",
    sModel = "models/godio/goro_nara_c.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("nara_chef", {
    sClassName = "nara_chef",
    sName = "Nara - Chef",
    sModel = "models/godio/nara_chef.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
-- Uzumaki --
WQ_Inventory.fcRegisterItem("uzumaki_jm", {
    sClassName = "uzumaki_jm",
    sName = "Uzumaki - Jeune Membre",
    sModel = "models/godio/uzumaki_jm.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("goro_uzumaki_g", {
    sClassName = "goro_uzumaki_g",
    sName = "Uzumaki - Jeune Membre 2",
    sModel = "models/godio/goro_uzumaki_g.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("uzumaki_m", {
    sClassName = "uzumaki_m",
    sName = "Uzumaki - Membre",
    sModel = "models/godio/uzumaki_m.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("eclypse_uzumaki_chef", {
    sClassName = "eclypse_uzumaki_chef",
    sName = "Uzumaki - Chef",
    sModel = "models/godio/eclypse_uzumaki_chef.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
-------------------------------------- Amegakure -----------------------------------------
WQ_Inventory.fcRegisterItem("ame_eleve2", {
    sClassName = "ame_eleve2",
    sName = "Amegakure - Eleve Distingué",
    sModel = "models/godio/ame_eleve2.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 1000,
    sVillage = "ame",
    sGrade = "genin1",
})
-- Genin ---
WQ_Inventory.fcRegisterItem("w_ame_genin", {
    sClassName = "w_ame_genin",
    sName = "Ame - Genin Femme",
    sModel = "models/daichi/w_ame_genin.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sVillage = "ame",
    sGrade = "genin1",
})
WQ_Inventory.fcRegisterItem("ame_genin", {
    sClassName = "ame_genin",
    sName = "Amegakure - Genin",
    sModel = "models/godio/ame_genin.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 5620,
    sVillage = "ame",
    sGrade = "genin1",
})
WQ_Inventory.fcRegisterItem("ame_tai_genin", {
    sClassName = "ame_tai_genin",
    sName = "Ame - Taijutsu",
    sModel = "models/daichi/ame_tai_genin.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sVillage = "ame",
    sGrade = "genin1",
})
-- Chunin --
WQ_Inventory.fcRegisterItem("ame_chunin", {
    sClassName = "ame_chunin",
    sName = "Amegakure - Chunin",
    sModel = "models/godio/ame_chunin.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 8240,
    sVillage = "ame",
    sGrade = "chunin1",
})
WQ_Inventory.fcRegisterItem("ame_tai_chunin", {
    sClassName = "ame_tai_chunin",
    sName = "Ame - Taijutsu Chunin",
    sModel = "models/daichi/ame_tai_chunin.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sVillage = "ame",
    sGrade = "chunin1",
})
WQ_Inventory.fcRegisterItem("ame_chunin2", {
    sClassName = "ame_chunin2",
    sName = "Amegakure - Chunin 2",
    sModel = "models/godio/ame_chunin2.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 8240,
    sVillage = "ame",
    sGrade = "chunin1",
})
-- Jonin --
WQ_Inventory.fcRegisterItem("ame_jonin", {
    sClassName = "ame_jonin",
    sName = "Amegakure - Jonin",
    sModel = "models/godio/ame_jonin.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 12870,
    sVillage = "ame",
    sGrade = "jonin",
})
WQ_Inventory.fcRegisterItem("ame_hc", {
    sClassName = "ame_hc",
    sName = "Amegakure - HC Combat",
    sModel = "models/godio/ame_hc.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 20000,
    sVillage = "ame",
    sGrade = "genin",
})
-- Amekage --
WQ_Inventory.fcRegisterItem("ame_amekage", {
    sClassName = "ame_amekage",
    sName = "Amegakure - Kage",
    sModel = "models/godio/ame_amekage.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    iPrice = 15000,
    sVillage = "ame",
    sGrade = "kage",
})
WQ_Inventory.fcRegisterItem("m_medecin_ame", {
    sClassName = "m_medecin_ame",
    sName = "Ame - Medecin",
    sModel = "models/daichi/m_medecin_ame.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("m_medecin_guerre_ame", {
    sClassName = "m_medecin_guerre_ame",
    sName = "Ame - Medecin Guerre",
    sModel = "models/daichi/m_medecin_guerre_ame.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("m_mashodai", {
    sClassName = "m_mashodai",
    sName = "Ame - Mashodai",
    sModel = "models/daichi/m_mashodai.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})

-------------------------------------- Clan Amegakure -----------------------------------------
-- Kaguya --
WQ_Inventory.fcRegisterItem("goro_kaguya_m", {
    sClassName = "goro_kaguya_m",
    sName = "Kaguya - Membre",
    sModel = "models/godio/goro_kaguya_m.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("w_kaguya_genin", {
    sClassName = "w_kaguya_genin",
    sName = "Ame - Femme Kaguya Genin",
    sModel = "models/daichi/w_kaguya_genin.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("m_kaguya_fight", {
    sClassName = "m_kaguya_fight",
    sName = "Ame - Kaguya",
    sModel = "models/daichi/m_kaguya_fight.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("m_kaguya_chunin", {
    sClassName = "m_kaguya_chunin",
    sName = "Ame - Kaguya Chunin",
    sModel = "models/daichi/m_kaguya_chunin.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("eclypse_kaguya_chef", {
    sClassName = "eclypse_kaguya_chef",
    sName = "Kaguya - Chef",
    sModel = "models/godio/eclypse_kaguya_chef.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
-- Kami --
WQ_Inventory.fcRegisterItem("w_kami_genin", {
    sClassName = "w_kami_genin",
    sName = "Ame - Femme Kami Genin",
    sModel = "models/daichi/w_kami_genin.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("goro_kami_m", {
    sClassName = "goro_kami_m",
    sName = "Kami - Membre",
    sModel = "models/godio/goro_kami_m.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("eclypse_kami_chef", {
    sClassName = "eclypse_kami_chef",
    sName = "Kami - Chef",
    sModel = "models/godio/eclypse_kami_chef.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("m_kami1_anthn", {
    sClassName = "m_kami1_anthn",
    sName = "Ame - Kami Chunin",
    sModel = "models/daichi/m_kami1_anthn.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
-- Chinoïke --
WQ_Inventory.fcRegisterItem("goro_chinoike_m", {
    sClassName = "goro_chinoike_m",
    sName = "Chinoïke - Membre",
    sModel = "models/godio/goro_chinoike_m.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("m_chinoike_chunin", {
    sClassName = "m_chinoike_chunin",
    sName = "Ame - Chinoike Chunin",
    sModel = "models/daichi/m_chinoike_chunin.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("m_chinoike_daichi", {
    sClassName = "m_chinoike_daichi",
    sName = "Ame - Chinoike",
    sModel = "models/daichi/m_chinoike_daichi.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("eclypse_chinoike_chef", {
    sClassName = "eclypse_chinoike_chef",
    sName = "Chinoïke - Chef",
    sModel = "models/godio/eclypse_chinoike_chef.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("m_chinoike_amekage", {
    sClassName = "m_chinoike_amekage",
    sName = "Ame - Chinoike Kage",
    sModel = "models/daichi/m_chinoike_amekage.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sVillage = "ame",
    sGrade = "kage",
})
-- Salamandre --
WQ_Inventory.fcRegisterItem("goro_salamandre_m", {
    sClassName = "goro_salamandre_m",
    sName = "Salamandre - Membre",
    sModel = "models/godio/goro_salamandre_m.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("m_salamandre_genin", {
    sClassName = "m_salamandre_genin",
    sName = "Ame - Salamandre Genin",
    sModel = "models/daichi/m_salamandre_genin.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("m_salamandre_fight", {
    sClassName = "m_salamandre_fight",
    sName = "Ame - Salamandre Chunin",
    sModel = "models/daichi/m_salamandre_fight.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("eclypse_salamandre_chef", {
    sClassName = "eclypse_salamandre_chef",
    sName = "Salamandre - Chef",
    sModel = "models/godio/eclypse_salamandre_chef.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
-- Fûma --
WQ_Inventory.fcRegisterItem("goro_fuma_m", {
    sClassName = "goro_fuma_m",
    sName = "Fuma - Membre",
    sModel = "models/godio/goro_fuma_m.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("m_fuma_cap", {
    sClassName = "m_fuma_cap",
    sName = "Ame - Fuma",
    sModel = "models/daichi/m_fuma_cap.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
}) 
WQ_Inventory.fcRegisterItem("m_fuma_ancien", {
    sClassName = "m_fuma_ancien",
    sName = "Ame - Fuma",
    sModel = "models/daichi/m_fuma_ancien.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("m_chunin_fuma", {
    sClassName = "m_chunin_fuma",
    sName = "Ame - Fuma Chunin",
    sModel = "models/daichi/m_chunin_fuma.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("eclypse_fuma_chef", {
    sClassName = "eclypse_fuma_chef",
    sName = "Fûma - Chef",
    sModel = "models/godio/eclypse_fuma_chef.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
WQ_Inventory.fcRegisterItem("m_fuma_chef", {
    sClassName = "m_fuma_chef",
    sName = "Ame - Chef Fuma",
    sModel = "models/daichi/m_fuma_chef.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
})
-------------------------------------- Neutre -----------------------------------------
WQ_Inventory.fcRegisterItem("neutre_robe", {
    sClassName = "neutre_robe",
    sName = "Neutre - Robe",
    sModel = "models/godio/neutre_robe.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    sRarity = "rare",
    iPrice = 47800,
})
WQ_Inventory.fcRegisterItem("man_longcoat_03", {
    sClassName = "man_longcoat_03",
    sName = "Neutre - Impérial",
    sModel = "models/LVM/bodies/man_longcoat_03.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sRarity = "rare",
})
WQ_Inventory.fcRegisterItem("neutre_1", {
    sClassName = "neutre_1",
    sName = "Neutre - D'argent",
    sModel = "models/godio/neutre_1.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 8745,

})
WQ_Inventory.fcRegisterItem("neutre_2", {
    sClassName = "neutre_2",
    sName = "Neutre - Détente",
    sModel = "models/godio/neutre_2.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 6983,
})
WQ_Inventory.fcRegisterItem("neutre_4", {
    sClassName = "neutre_4",
    sName = "Neutre - Voyageur",
    sModel = "models/godio/neutre_4.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 7130,
})
WQ_Inventory.fcRegisterItem("neutre_w", {
    sClassName = "neutre_w",
    sName = "Neutre - Voyageuse",
    sModel = "models/godio/neutre_w.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    iPrice = 7130,
})
WQ_Inventory.fcRegisterItem("neutre_3", {
    sClassName = "neutre_3",
    sName = "Neutre - Gilet simple",
    sModel = "models/godio/neutre_3.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    sRarity = "epic",
    iPrice = 64852,
})
WQ_Inventory.fcRegisterItem("neutre_5", {
    sClassName = "neutre_5",
    sName = "Neutre - Gilet simple rouge",
    sModel = "models/godio/neutre_5.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    sRarity = "epic",
    iPrice = 64852,
})
WQ_Inventory.fcRegisterItem("neutre_7", {
    sClassName = "neutre_7",
    sName = "Neutre - Costard cravatte",
    sModel = "models/godio/neutre_7.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    sRarity = "rare",
    iPrice = 10264,
})
WQ_Inventory.fcRegisterItem("neutre_8", {
    sClassName = "neutre_8",
    sName = "Neutre - Simplet",
    sModel = "models/godio/neutre_8.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    sRarity = "rare",
    iPrice = 8460,
})
WQ_Inventory.fcRegisterItem("neutre_13", {
    sClassName = "neutre_13",
    sName = "Neutre - Combatant",
    sModel = "models/godio/neutre_13.mdl",
    sItemType = "cloth",
    bIsBuyyable = true,
    sRarity = "rare",
    iPrice = 11378,
})
WQ_Inventory.fcRegisterItem("neutre_6", {
    sClassName = "neutre_6",
    sName = "Neutre - Tenue Lunaire",
    sModel = "models/godio/neutre_6.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sRarity = "epic",
})
WQ_Inventory.fcRegisterItem("neutre_9", {
    sClassName = "neutre_9",
    sName = "Neutre - Tenue en Cuir",
    sModel = "models/godio/neutre_9.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sRarity = "rare",
})
WQ_Inventory.fcRegisterItem("neutre_11", {
    sClassName = "neutre_11",
    sName = "Neutre - Tenue du Vent Caché",
    sModel = "models/godio/neutre_11.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sRarity = "epic",
})
WQ_Inventory.fcRegisterItem("neutre_12", {
    sClassName = "neutre_12",
    sName = "Neutre - Tenue Nomade",
    sModel = "models/godio/neutre_12.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sRarity = "epic",
})
WQ_Inventory.fcRegisterItem("neutre_14", {
    sClassName = "neutre_14",
    sName = "Neutre - Tenue Bleu Cyan",
    sModel = "models/godio/neutre_14.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sRarity = "rare",
})
WQ_Inventory.fcRegisterItem("neutre_15", {
    sClassName = "neutre_15",
    sName = "Neutre - Tenue Bleu Nocturne",
    sModel = "models/godio/neutre_15.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sRarity = "rare",
})
WQ_Inventory.fcRegisterItem("neutre_16", {
    sClassName = "neutre_15",
    sName = "Neutre - Tenue Vert Kaki",
    sModel = "models/godio/neutre_16.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sRarity = "rare",
})
WQ_Inventory.fcRegisterItem("neutre_17", {
    sClassName = "neutre_17",
    sName = "Neutre - Tenue Tactique Bleu",
    sModel = "models/godio/neutre_17.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sRarity = "rare",
})
WQ_Inventory.fcRegisterItem("neutre_18", {
    sClassName = "neutre_18",
    sName = "Neutre - Tenue Tactique Verte",
    sModel = "models/godio/neutre_18.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sRarity = "rare",
})
WQ_Inventory.fcRegisterItem("neutre_19", {
    sClassName = "neutre_19",
    sName = "Neutre - Tenue Tactique Sombre",
    sModel = "models/godio/neutre_19.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sRarity = "epic",
})
WQ_Inventory.fcRegisterItem("neutre_20", {
    sClassName = "neutre_20",
    sName = "Neutre - Tenue Armure Nocturne",
    sModel = "models/godio/neutre_20.mdl",
    sItemType = "cloth",
    bIsBuyyable = false,
    sRarity = "legendary",
})
--------------- Bois ----------------

WQ_Inventory.fcRegisterItem("planche_de_bois_brut", {
    sClassName = "planche_de_bois_brut",
    sName = "Planche de bois brut",
    sMaterial = "planche_de_bois_brut",
    sItemType = "farm",
})

WQ_Inventory.fcRegisterItem("bois_renforce", {
    sClassName = "bois_renforce",
    sName = "Bois renforcé",
    sMaterial = "bois_renforce",
    sRarity = "rare",
    sItemType = "farm",
})

WQ_Inventory.fcRegisterItem("bois_ancestral", {
    sClassName = "bois_ancestral",
    sName = "Bois ancestral",
    sMaterial = "bois_ancestral",
    sRarity = "epic",
    sItemType = "farm",
})

WQ_Inventory.fcRegisterItem("bois_arbre_sacre", {
    sClassName = "bois_arbre_sacre",
    sName = "Bois de l'arbre sacré",
    sMaterial = "bois_arbre_sacre",
    sRarity = "legendary",
    sItemType = "farm",
})


--------------- FER ----------------


WQ_Inventory.fcRegisterItem("minerai_de_fer_brut", {
    sClassName = "minerai_de_fer_brut",
    sName = "Minerai de fer brut",
    sMaterial = "minerai_de_fer_brut",
    sItemType = "farm",
})


WQ_Inventory.fcRegisterItem("lingot_de_fer_purifie", {
    sClassName = "lingot_de_fer_purifie",
    sName = "Lingot de fer purifié",
    sMaterial = "lingot_de_fer_purifie",
    sRarity = "rare",
    sItemType = "farm",
})

WQ_Inventory.fcRegisterItem("lingot_de_fer_maudit", {
    sClassName = "lingot_de_fer_maudit",
    sName = "Lingot de fer maudit",
    sMaterial = "lingot_de_fer_maudit",
    sRarity = "epic",
    sItemType = "farm",
})

WQ_Inventory.fcRegisterItem("fer_des_etoiles", {
    sClassName = "fer_des_etoiles",
    sName = "Fer des étoiles",
    sMaterial = "fer_des_etoiles",
    sRarity = "legendary",
    sItemType = "farm",
})


--------------- TISSU ----------------

WQ_Inventory.fcRegisterItem("tissu_simple", {
    sClassName = "tissu_simple",
    sName = "Tissu simple",
    sMaterial = "tissu_simple",
    sItemType = "farm",
})

WQ_Inventory.fcRegisterItem("tissu_raffine", {
    sClassName = "tissu_raffine",
    sName = "Tissu raffiné",
    sMaterial = "tissu_raffine",
    sRarity = "rare",
    sItemType = "farm",
})

WQ_Inventory.fcRegisterItem("tissu_spirituel", {
    sClassName = "tissu_spirituel",
    sName = "Tissu spirituel",
    sMaterial = "tissu_spirituel",
    sRarity = "epic",
    sItemType = "farm",
})

WQ_Inventory.fcRegisterItem("tissu_chakra_condense", {
    sClassName = "tissu_chakra_condense",
    sName = "Tissu de chakra condensé",
    sMaterial = "tissu_chakra_condense",
    sRarity = "legendary",
    sItemType = "farm",
})



--------------- SOIE ----------------



WQ_Inventory.fcRegisterItem("fil_de_soie_basique", {
    sClassName = "fil_de_soie_basique",
    sName = "Fil de soie basique",
    sMaterial = "fil_de_soie_basique",
    sItemType = "farm",
})

WQ_Inventory.fcRegisterItem("fibres_de_chanvre", {
    sClassName = "fibres_de_chanvre",
    sName = "Fibres de chanvre",
    sMaterial = "fibres_de_chanvre",
    sRarity = "rare",
    sItemType = "farm",
})


WQ_Inventory.fcRegisterItem("soie_celeste", {
    sClassName = "soie_celeste",
    sName = "Soie céleste",
    sMaterial = "soie_celeste",
    sRarity = "epic",
    sItemType = "farm",
})

WQ_Inventory.fcRegisterItem("soie_araignee_demoniaque", {
    sClassName = "soie_araignee_demoniaque",
    sName = "Soie du fil d'araignée démoniaque",
    sMaterial = "soie_araignee_demoniaque",
    sRarity = "legendary",
    sItemType = "farm",
})






WQ_Inventory.fcRegisterItem("casque_t1", {
    sClassName = "casque_t1",
    sName = "Casque T1",
    sMaterial = "item_casque",
    sItemType = "helmet",
    bRemoveOnDeath = true,
    tBoost = {
        ["Resistance"] = 4
    }
})

WQ_Inventory.fcRegisterItem("plastron_t1", {
    sClassName = "plastron_t1",
    sName = "Plastron T1",
    sMaterial = "item_plastron",
    sItemType = "armor",
    bRemoveOnDeath = true,
    tBoost = { ["Health"] = 5 }
})

WQ_Inventory.fcRegisterItem("pantalon_t1", {
    sClassName = "pantalon_t1",
    sName = "Pantalon T1",
    sMaterial = "item_pantalon",
    sItemType = "pant",
    bRemoveOnDeath = true,
    tBoost = { ["Speed"] = 10 }
})

WQ_Inventory.fcRegisterItem("bottes_t1", {
    sClassName = "bottes_t1",
    sName = "Bottes T1",
    sMaterial = "item_bottes",
    sItemType = "shoes",
    bRemoveOnDeath = true,
    tBoost = { ["Chakra"] = 10 }
})

-- Tier 2
WQ_Inventory.fcRegisterItem("casque_t2", {
    sClassName = "casque_t2",
    sName = "Casque T2",
    sMaterial = "item_casque",
    sItemType = "helmet",
    sRarity = "rare",
    bRemoveOnDeath = true,
    tBoost = { ["Resistance"] = 8 }
})

WQ_Inventory.fcRegisterItem("plastron_t2", {
    sClassName = "plastron_t2",
    sName = "Plastron T2",
    sMaterial = "item_plastron",
    sItemType = "armor",
    sRarity = "rare",
    bRemoveOnDeath = true,
    tBoost = { ["Health"] = 10 }
})

WQ_Inventory.fcRegisterItem("pantalon_t2", {
    sClassName = "pantalon_t2",
    sName = "Pantalon T2",
    sMaterial = "item_pantalon",
    sItemType = "pant",
    sRarity = "rare",
    bRemoveOnDeath = true,
    tBoost = { ["Speed"] = 20 }
})

WQ_Inventory.fcRegisterItem("bottes_t2", {
    sClassName = "bottes_t2",
    sName = "Bottes T2",
    sMaterial = "item_bottes",
    sItemType = "shoes",
    sRarity = "rare",
    bRemoveOnDeath = true,
    tBoost = { ["Chakra"] = 15 }
})

-- Tier 3
WQ_Inventory.fcRegisterItem("casque_t3", {
    sClassName = "casque_t3",
    sName = "Casque T3",
    sMaterial = "item_casque",
    sRarity = "epic",
    sItemType = "helmet",
    bRemoveOnDeath = true,
    tBoost = { ["Resistance"] = 12 }
})

WQ_Inventory.fcRegisterItem("plastron_t3", {
    sClassName = "plastron_t3",
    sName = "Plastron T3",
    sRarity = "epic",
    sMaterial = "item_plastron",
    sItemType = "armor",
    bRemoveOnDeath = true,
    tBoost = { ["Health"] = 15 }
})

WQ_Inventory.fcRegisterItem("pantalon_t3", {
    sClassName = "pantalon_t3",
    sName = "Pantalon T3",
    sRarity = "epic",
    sMaterial = "item_pantalon",
    sItemType = "pant",
    bRemoveOnDeath = true,
    tBoost = { ["Speed"] = 30 }
})

WQ_Inventory.fcRegisterItem("bottes_t3", {
    sClassName = "bottes_t3",
    sName = "Bottes T3",
    sRarity = "epic",
    sMaterial = "item_bottes",
    sItemType = "shoes",
    bRemoveOnDeath = true,
    tBoost = { ["Chakra"] = 25 }
})

-- Tier 4
WQ_Inventory.fcRegisterItem("casque_t4", {
    sClassName = "casque_t4",
    sName = "Casque T4",
    sRarity = "legendary",
    sMaterial = "item_casque",
    sItemType = "helmet",
    bRemoveOnDeath = true,
    tBoost = { ["Resistance"] = 15 }
})

WQ_Inventory.fcRegisterItem("sharigan_1_left", {
    sClassName = "sharigan_1_left",
    sName = "Sharingan 1 Droit",
    sRarity = "legendary",
    sMaterial = "sharingan1",
    sItemType = "eye",
    bRemoveOnDeath = false,
    tBoost = { ["DojutsuID"] = 123, ["DojutsuPlacement"] = 1, ["DojutsuLevel"] = 1 }
})

WQ_Inventory.fcRegisterItem("sharigan_2_left", {
    sClassName = "sharigan_2_left",
    sName = "Sharingan 2 Droit",
    sRarity = "legendary",
    sMaterial = "sharingan2",
    sItemType = "eye",
    bRemoveOnDeath = false,
    tBoost = { ["DojutsuID"] = 123, ["DojutsuPlacement"] = 1, ["DojutsuLevel"] = 2, ["DojutsuClan"] = 2 }
})

WQ_Inventory.fcRegisterItem("sharigan_3_left", {
    sClassName = "sharigan_2_left",
    sName = "Sharingan 3 Droit",
    sRarity = "legendary",
    sMaterial = "sharingan3",
    sItemType = "eye",
    bRemoveOnDeath = false,
    tBoost = { ["DojutsuID"] = 123, ["DojutsuPlacement"] = 1, ["DojutsuLevel"] = 3 }
})

WQ_Inventory.fcRegisterItem("plastron_t4", {
    sClassName = "plastron_t4",
    sName = "Plastron T4",
    sRarity = "legendary",
    sMaterial = "item_plastron",
    sItemType = "armor",
    bRemoveOnDeath = true,
    tBoost = { ["Health"] = 20}
})

WQ_Inventory.fcRegisterItem("pantalon_t4", {
    sClassName = "pantalon_t4",
    sName = "Pantalon T4",
    sRarity = "legendary",
    sMaterial = "item_pantalon",
    sItemType = "pant",
    bRemoveOnDeath = true,
    tBoost = { ["Speed"] = 40 }
})

WQ_Inventory.fcRegisterItem("bottes_t4", {
    sClassName = "bottes_t4",
    sName = "Bottes T4",
    sRarity = "legendary",
    sMaterial = "item_bottes",
    sItemType = "shoes",
    bRemoveOnDeath = true,
    tBoost = {
        ["Chakra"] = 30,
    }
})





WQ_Inventory.fcRegisterItem("WQ_masque_custom_demi", {
    sClassName = "WQ_masque_custom_demi",
    sName = "Masque Custom Demi",
    sModel = "models/falko_naruto_foc/mask/custom_mask_half_01.mdl",
    sRarity = "rare",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 3360,

    aAngle = Angle(-180, 90, 90),
    vPos = Vector(2.9, -3.3, -0.2),
})
WQ_Inventory.fcRegisterItem("WQ_masque_anbu_29", {
    sClassName = "WQ_masque_anbu_29",
    sName = "Masque du Corbeau Sombre",
    sModel = "models/oldjimmy/naruto/mask/mask29.mdl",
    sRarity = "rare",
    sItemType = "mask",
    bIsBuyyable = true,

    bIsBuyyable = true,
    iPrice = 3360,

    aAngle = Angle(-180, 90, 90),
    vPos = Vector(2.9, -2.1, -0.4),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_3", {
    sClassName = "WQ_masque_anbu_3",
    sName = "Masque du Tigre Silencieux",
    sModel = "models/oldjimmy/naruto/mask/mask3.mdl",
    --sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 1680,

    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_30", {
    sClassName = "WQ_masque_anbu_30",
    sName = "Masque demoniaque",
    sModel = "models/oldjimmy/naruto/mask/mask30.mdl",
    sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 6720,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_31", {
    sClassName = "WQ_masque_anbu_31",
    sName = "Masque du Démon Hypnotique",
    sModel = "models/oldjimmy/naruto/mask/mask31.mdl",
    sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 6720,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_4", {
    sClassName = "WQ_masque_anbu_4",
    sName = "Demi masque felin",
    sModel = "models/oldjimmy/naruto/mask/mask4.mdl",
    --sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 1680,

    tScale = {
        ["eleve"] = 0.4,
        ["genin1"] = 0.45,
        ["genin2"] = 0.5,
        ["chunin1"] = 0.55,
        ["chunin2"] = 0.57,
        ["chunin3"] = 0.6,
        ["grandchunin"] = 0.7,
        ["jonin"] = 0.75,
        ["commandant"] = 0.85,
        ["conseiller"] = 0.85,
        ["kage"] = 0.9,
    },
    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})


WQ_Inventory.fcRegisterItem("WQ_masque_anbu_5", {
    sClassName = "WQ_masque_anbu_5",
    sName = "Demi masque tigre",
    sModel = "models/oldjimmy/naruto/mask/mask5.mdl",
    --sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 6720,

    tScale = {
        ["eleve"] = 0.4,
        ["genin1"] = 0.45,
        ["genin2"] = 0.5,
        ["chunin1"] = 0.55,
        ["chunin2"] = 0.57,
        ["chunin3"] = 0.6,
        ["grandchunin"] = 0.7,
        ["jonin"] = 0.75,
        ["commandant"] = 0.85,
        ["conseiller"] = 0.85,
        ["kage"] = 0.9,
    },
    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_6", {
    sClassName = "WQ_masque_anbu_6",
    sName = "Demi masque chat",
    sModel = "models/oldjimmy/naruto/mask/mask6.mdl",
    --sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 1680,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_9", {
    sClassName = "WQ_masque_anbu_9",
    sName = "Masque bagarreur",
    sModel = "models/oldjimmy/naruto/mask/mask9.mdl",
    --sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 1680,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_8", {
    sClassName = "WQ_masque_anbu_8",
    sName = "col rouge",
    sModel = "models/oldjimmy/naruto/mask/mask8.mdl",
    sRarity = "rare",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 3360,

    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_resp_8", {
    sClassName = "WQ_masque_resp_8",
    sName = "Masque respiration",
    sModel = "models/accessories_naruto_geams/jack/solve/solve_naruto_accessories/mask_hanzou.mdl",
    sRarity = "rare",
    sItemType = "mask",
    bIsBuyyable = false,
    iPrice = 3360,

    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2, -1.5, 0),
})

WQ_Inventory.fcRegisterItem("diesel_masque_goat", {
    sClassName = "diesel_masque_goat",
    sName = "Masque dragon",
    sModel = "models/mask_solve/mask01_solve.mdl",
    sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 6720,

    aAngle = Angle(90, 90, 90),
    vPos = Vector(2.9, -3.2, -0.2),
})

WQ_Inventory.fcRegisterItem("WQ_masque_generique", {
    sClassName = "WQ_masque_generique",
    sName = "Masque loup",
    sModel = "models/mask/mask.mdl",
    --sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 1680,


    aAngle = Angle(-180, 85.9, 85.9),
    vPos = Vector(3.9, -1.4, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_generique_2", {
    sClassName = "WQ_masque_generique_2",
    sName = "Masque lapin",
    sModel = "models/mask/mask2.mdl",
    -- sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 1680,

    aAngle = Angle(-180, 85.9, 85.9),
    vPos = Vector(3.9, -1.4, -0.1),
})
WQ_Inventory.fcRegisterItem("WQ_masque_anbu_26", {
    sClassName = "WQ_masque_anbu_26",
    sName = "Masque de poulet",
    sModel = "models/oldjimmy/naruto/mask/mask26.mdl",
    --sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 1680,

    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_27", {
    sClassName = "WQ_masque_anbu_27",
    sName = "Masque koala",
    sModel = "models/oldjimmy/naruto/mask/mask27.mdl",
    --sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 1680,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_28", {
    sClassName = "WQ_masque_anbu_28",
    sName = "Masque du Veilleur Violet",
    sModel = "models/oldjimmy/naruto/mask/mask28.mdl",
    sRarity = "legendary",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 13440,

    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})
WQ_Inventory.fcRegisterItem("WQ_masque_anbu_12", {
    sClassName = "WQ_masque_anbu_12",
    sName = "Masque lapin neon",
    sModel = "models/oldjimmy/naruto/mask/mask12.mdl",
    sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 6720,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_13", {
    sClassName = "WQ_masque_anbu_13",
    sName = "Masque obito",
    sModel = "models/oldjimmy/naruto/mask/mask13.mdl",
    sRarity = "legendary",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 13440,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_14", {
    sClassName = "WQ_masque_anbu_14",
    sName = "Masque obito abimée",
    sModel = "models/oldjimmy/naruto/mask/mask14.mdl",
    sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 6720,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_15", {
    sClassName = "WQ_masque_anbu_15",
    sName = "Masque du Démon Hypnotique blanc",
    sModel = "models/oldjimmy/naruto/mask/mask15.mdl",
    sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 6720,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_16", {
    sClassName = "WQ_masque_anbu_16",
    sName = "Masque Shinigami violet",
    sModel = "models/oldjimmy/naruto/mask/mask16.mdl",
    sRarity = "legendary",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 13440,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_16_noir", {
    sClassName = "WQ_masque_anbu_16_noir",
    sName = "Masque Shinigami noir",
    sModel = "models/oldjimmy/naruto/mask/mask16_black.mdl",
    sRarity = "legendary",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 13440,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_17", {
    sClassName = "WQ_masque_anbu_17",
    sName = "Masque hiboux",
    sModel = "models/oldjimmy/naruto/mask/mask17.mdl",
    sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 6720,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_18", {
    sClassName = "WQ_masque_anbu_18",
    sName = "Masque du Troisième Œil",
    sModel = "models/oldjimmy/naruto/mask/mask18.mdl",
    --sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 1680,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),

})
WQ_Inventory.fcRegisterItem("WQ_masque_anbu_20", {
    sClassName = "WQ_masque_anbu_20",
    sName = "Masque vert",
    sModel = "models/oldjimmy/naruto/mask/mask20.mdl",
    --sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 1680,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_21", {
    sClassName = "WQ_masque_anbu_21",
    sName = "Masque corbeau blanc",
    sModel = "models/oldjimmy/naruto/mask/mask21.mdl",
    sRarity = "rare",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 3360,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_22", {
    sClassName = "WQ_masque_anbu_22",
    sName = "Masque ours rouge",
    sModel = "models/oldjimmy/naruto/mask/mask22.mdl",
    --sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 1680,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_23", {
    sClassName = "WQ_masque_anbu_23",
    sName = "Masque maitre poulet",
    sModel = "models/oldjimmy/naruto/mask/mask23.mdl",
    sRarity = "rare",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 3360,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_24", {
    sClassName = "WQ_masque_anbu_24",
    sName = "Masque triste vert",
    sModel = "models/oldjimmy/naruto/mask/mask24.mdl",
    --sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 1680,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_anbu_25", {
    sClassName = "WQ_masque_anbu_25",
    sName = "Masque koala violet",
    sModel = "models/oldjimmy/naruto/mask/mask25.mdl",
    --sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,
    iPrice = 1680,


    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})

WQ_Inventory.fcRegisterItem("WQ_masque_diesel", {
    sClassName = "WQ_masque_diesel",
    sName = "Masque du Démon Hypnotique noir",
    sModel = "models/models/props/mao/models/m_mask_demon_v_daichi.mdl",
    sRarity = "epic",
    sItemType = "mask",
    bIsBuyyable = true,

    iPrice = 3360,

    aAngle = Angle(-180, 94.1, 90),
    vPos = Vector(2.8, -3.7, -0.1),
})







------------------------------------ KENJUTSU ------------------------------------

WQ_Inventory.fcRegisterItem("kenjutsu_common_ninja_katana", {
    sClassName = "kenjutsu_common_ninja_katana",
    sName = "K - Katana",
    sModel = "models/skylyxx/ctg/props/swords/ninja_katana.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(143.8, 0, 0),
    vPos = Vector(22.7, -5.5, 6.6),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana1", {
    sClassName = "m_katana1",
    sName = "Katana des Ailes de Feu",
    sModel = "models/models/props/daichi/m_katana1.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana2", {
    sClassName = "m_katana2",
    sName = "Katana du Croc de Foudre",
    sModel = "models/models/props/daichi/m_katana2.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana3", {
    sClassName = "m_katana3",
    sName = "Katana de la Vérité",
    sModel = "models/models/props/daichi/m_katana3.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana4", {
    sClassName = "m_katana4",
    sName = "Katana des Larmes des Abysses",
    sModel = "models/models/props/daichi/m_katana4.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana5", {
    sClassName = "m_katana5",
    sName = "Katana de l'Ombre de la Lune",
    sModel = "models/models/props/daichi/m_katana5.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana6", {
    sClassName = "m_katana6",
    sName = "Katana de la Fin Obscure",
    sModel = "models/models/props/daichi/m_katana6.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana7", {
    sClassName = "m_katana7",
    sName = "Katana du Tranchant Infini",
    sModel = "models/models/props/daichi/m_katana7.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana8", {
    sClassName = "m_katana8",
    sName = "Katana de la Mélodie du Vent",
    sModel = "models/models/props/daichi/m_katana8.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana9", {
    sClassName = "m_katana9",
    sName = "Katana du Cœur des Ténèbres",
    sModel = "models/models/props/daichi/m_katana9.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(143.8, 0, 0),
    vPos = Vector(22.7, -5.5, 6.6),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana10", {
    sClassName = "m_katana10",
    sName = "Katana du Dragon Céleste Sacré",
    sModel = "models/models/props/daichi/m_katana10.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana11", {
    sClassName = "m_katana11",
    sName = "Katana du Chemin de l'Agonie",
    sModel = "models/models/props/daichi/m_katana11.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana12", {
    sClassName = "m_katana12",
    sName = "Katana du Démon Yasha",
    sModel = "models/models/props/daichi/m_katana12.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana13", {
    sClassName = "m_katana13",
    sName = "Katana des Pluie Silencieuse",
    sModel = "models/models/props/daichi/m_katana13.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana14", {
    sClassName = "m_katana14",
    sName = "Katana du Miroir de l'âme",
    sModel = "models/models/props/daichi/m_katana14.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_kenjutsu_bamboo", {
    sClassName = "m_kenjutsu_bamboo",
    sName = "Bamboo",
    sModel = "models/models/props/daichi/m_kenjutsu_bamboo.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 6
})

WQ_Inventory.fcRegisterItem("m_katana15", {
    sClassName = "m_katana15",
    sName = "Katana de la Glace de la Terre",
    sModel = "models/models/props/daichi/m_katana15.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana16", {
    sClassName = "m_katana16",
    sName = "Katana du Dieu Dragon",
    sModel = "models/models/props/daichi/m_katana16.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana17", {
    sClassName = "m_katana17",
    sName = "Katana du Tigre Blanc",
    sModel = "models/models/props/daichi/m_katana17.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana18", {
    sClassName = "m_katana18",
    sName = "Katana des Larmes du Démon",
    sModel = "models/models/props/daichi/m_katana18.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana19", {
    sClassName = "m_katana19",
    sName = "Katana de la Lumière Pure",
    sModel = "models/models/props/daichi/m_katana19.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana20", {
    sClassName = "m_katana20",
    sName = "Katana de l'Energie de Vie",
    sModel = "models/models/props/daichi/m_katana20.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("m_katana21", {
    sClassName = "m_katana21",
    sName = "Katana de la Brume Violette",
    sModel = "models/models/props/daichi/m_katana21.mdl",
    sItemType = "weapon",
    iRangeMultiplicator = 1.5,
    iDamage = 40,
 
    aAngle = Angle(-156.2, -180, -1),
    vPos = Vector(17.8, -6.3, 6.3),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})


WQ_Inventory.fcRegisterItem("kenjutsu_legend_hiramekarei", {
    sClassName = "kenjutsu_legend_hiramekarei",
    sName = "K - Hiramekarei",
    sModel = "models/skylyxx/ctg/props/swords/hiramekarei.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_legend_kabutowari", {
    sClassName = "kenjutsu_legend_kabutowari",
    sName = "K - Kabutowari",
    sModel = "models/skylyxx/ctg/props/swords/kabutowari_right.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_legend_kiba", {
    sClassName = "kenjutsu_legend_kiba",
    sName = "K - Kiba",
    sModel = "models/skylyxx/ctg/props/swords/kiba.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_legend_kubikiribocho", {
    sClassName = "kenjutsu_legend_kubikiribocho",
    sName = "K - KubikiribÃ´chÃ´",
    sModel = "models/skylyxx/ctg/props/swords/kubikiribocho.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_legend_kurosawa", {
    sClassName = "kenjutsu_legend_kurosawa",
    sName = "K - Kurosawa",
    sModel = "models/skylyxx/ctg/props/swords/kurosawa.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_legend_nuibari", {
    sClassName = "kenjutsu_legend_nuibari",
    sName = "K - Nuibari",
    sModel = "models/skylyxx/ctg/props/swords/nuibari.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_legend_samehada", {
    sClassName = "kenjutsu_legend_samehada",
    sName = "K - Samehada",
    sModel = "models/skylyxx/ctg/props/swords/samehada.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_legend_shibuki", {
    sClassName = "kenjutsu_legend_shibuki",
    sName = "K - Shibuki",
    sModel = "models/skylyxx/ctg/props/swords/shibuki.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_legend_stars", {
    sClassName = "kenjutsu_legend_stars",
    sName = "K - Ã‰pÃ©e des 7 Ã‰toiles",
    sModel = "models/skylyxx/ctg/props/swords/seven_stars.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_rare_couperet", {
    sClassName = "kenjutsu_rare_couperet",
    sName = "K - Couperet",
    sModel = "models/skylyxx/ctg/props/swords/couperet.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_rare_dague_kakashi", {
    sClassName = "kenjutsu_rare_dague_kakashi",
    sName = "K - Sabre de Chakra Blanc",
    sModel = "models/skylyxx/ctg/props/swords/dague_kakashi.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_rare_emeraude", {
    sClassName = "kenjutsu_rare_emeraude",
    sName = "K - Ã‰meraude",
    sModel = "models/skylyxx/ctg/props/swords/emeraude.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_rare_hashira", {
    sClassName = "kenjutsu_rare_hashira",
    sName = "K - Hashira",
    sModel = "models/skylyxx/ctg/props/swords/hashira.mdl",
    sItemType = "weapon",
    sRarity = "rare",
})

WQ_Inventory.fcRegisterItem("kenjutsu_rare_hidan_faux", {
    sClassName = "kenjutsu_rare_hidan_faux",
    sName = "K - Faux de Jashin",
    sModel = "models/skylyxx/ctg/props/swords/hidan_faux.mdl",
    sItemType = "weapon",
    sRarity = "legendary",
})

WQ_Inventory.fcRegisterItem("kenjutsu_rare_kamakura", {
    sClassName = "kenjutsu_rare_kamakura",
    sName = "K - Kamakura",
    sModel = "models/skylyxx/ctg/props/swords/kamakura.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_rare_kotetsu", {
    sClassName = "kenjutsu_rare_kotetsu",
    sName = "K - KÃ´tetsu",
    sModel = "models/skylyxx/ctg/props/swords/kotetsu.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_rare_kusanagi", {
    sClassName = "kenjutsu_rare_kusanagi",
    sName = "K - Kusanagi",
    sModel = "models/skylyxx/ctg/props/swords/kusanagi.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_rare_lame_feu", {
    sClassName = "kenjutsu_rare_lame_feu",
    sName = "K - Lame de Feu",
    sModel = "models/skylyxx/ctg/props/swords/lame_feu.mdl",
    sItemType = "weapon",
    aAngle = Angle(21.7, 180, -3.1),
    vPos = Vector(19, -5.2, 8.6),
    sBone = "ValveBiped.Bip01_Spine1",

})

WQ_Inventory.fcRegisterItem("kenjutsu_rare_meito", {
    sClassName = "kenjutsu_rare_meito",
    sName = "K - Meito",
    sModel = "models/skylyxx/ctg/props/swords/meito.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_rare_nunchakus", {
    sClassName = "kenjutsu_rare_nunchakus",
    sName = "K - Nunchakus",
    sModel = "models/skylyxx/ctg/props/swords/nunchakus.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_rare_tanto_bee", {
    sClassName = "kenjutsu_rare_tanto_bee",
    sName = "K - Tanto (Bee)",
    sModel = "models/skylyxx/ctg/props/swords/tanto_bee.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_common_anbu_tanto", {
    sClassName = "kenjutsu_common_anbu_tanto",
    sName = "K - Tanto (Anbu)",
    sModel = "models/ayko/ctg/characters/man/body/commun_tanto_anbu_v2.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_common_kama", {
    sClassName = "kenjutsu_common_kama",
    sName = "K - Kama",
    sModel = "models/skylyxx/ctg/props/swords/kama.mdl",
    sItemType = "weapon",

    aAngle = Angle(5.2, 30, 77.6),
    vPos = Vector(0.6, -4, -5.2),
    sBone = "ValveBiped.Bip01_R_Thigh"
})

WQ_Inventory.fcRegisterItem("kenjutsu_common_kama_vip", {
    sClassName = "kenjutsu_common_kama_vip",
    sName = "K - Kama VIP",
    sModel = "models/ayko/ctg/characters/man/body/vip_kama.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_common_ninja_dague", {
    sClassName = "kenjutsu_common_ninja_dague",
    sName = "K - Dague",
    sModel = "models/skylyxx/ctg/props/swords/ninja_dague.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_common_ninja_dague_vip", {
    sClassName = "kenjutsu_common_ninja_dague_vip",
    sName = "K - Dague VIP",
    sModel = "models/ayko/ctg/characters/man/body/vip_dague.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_common_ninja_griffes", {
    sClassName = "kenjutsu_common_ninja_griffes",
    sName = "K - Griffes",
    sModel = "models/skylyxx/ctg/props/swords/ninja_griffes.mdl",
    sItemType = "weapon",
    aAngle = Angle(5.2, 30, 77.6),
    vPos = Vector(0.6, -4, -5.2),
    sBone = "ValveBiped.Bip01_R_Thigh"

})

WQ_Inventory.fcRegisterItem("kenjutsu_common_ninja_griffes_vip", {
    sClassName = "kenjutsu_common_ninja_griffes_vip",
    sName = "K - Griffes VIP",
    sModel = "models/ayko/ctg/characters/man/body/vip_griffes.mdl",
    sItemType = "weapon",
})


WQ_Inventory.fcRegisterItem("kenjutsu_common_ninja_katana_vip", {
    sClassName = "kenjutsu_common_ninja_katana_vip",
    sName = "K - Katana VIP",
    sModel = "models/ayko/ctg/characters/man/body/vip_sword.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_common_ninja_lance", {
    sClassName = "kenjutsu_common_ninja_lance",
    sName = "K - Lance",
    sModel = "models/skylyxx/ctg/props/swords/ninja_lance.mdl",
    sItemType = "weapon",
    aAngle = Angle(5.2, 30, 77.6),
    vPos = Vector(0.6, -4, -5.2),
    sBone = "ValveBiped.Bip01_R_Thigh"
})

WQ_Inventory.fcRegisterItem("kenjutsu_common_ninja_lance_vip", {
    sClassName = "kenjutsu_common_ninja_lance_vip",
    sName = "K - Lance VIP",
    sModel = "models/ayko/ctg/characters/man/body/vip_lance.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_common_ninja_massue", {
    sClassName = "kenjutsu_common_ninja_massue",
    sName = "K - Massue",
    sModel = "models/skylyxx/ctg/props/swords/ninja_massue.mdl",
    sItemType = "weapon",

    aAngle = Angle(143.8, 0, 0),
    vPos = Vector(22.7, -5.5, 6.6),
    sBone = "ValveBiped.Bip01_Spine2",
    iScaleReductor = 0.93534482758621
})

WQ_Inventory.fcRegisterItem("kenjutsu_common_ninja_massue_vip", {
    sClassName = "kenjutsu_common_ninja_massue_vip",
    sName = "K - Massue",
    sModel = "models/ayko/ctg/characters/man/body/vip_axe.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_legend_baton", {
    sClassName = "kenjutsu_legend_baton",
    sName = "K - BÃ¢ton de la VÃ©ritÃ©",
    sModel = "models/skylyxx/ctg/props/swords/adamantine.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_legend_foudre", {
    sClassName = "kenjutsu_legend_foudre",
    sName = "K - Ã‰pÃ©e du Dieu de la Foudre",
    sModel = "models/skylyxx/ctg/props/swords/foudre_dieu.mdl",
    sItemType = "weapon",
})

WQ_Inventory.fcRegisterItem("kenjutsu_legend_gunbai", {
    sClassName = "kenjutsu_legend_gunbai",
    sName = "K - Gunbai",
    sModel = "models/skylyxx/ctg/props/swords/gunbai.mdl",
    sItemType = "weapon",
})


WQ_Inventory.fcRegisterItem("kenjutsu_boutique_epee36", {
    sClassName = "kenjutsu_boutique_epee36",
    sName = "Epée #36",
    sModel = "models/naruto/epee/epee36/foc_nr_epee36_bane.mdl",
    sItemType = "weapon",
    aAngle = Angle(0, 34, 92),
    vPos = Vector(-18, 29, 21),
    sBone = "ValveBiped.Bip01_R_Thigh"
})