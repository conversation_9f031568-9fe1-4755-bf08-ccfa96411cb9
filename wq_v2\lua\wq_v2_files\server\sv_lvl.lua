---------------------------------------
   ---@author: WQ
   ---@time: 11/04/2025 18:41
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------

local TIME_INCREMENT = LVM_V2.Lvl.Config["XPTime"]
local INCREMENT = LVM_V2.Lvl.Config["XPEach"]
 
util.AddNetworkString("LVM_V2:LevelUP")
 
hook.Add("LVM_CHARACTERS_BEFORE_SELECT", "LVM:LevelSync", function(ply, char, id)
    
    timer.Simple(0.1, function()
        local level = DLVM.GC:GetByChar("level", ply:SteamID64(), id)
        
        if level == nil then return end
        
        -- PrintTable(level)
        
        if level.lvl == 0 or level.lvl == nil or level.lvl == false then
            level.lvl = 1;
            level.Push(level);
        end

        ply:SetXP(level.lvl)
        ply:ChatNotify("Niveau", Color(40, 255, 30), "Nous avons actualisé votre niveau !")
    end)
    
end)


local function SavePlayer(ply)
    local level = DLVM.GC:GetByChar("level",ply)
    
    if level == nil then return end
    
    level.lvl = ply:GetXP()
    
    level.Push(level)
end
 
timer.Create("LVM_V2:LVL", 120, 0, function()
    for __,ply in ipairs(player.GetAll()) do
        if ply.SelectedCharacter == nil then continue end
        if ply.SelectedCharacter == 3 then continue end
    
        local xp = math.random(INCREMENT[1], INCREMENT[2])
        
        if ply:IsPremium() then
            xp = math.Round(xp * 1.25)
        end
        
      --  print(ply, ply:GetXP())
        if ply:GetXP() == false then continue end
        local actualLvl = ply:GetLvl()
        ply:AddXP(xp)
        
        local newLvl = ply:GetLvl()
        if ply:GetLvl() > actualLvl then
           net.Start("LVM_V2:LevelUP")
           net.Send(ply)

           local pts = 1
           if newLvl % 10 == 0 then
               pts = 3
           end
           ply:AddPDC(pts)

        end
        
        SavePlayer(ply)
    end
end)

hook.Add("PlayerSpawn", "LVM:VIE", function(ply)
    timer.Simple(5, function()
        if ply.CTG_JOB_HP != nil then
            ply:SetMaxHealth(ply.CTG_JOB_HP)
            ply:SetHealth(ply.CTG_JOB_HP)
        end
    end)
end)