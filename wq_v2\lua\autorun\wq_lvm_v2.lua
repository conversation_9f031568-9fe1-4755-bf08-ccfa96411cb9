local path = "wq_v2_files/"

if CLIENT then
    local files, dir = file.Find(path.."shared/*.lua", "LUA")
    
    for k,v in pairs(files) do
        include(path.."shared/"..v)
    end
 
    local files, dir = file.Find(path.."client/*.lua", "LUA")
     
    for k,v in pairs(files) do
        include(path.."client/"..v)
    end
    
    local f, d = file.Find(path.."pages/*.lua", "LUA")
    for k,v in pairs(f) do
        include(path.."pages/"..v)
        print("PAGES => "..v)
    end
    
else
     
    local files, dir = file.Find(path.."shared/*.lua", "LUA")
    
    for k,v in pairs(files) do
        AddCSLuaFile(path.."shared/"..v)
        include(path.."shared/"..v)
        print("SHARED => "..v)
    end
    
    local files, dir = file.Find(path.."client/*.lua", "LUA")
    
    for k,v in pairs(files) do
        AddCSLuaFile(path.."client/"..v)
        print("CLIENT => "..v)
    end
    
    local files, dir = file.Find(path.."server/*.lua", "LUA")
    
    for k,v in pairs(files) do
        include(path.."server/"..v)
        print("SERVER => "..v)
    end
    
    local f, d = file.Find(path.."pages/*.lua", "LUA")
    for k,v in pairs(f) do
        AddCSLuaFile(path.."pages/"..v)
        print("PAGES => "..v)
    end

end