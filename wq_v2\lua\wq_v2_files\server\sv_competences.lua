---------------------------------------
   ---@author: WQ
   ---@time: 27/05/2025 18:22
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------

LVM_V2 = LVM_V2 or {}

LVM_V2.Competences = {}

local PLAYER = FindMetaTable("Player")

---@param pdc integer
function PLAYER:AddPDC(pdc)
    local competences = DLVM.GC:GetByChar("competences", self)
    if competences == nil then return end
    
    competences.pdc = competences.pdc + pdc
    
    competences.Push(competences)
        
    LVM_V2.Competences:SyncPlayer(self)
end

function PLAYER:RemovePDC(pdc)
    local competences = DLVM.GC:GetByChar("competences", self)
    if competences == nil then return end
    
    competences.pdc = competences.pdc - pdc
    
    competences.Push(competences)
        
    LVM_V2.Competences:SyncPlayer(self)
end

function PLAYER:AddReset(reset)

    if not IsValid(self) then return end

    local boutique = DLVM.GC:Get("boutique", self:SteamID64())

    if boutique == nil then return end

    boutique["reset_competences"] = tonumber(boutique["reset_competences"]) + reset

    DLVM.GC:Push("boutique", self:SteamID64(), boutique)

    LVM_V2.Boutique:SyncPlayer(self)

end

function PLAYER:RemoveReset(reset)

    if not IsValid(self) then return end

    local boutique = DLVM.GC:Get("boutique", self:SteamID64())

    if boutique == nil then return end

    boutique["reset_competences"] = tonumber(boutique["reset_competences"]) - reset

    DLVM.GC:Push("boutique", self:SteamID64(), boutique)

    LVM_V2.Boutique:SyncPlayer(self)
    
end

util.AddNetworkString("LVM:MySkills")

function LVM_V2.Competences:SyncPlayer(ply)
    local competences = DLVM.GC:GetByChar("competences", ply)
    if competences == nil then return end

    MsgC( Color(46, 101, 172), "[LVM Competences] ", Color(46, 101, 172), ply:Nick(), Color(46, 101, 172), " a synchronisé ses compétences !\n" )
    
  --  print("[COMPETENCES] Synchronize "..ply:Nick())
    
    -- PrintTable(competences)
    
    net.Start("LVM:MySkills")
        net.WriteUInt(competences.pdc, 10)
        
        net.WriteUInt(competences.strength, 4)
        net.WriteUInt(competences.speed, 4)
        net.WriteUInt(competences.resistance, 4)
        net.WriteUInt(competences.jump, 4)
        net.WriteUInt(competences.chakra, 4)
    net.Send(ply)
end

function PLAYER:IsBeforeJutsuUnlocked(id)
    if id == 1 then return true end
    
    local tech = DLVM.Technics[id]
    local tech_before = DLVM.Technics[id-1]


    if tech.Nature == tech_before.Nature then

        if string.find(tech_before.Name, "En cours") then
            return true 
        end
        

        if DLVM.Technics:HasJutsu(self, id-1) then
            return true
        else
            return false
        end
    end
    
    return true;
end

function PLAYER:ResetJutsuByNature(name)
    local jutsu = DLVM.GC:GetByChar("jutsu", self)
    
    if jutsu == nil then return end
    
    local result = 0;
    
    for k,v in pairs(jutsu.owned_jutsu) do
        if DLVM.Technics[k] == nil then continue end
        if DLVM.Technics[k].Nature == name then
            result = result + DLVM.Technics[k].PDC;
            
            if v.star >= 2 then
                for i = 2, v.star do
                    result = result + DLVM.Technics[k].Upgrades[i]
                end
            end
            
            jutsu.owned_jutsu[k] = nil
        end
    end
    
    jutsu.Push(jutsu)
    
    print(result)
    
    local competences = DLVM.GC:GetByChar("competences", self)
    if competences == nil then return end
    
    competences.pdc = competences.pdc + result;
    
    competences.Push(competences)
    
    LVM_V2.Competences:SyncPlayer(self)
    
end

util.AddNetworkString("LVM:SKILL:UP")
util.AddNetworkString("LVM:ResetStat")

net.Receive("LVM:SKILL:UP", function(_, ply)
    
    local skill = net.ReadUInt(4)
    local skills = {
        "speed",
        "chakra",
        "resistance",
        "strength",
        "jump"
    }
    
    local competences = DLVM.GC:GetByChar("competences", ply)
    if competences == nil then return end
    
    if competences.pdc <= 0 then return end
    if competences[skills[skill]] >= 10 then return end
    
    competences.pdc = competences.pdc - 1
    
    competences[skills[skill]] = competences[skills[skill]] + 1
    
    competences.Push(competences)
    
    DarkRP.notify(ply, 1, 5, "Vous avez augmenté votre compétence "..skills[skill].." de 1")
    
    LVM_V2.Competences:SyncPlayer(ply)
    
end)

hook.Add("LVM_CHARACTERS_SELECT", "LVM:Competences", function(ply)
    
    timer.Simple(0.1, function()
        LVM_V2.Competences:SyncPlayer(ply)
        
        local newSpeed = 320 
        local walkspeed  = 220
        local newJump = 160 + (ply:GetSkill("jump") * 10)
        
        ply:SetWalkSpeed(walkspeed)
        ply:SetRunSpeed(newSpeed)
        ply:SetMaxSpeed(newSpeed)
        
        ply:SetJumpPower(newJump)
    end)
    
end)

net.Receive("LVM:ResetStat", function(_, ply)
    local boutique = DLVM.GC:Get("boutique", ply:SteamID64())

    if boutique == nil then return end
    
    if boutique.reset_competences <= 0 then return end
    
    
    local competences = DLVM.GC:GetByChar("competences", ply)
    
    if competences == nil then return end
    
    local jutsu = DLVM.GC:GetByChar("jutsu", ply)
    
    if jutsu == nil then return end
    
    -- if jutsu.nature.speciality == 0 then
    --     DarkRP.notify(ply, 1, 5, "Vous n'avez pas de compétence à reset !")
    --     return
    -- end
    
    jutsu.nature.speciality = 0;
    jutsu.nature.classe = 0;
    jutsu.owned_jutsu = {};
    jutsu.kenjutsu = {};
    competences = {
        strength = 0,
        speed = 0,
        resistance = 0,
        jump = 0,
        chakra = 0,
        Push = competences.Push
    };
    
    local result = ply:GetLvl();
    local bonus = math.floor(result /10) * 2
    result = result + bonus;

    
    competences.pdc = tonumber(result);
    
    jutsu.Push(jutsu)
    competences.Push(competences)
    
    ply:RemoveReset(1)
    
    DLVM.Technics:SyncPlayer(ply)
    LVM_V2.Competences:SyncPlayer(ply)
    
    DarkRP.notify(ply, 1, 5, "Vous avez reset vos toutes vos compétences, vous avez maintenant ".. result .." pdc !")
    
end)


concommand.Add("add_pdc", function(ply, cmd, args)
    ply:AddPDC(args[1])
end)

local PLAYER = FindMetaTable("Player")

---@return integer
function PLAYER:GetPDC()
    local competences = DLVM.GC:GetByChar("competences", self)
    if competences == nil then return 0 end

    return competences.pdc
end

---@param skill string
---@return integer
function PLAYER:GetSkill(skill)
    local competences = DLVM.GC:GetByChar("competences", self)
    if competences == nil then return 0 end

    return competences[skill:lower()]
end

local function FindClearPositionAround(origin, radius, steps)
    steps = steps or 16 -- plus de points = plus précis
    local height = 72 -- hauteur standard d'un joueur debout
    local mins = Vector(-16, -16, 0) -- hitbox approximative
    local maxs = Vector(16, 16, height)

    for i = 0, steps - 1 do
        local angle = (i / steps) * math.pi * 2
        local offset = Vector(math.cos(angle), math.sin(angle), 0) * radius
        local testPos = origin + offset

        local tr = util.TraceHull({
            start = testPos,
            endpos = testPos,
            mins = mins,
            maxs = maxs,
            mask = MASK_PLAYERSOLID
        })

        if not tr.Hit then
            return testPos -- position libre trouvée
        end
    end

    return nil -- aucune position libre
end

---@class DODGE
local DODGE = {}

local PERMUTATION = {}

---@enum Sequence
local act = {
    -- ["left"] = function(ply)
    --     ply:PlayAnimation("nrp_base_dodge_run_left")
    --     ply:SetVelocity(-ply:GetAngles():Right() * 5000)
    -- end,
    -- ["right"] = function(ply)
    --     ply:PlayAnimation("nrp_base_dodge_run_right")
    --     ply:SetVelocity(ply:GetAngles():Right() * 5000)
    -- end,
    ["permutation"] = function (ply)
        if ply:GetMoveType() == MOVETYPE_NONE then return end
        if PERMUTATION[ply] != nil and PERMUTATION[ply] > CurTime() then
            ply:ChatNotify("Permutation", color_white, "Patientez encore "..math.floor(PERMUTATION[ply]-CurTime()).."s avant de permuter !")
            return
        end
        
        if ply:GetCurrentChakra() < 150 then
            ply:ChatNotify("Permutation", color_white, "Vous devez avoir 150 de chakra minimum pour permuter !")
            return
        end
        
        local oldPos = ply:GetPos()
        local oldAng = ply:GetAngles()
        local newPos = FindClearPositionAround(oldPos + ply:GetUp() * 50, math.random(300, 700), 3)
        
        if newPos == nil then
            ply:ChatNotify("Permutation", color_white, "Impossible de permuter !")
            return
        end
        
        ply:EmitSound("LVM_v2/jutsu/poof.wav")
        ply:TakeChakra(150)
    
        timer.Simple(0, function()
            ply:SetPos(newPos + Vector(0, 0, 50))
            
            ParticleEffect("{1}Moku_croc", oldPos, oldAng)
            
            timer.Simple(0.1, function()
                local prop = ents.Create("prop_physics")
                prop:SetModel("models/skylyxx/ctg/props/permut_log.mdl")
                prop:SetPos(oldPos + Vector(0, 0, 20))
                prop:SetMoveType(MOVETYPE_VPHYSICS)
                prop:Spawn()
                prop:SetModelScale(0, 0)
                prop:SetModelScale(1, 0.2)
                prop:DropToFloor()
                
                ParticleEffect("{1}Moku_croc", newPos, ply:GetAngles())
                timer.Simple(2, function()
                    prop:SetModelScale(0, 0.8)
                    timer.Simple(1, function()
                        SafeRemoveEntity(prop)
                    end)
                end)
            end)
        end)
		
        PERMUTATION[ply] = CurTime() + 50
    end,
    ["up"] = function(ply)
        if ply:GetNWBool("LVM:WQ:PAIN", false) then return end
        if ply:GetGravity() > 5 then return end
        ply:EmitSound("solve_naruto_base/Double jump.wav")
        local iDuration = ply:PlayAnimation("nrp2_inplace_jump4", false, 2)

        ply:SetNetworkVar("bWasDoubleJumping", true)
        
        timer.Simple(iDuration, function()
            if IsValid(ply) then
                ply:SetNetworkVar("bWasDoubleJumping", false)

                if ply:GetNetworkVar("bNarutoRun", false) then
                    ply:PlayAnimation("nrp_base_run_loop", true)
                end
            end
        end)

        ply:SetVelocity(ply:GetUp() * (250 + (ply:GetSkill("jump") * 20)))
    end
}

---@param ply Player
---@param direction string
function DODGE:Act(ply, direction)
    -- if not ply:IsOnGround() and direction != "up" then return end
    if DODGE[ply]["cooldown"] > CurTime() then return end
    
    act[direction](ply)
    -- DODGE[ply][direction] = 0;
    DODGE[ply]["cooldown"] = CurTime() + 5
end

hook.Add("PlayerButtonDown", "LVM:Permutation", function(ply, key)
    if not IsValid(ply) then return end
    
    if ply:GetActiveWeapon() == nil then return end
    if not ply:GetActiveWeapon():IsValid() then return end
    if ply:GetActiveWeapon():GetClass() == nil then return end
    if ply:GetActiveWeapon():GetClass() != "LVM_fightmode" then return end
    
    --print(key, ply:GetInfoNum("kpermutation", 0))
    
    if ply:GetInfoNum("kpermutation", 0) == key then
        if ply:GetNetworkVar("LVM:WQ:PAIN", false) then return end
        if ply:GetNWBool("LVM:WQ:NoFallDamage", false) then return end
        if ply:GetNWBool("LVM:WQ:Doton:PeauDeRoche", false) then return end
        if ply:GetNWBool("LVM:WQ:Doton:Taupe", false) then return end

        DODGE:Act(ply, "permutation")
    end
    
end)

hook.Add("KeyPress", "LVM:DodgeKeyPress", function(ply, key)
    if not IsFirstTimePredicted() then return end
    if not IsValid(ply) then return end
    
    if ply:GetActiveWeapon() == nil then return end
    if not ply:GetActiveWeapon():IsValid() then return end
    if ply:GetActiveWeapon():GetClass() == nil then return end
    if ply:GetActiveWeapon():GetClass() != "LVM_fightmode" then return end

    if DODGE[ply] == nil then
        DODGE[ply] = {left = 0, right = 0, back = 0, cooldown = 0, up = 0, last = 0};
    end
     
    if not ply:Alive() then return end
    if DODGE[ply]["last"] + 0.3 < CurTime() then
        DODGE[ply] = {left = 0, right = 0, back = 0, cooldown = DODGE[ply]["cooldown"], up = 0, last = 0};
    end
    
    if DODGE[ply]["cooldown"] > CurTime() then return end

    local movement = nil;

    -- if key == IN_MOVELEFT then
    --     DODGE[ply]["left"] = DODGE[ply]["left"] + 1;
    --     DODGE[ply]["last"] = CurTime();
    --     if DODGE[ply]["left"] >= 2 then
    --         DODGE:Act(ply, "left")
    --     end
        
    --     movement = "left";
    -- end
    -- if key == IN_MOVERIGHT then
    --     DODGE[ply]["right"] = DODGE[ply]["right"] + 1;
    --     DODGE[ply]["last"] = CurTime();
    --     if DODGE[ply]["right"] >= 2 then
    --         DODGE:Act(ply, "right")
    --     end
        
    --     movement = "right";
    -- end
    if key == IN_JUMP then
        DODGE[ply]["up"] = DODGE[ply]["up"] + 1;
        DODGE[ply]["last"] = CurTime();
        if DODGE[ply]["up"] >= 2 then
            DODGE:Act(ply, "up")
            DODGE[ply]["up"] = 0;
        end
    end
end)

util.AddNetworkString("LVM:EFFECT:NARUTORUN")

util.AddNetworkString("LVM:RequestAnimations")

net.Receive("LVM:RequestAnimations", function(_, ply)

    
    local sequence = net.ReadString()
    local freeze = net.ReadBool() or false
    local bool = net.ReadBool() or false
    local float = net.ReadFloat() or 1

    ply:Freeze(freeze)
    if sequence == nil then return end
    if sequence == "reset" then
        ply:ResetAnimation()
        return
    end


    ply:PlayAnimation(sequence, bool, float)
    
end)

local function fnReset(pPlayer)
    if not IsValid(pPlayer) then return end
    
    pPlayer:PlayAnimation('nrp_base_run_end')

    pPlayer:SetNetworkVar("bNarutoRun", false)

    pPlayer:ResetSpeed()
    
    

    net.Start("LVM:EFFECT:NARUTORUN")
        net.WriteBool(false )
    net.Send(pPlayer)

    hook.Run("LVM:NarutoRun:End", pPlayer)
end    

hook.Add("KeyPress", "LVM:NarutoRun:KeyPress", function(pPlayer, iKey)

    if pPlayer:GetNetworkVar("LVM:PainMode", false) then return end
    if pPlayer:GetNetworkVar("LVM:WQ:ReductionVitesse", false) then return end
    if pPlayer:GetNWBool("LVM:WQ:Doton:PeauDeRoche", false) then return end
    if pPlayer:GetNWBool("LVM:WQ:Doton:Taupe", false) then return end
    if pPlayer:GetMoveType() == MOVETYPE_NOCLIP then return end
    if (pPlayer:AdminMode() and not pPlayer:IsAnimateur()) then return end
    if pPlayer:IsCarried() then return end
    if pPlayer:IsCarrying() then return end

    if iKey == IN_BACK then
        if pPlayer:GetNetworkVar("bNarutoRun", false) then
            fnReset(pPlayer)
        end
        return 
    end

    if iKey == 512 then
        if pPlayer:GetNetworkVar("bNarutoRun", false) then
            fnReset(pPlayer)
        end
        return 
    end

    if iKey == 1024 then
        if pPlayer:GetNetworkVar("bNarutoRun", false) then
            fnReset(pPlayer)
        end
        return 
    end

    if iKey == IN_SPEED and pPlayer:KeyDown(IN_FORWARD) then

        local eActiveWeapon = pPlayer:GetActiveWeapon()
        if IsValid(eActiveWeapon) and eActiveWeapon:GetClass() == "LVM_fightmode" then
            return
        end
        if pPlayer:GetNetworkVar("LVM:CanReset", false) then return end

        local iCurTime = CurTime()
        pPlayer.iLastShiftPressTime = pPlayer.iLastShiftPressTime or 0

        if iCurTime - pPlayer.iLastShiftPressTime <= 0.5 then
            if not pPlayer:GetNetworkVar("bNarutoRun", false) then  
                pPlayer:SetNetworkVar("bNarutoRun", true)

                local newSpeed = 550 + (pPlayer:GetSkill("speed") * 20)

                pPlayer:SetRunSpeed(newSpeed)
                pPlayer:SetMaxSpeed(newSpeed)
                
                pPlayer:EmitSound("eljaunito/solve/movements/dash1.wav")
          --      ParticleEffect("[5]black_hole_micro_swirl_base_b", pPlayer:GetPos(), Angle(0, 0, 0))
                
                pPlayer:PlayAnimation("nrp_base_run_loop", true)

                net.Start("LVM:EFFECT:NARUTORUN")
                    net.WriteBool(true)
                net.Send(pPlayer)

                hook.Run("LVM:NarutoRun:Start", pPlayer)
            end
        end
        
        pPlayer.iLastShiftPressTime = iCurTime
    end

end)

hook.Add("KeyRelease", "LVM:NarutoRun:KeyRelease", function(pPlayer, iKey)
    if iKey == IN_SPEED then
        if pPlayer:GetNetworkVar("bNarutoRun", false) then
            fnReset(pPlayer)
        end
    end
end)

hook.Add("SetupMove", "LVM:LimitNarutoRunPlayerSpeed", function(pPlayer, oMv)
    if pPlayer:GetNetworkVar("bNarutoRun", false) then
    
        local speed1 = pPlayer:GetVelocity()
        speed1.x = math.abs(speed1.x)
        speed1.y = math.abs(speed1.y)
        speed1.z = math.abs(speed1.z)
        
        local speed2 = math.max(speed1.x, speed1.y, speed1.z)
        
        if speed2 <= 110 then
            fnReset(pPlayer)
            return
        end
        
    
        local eActiveWeapon = pPlayer:GetActiveWeapon()

        if IsValid(eActiveWeapon) and eActiveWeapon:GetClass() == "LVM_fightmode" then
            fnReset(pPlayer)
        end

    end
end)

local iAngle = 15

hook.Add( "GetFallDamage", "LVM:WQ:DAMAGEFALL", function( ply, speed )
    if ply:Crouching() then 
        return 0 
    else
        if ply:GetNetworkVar("LVM:WQ:NoFallDamage", false) then return 0 end
        ply:TakeDamage( speed / 10, ply, ply )
        return 0 
    end
end)


util.AddNetworkString("LVM:ChakraJump:Jump")

net.Receive("LVM:ChakraJump:Jump", function(_, pPlayer)
    local iCurTime = CurTime()
    if pPlayer:GetNetworkVar("iChakraJumpCooldown", 0) > iCurTime then return end

    local angEye = pPlayer:EyeAngles()
    angEye.x = math.min(angEye.x, -iAngle)

    local iBodyBone = pPlayer:LookupBone("ValveBiped.Bip01_Spine2")
    if not iBodyBone then return end

    local vecPos = pPlayer:GetBonePosition(iBodyBone)

    pPlayer:EmitSound("solve_naruto_base/chakra_jump_v2.wav")

    ParticleEffectAttach("dash_geams_solve", PATTACH_POINT_FOLLOW, pPlayer, 1)

    local iPower = net.ReadUInt(7)
    iPower = math.Clamp(iPower/100, 0, 1)

    pPlayer:PlayAnimation("nrp_base_chakrajump_start")

    local vecDir = angEye:Forward()
    pPlayer:SetVelocity(vecDir*(iPower*40^2))

    local bIsInvisible = pPlayer:GetNetworkVar("Invisibility", false)
    pPlayer:SetNetworkVar("Invisibility", true)
    pPlayer:SetNoDraw(true)
    timer.Simple(0.4, function()

        if bIsInvisible then return end
        

        if IsValid(pPlayer) then
           pPlayer:SetNetworkVar("Invisibility", false)
            pPlayer:SetNoDraw(false)
        end

    end)

    pPlayer:SetNetworkVar("iChakraJumpCooldown", iCurTime + 10)

end)



util.AddNetworkString("LVM:AskToBeCarried")
util.AddNetworkString("LVM:AskCarried")
util.AddNetworkString("LVM:AcceptCarried")
util.AddNetworkString("LVM:StopBeCarry")
util.AddNetworkString("LVM:StopBeCarrier")

local CARRY_TABLE = {}
local REQUEST_CARRY = {}

local function CarryPlayer(leporter, leporteur)
    leporter:SetNetworkVar("PorterPar", leporteur)
    leporteur:SetNetworkVar("JoueurPorte", leporter)
    
    leporter:SetParent(leporteur)
    --leporter:FollowBone(leporter, leporter:LookupBone("ValveBiped.Bip01_Spine2"))
    --leporter:SetMoveType(MOVETYPE_NONE)
    leporter:SetCollisionGroup(COLLISION_GROUP_WEAPON)
    leporter:SetCustomCollisionCheck(true)
end

net.Receive("LVM:StopBeCarrier", function(_, ply)
    if ply:GetNetworkVar("JoueurPorte", false) == false then return end
    
    local porter = ply:GetNetworkVar("JoueurPorte", false)
    
    if IsValid(porter) then return end
    
    ply:SetNetworkVar("JoueurPorte", false)
end)

--hook.Remove( "ShouldCollide", "LVM:Carry")
hook.Add( "ShouldCollide", "LVM:Carry", function( ent1, ent2 )
    --print(ent1, ent2)
    if (ent1:IsPlayer() and ent2:IsPlayer()) then
	    if ( ent1:IsCarry() and ent2:IsPlayer() )then return false end
    end
end )

net.Receive("LVM:StopBeCarry", function(_, ply)
    if ply:GetNetworkVar("PorterPar", false) == false then return end
    
    ply:SetParent(nil)
    ply:SetMoveType(MOVETYPE_WALK)
    local leporteur = ply:GetNetworkVar("PorterPar", false)
    if IsValid(ply) then
        ply:SetPos(leporteur:GetPos() + Vector(0, 0, 90))
    else
        ply:SetPos(ply:GetPos() + Vector(0, 0, 90))
    end
    ply:SetCustomCollisionCheck(false)
    
    local leporteur = ply:GetNetworkVar("PorterPar", false)
    
    ply:SetNetworkVar("PorterPar", false)
    
    if IsValid(leporteur) then
        leporteur:SetNetworkVar("JoueurPorte", false)
    end
end)

net.Receive("LVM:AcceptCarried", function(_, ply)
    local bool = net.ReadBool()
    local sender = net.ReadPlayer()
    
    if not bool then REQUEST_CARRY[ply] = nil; return end
    
    if REQUEST_CARRY[ply] == nil then return end
    if ply:GetNetworkVar("JoueurPorte", false) != false then return end
    if ply:GetNetworkVar("PorterPar", false) != false then return end
    if sender:GetNetworkVar("PorterPar", false) != false then return end
    if sender:GetNetworkVar("JoueurPorte", false) != false then return end
    
    REQUEST_CARRY[ply] = nil;
    
    if sender:GetPos():Distance(ply:GetPos()) >= 130 then
        DarkRP.notify(ply, 1, 5, "Trop loin du joueur !")
        return
    end
    
    if CARRY_TABLE[sender][ply] == nil then return end
    
    if CARRY_TABLE[sender][ply].time < CurTime() then
        DarkRP.notify(ply, 1, 5, "La demande a expirer !")
        return
    end
    
    CarryPlayer(sender, ply)
    
end)

net.Receive("LVM:AskToBeCarried", function(_, ply)
    local entites = net.ReadPlayer()
    if not IsValid(entites) then return end
    if not entites:IsPlayer() then return end
    
    if entites:IsBot() then
        CarryPlayer(ply, entites)
        return 
    end
    
    if REQUEST_CARRY[entites] != nil and REQUEST_CARRY[entites] then
        DarkRP.notify(ply, 1, 5, "Il y'a déjà une demande en attente !")
        return
    end
    
    if ply:GetNetworkVar("JoueurPorte", false) != false then return end
    if ply:GetNetworkVar("PorterPar", false) != false then return end
    
    if entites:GetNetworkVar("JoueurPorte", false) != false then return end
    
    if entites:GetNetworkVar("PorterPar", false) then
        return
    end
    
    if entites:GetPos():Distance(ply:GetPos()) >= 100 then DarkRP.notify(ply, 0, 5, "Vous êtes trop loin pour sa !") return end
    
    if CARRY_TABLE[ply] == nil then
        CARRY_TABLE[ply] = {}
    end
    
    
    if CARRY_TABLE[ply][entites] != nil then
        if CARRY_TABLE[ply][entites].time >= CurTime() then
            DarkRP.notify(ply, 1, 5, "Vous avez déja demandé a vous faire porter à cette personne !")
            return
        end
    end
    
    CARRY_TABLE[ply][entites] = {time = CurTime() + 10}
    REQUEST_CARRY[entites] = true;
        

    net.Start("LVM:AskCarried")
        net.WritePlayer(ply)
    net.Send(entites)

end)


util.AddNetworkString("LVM:requestAnimation")
util.AddNetworkString("LVM:startAnimation")
util.AddNetworkString("LVM:stopAnimation")

local function getanim(act)
    if act == nil then return end
    if act == "" then return end

    local anim = LVM_V2.Animations

    for k, v in pairs(anim) do
        if v.Act == act then
            return v
        end
    end

    return nil
end

net.Receive("LVM:requestAnimation", function(_, ply)
    local anim = net.ReadString()
    
    local animData = getanim(anim)
    if animData == nil then return end

    print("playing, animation", animData.CanWalk, animData.Loop)

    net.Start("LVM:startAnimation")
        net.WriteString(anim)
        net.WritePlayer(ply)
    net.SendPVS(ply:GetPos())
    
    ply.animation_playing = true
    ply.animation_canmove = animData.CanWalk or false
end)

local function VelocityIsHigher(ply, value)
    local x, y, z = math.abs(ply:GetVelocity().x), math.abs(ply:GetVelocity().y), math.abs(ply:GetVelocity().z)

    if x > value or y > value or z > value then
        return true
    else
        return false
    end
end

hook.Add("SetupMove", "LVM:animationStop", function(ply, moveData, cmd)

    if (not ply.animation_playing) then return end
    
    if (not ply.animation_canmove) then
        if (VelocityIsHigher(ply, 5)) then
            print("stop animation")
            net.Start("LVM:stopAnimation")
                net.WritePlayer(ply)
            net.Broadcast()
            ply.animation_playing = false
        end
    end

    if (ply:KeyDown(IN_DUCK) or ply:KeyDown(IN_JUMP) or ply:KeyDown(IN_USE) or ply:KeyDown(IN_SPEED)) then
        print("stop animation")
        net.Start("LVM:stopAnimation")
            net.WritePlayer(ply)
        net.Broadcast()
        ply.animation_playing = false
    end
end)

hook.Add( "CanPlayerSuicide", "LVM:DisableSuicideCarried", function( ply )
	if ply:IsCarried() or ply:IsCarrying() then
		return false
	end
end )


local iAddHealth = 1; ----------- EN %
local iTime = 3; ---------- EN SECONDES

local classe = {
    ["Médicin"] = function(ply) ---------- Cela ajoute x % de vie toutes les x secondes
        if timer.Exists("LVM:HealMedic"..ply:SteamID64()) then
            timer.Remove("LVM:HealMedic"..ply:SteamID64())
        end
        timer.Create("LVM:HealMedic"..ply:SteamID64(), iTime, 0, function()
            if not IsValid(ply) then return end

            local iHealth = ply:Health()
            local iMaxHealth = ply:GetMaxHealth()
            if iHealth >= iMaxHealth then return end

            local iNewHeatlh = math.Clamp(iHealth + (iMaxHealth * (iAddHealth / 100)), 0, iMaxHealth)

            ply:SetHealth(iNewHeatlh)
        end) 
    end

}

hook.Add("LVM:Classe:Start", "LVM:Classe:Medic", function(ply, classeName)
    -- if timer.Exists("LVM:HealMedic"..ply:SteamID64()) then
    --     timer.Remove("LVM:HealMedic"..ply:SteamID64())
    -- end

    -- if classeName == 1 then
    --     classe["Médicin"](ply)
    -- end
    print("[LVM] Classe: "..classeName.." - "..ply:Nick())
end)