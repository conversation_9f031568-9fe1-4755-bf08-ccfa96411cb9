---------------------------------------
   ---@author: WQ
   ---@time: 01/06/2025 16:38
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------

function LVM_V2.PoubelleCosmetic:OpenInfoItemMenu(x, y, item, ammount)
    local vendable = ""
    local itemtype = ""
    
    if IsValid(self.InfoItemMenu) then self.InfoItemMenu:Remove() end
    
    PrintTable(item)
    
    local itemtypee = item.sItemType
    if type(itemtypee) == "table" then
        itemtypee = itemtypee[1]
    end

    self.InfoItemMenu = vgui.Create("DFrame")
    self.InfoItemMenu:SetSize(SW(246), SH(300))
    self.InfoItemMenu:SetPos(x, y)
    self.InfoItemMenu:SetDraggable(false)
    self.InfoItemMenu:ShowCloseButton(false)
    self.InfoItemMenu:SetTitle("")
    self.InfoItemMenu:MakePopup()

    self.InfoItemMenu.Paint = function(s, w, h)
        if (not IsValid(self.vFrame) and not IsValid(self.vFrameAdmin)) then
            if IsValid(s) then
                s:Remove()
            end
        end
        surface.SetMaterial(WQ_Inventory.Materials["WQ_background_item_info"])
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(0, 0, w, h)

        draw.SimpleText("Type", "LVM:WQ:MarketPlace:1:22", SW(30), SH(5), color_white, TEXT_ALIGN_LEFT)
        -- draw.SimpleText(itemtypee, "LVM:WQ:MarketPlace:2:17", SW(30), SH(25), color_white, TEXT_ALIGN_LEFT)
    end


    self.InfoItemMenu.ItemBackground = vgui.Create("DPanel", self.InfoItemMenu)
    self.InfoItemMenu.ItemBackground:SetSize(SW(200), SH(200))
    self.InfoItemMenu.ItemBackground:SetPos(SW(25), SH(65))
    self.InfoItemMenu.ItemBackground.Paint = function(self, w, h)
        surface.SetMaterial(WQ_MarketPlace.Material["WQ_item_background_marketplace_info"])
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(0, 0, w, h)
    end

    if item.sModel then
        self.InfoItemMenu.Item = vgui.Create("DModelPanel", self.InfoItemMenu)
        self.InfoItemMenu.Item:SetSize(SW(180), SH(180))
        self.InfoItemMenu.Item:SetPos(SW(35), SH(65))
        self.InfoItemMenu.Item:SetModel(item.sModel)
        self.InfoItemMenu.Item.LayoutEntity = function(self, Entity)
            return
        end

        self.InfoItemMenu.Item.Entity:SetPos(self.InfoItemMenu.Item.Entity:GetPos() - Vector(0, 0, 0))
        self.InfoItemMenu.Item:SetCamPos(Vector(15, -6, 60))
        self.InfoItemMenu.Item:SetLookAt(Vector(0, 0, 60))
        self.InfoItemMenu.Item:SetFOV(50)
        local min, max = self.InfoItemMenu.Item.Entity:GetRenderBounds()
        self.InfoItemMenu.Item:SetCamPos(min:Distance(max) * Vector(0.7, 0.7, 0.7))
        self.InfoItemMenu.Item:SetLookAt((max + min) / 2)
        self.InfoItemMenu.Item:SetMouseInputEnabled(false)
    elseif item.sMaterial then
        self.InfoItemMenu.Item = vgui.Create("DPanel", self.InfoItemMenu)
        self.InfoItemMenu.Item:SetSize(SW(180), SH(180))
        self.InfoItemMenu.Item:SetPos(SW(35), SH(65))

        self.InfoItemMenu.Item.Paint = function(s, w, h)
            surface.SetDrawColor(255, 255, 255)
            surface.SetMaterial(WQ_Inventory.Materials[item.sMaterial])
            surface.DrawTexturedRect(0, 0, w, h)
        end
    end

    self.InfoItemMenu.ImageName = vgui.Create("DPanel", self.InfoItemMenu)
    self.InfoItemMenu.ImageName:SetSize(SW(200), SH(40))
    self.InfoItemMenu.ImageName:SetPos(SW(25), SH(222))
    self.InfoItemMenu.ImageName.Paint = function(self, w, h)
        local bg = WQ_Inventory.Materials["item_bar_none"]

        if item.sRarity then
            bg = WQ_Inventory.Materials["item_bar_" .. item.sRarity]
        end

        surface.SetMaterial(bg)
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(0, 0, w, h)

        draw.SimpleText(item.sName, "LVM:WQ:MarketPlace:2:22", w / 2, h / 2, color_white, TEXT_ALIGN_CENTER,
            TEXT_ALIGN_CENTER)
    end

    self.InfoItemMenu.ItemAmmount = vgui.Create("DLabel", self.InfoItemMenu)
    self.InfoItemMenu.ItemAmmount:SetPos(SW(200), SH(70))
    self.InfoItemMenu.ItemAmmount:SetFont("LVM:WQ:MarketPlace:1:22")
    self.InfoItemMenu.ItemAmmount:SetTextColor(color_white)
    self.InfoItemMenu.ItemAmmount:SetText("x" .. ammount)
    self.InfoItemMenu.ItemAmmount:SizeToContents()


    return self.InfoItemMenu
end


local function getInventoryBoutique(cb)
    if not IsValid(LocalPlayer()) or not LocalPlayer().WQ_Inventory_tInventory then
        cb({})
    end

    local inventory = LocalPlayer().WQ_Inventory_tInventory or {}
    local result = {}
    
    for k,v in pairs(inventory) do
        local itemData = WQ_Inventory:GetItem(v.item)
        if not itemData then continue end
        if itemData.sModel != nil and LVM_V2.Boutique:IsModelBoutique(itemData.sModel) then
            table.insert(result, v)
        end
    end
    
    cb(result);
end

function LVM_V2.PoubelleCosmetic:Open()
    
    self.vFrame = vgui.Create("DFrame")
    self.vFrame:SetSize(SW(1662), SH(931))
    self.vFrame:Center()
    self.vFrame:SetDraggable(false)
    self.vFrame:ShowCloseButton(false)
    self.vFrame:SetTitle("")
    self.vFrame:MakePopup()
    self.vFrame:SetAlpha(0)
    self.vFrame:AlphaTo(255, 0.2, 0)
    self.vFrame.Paint = function(s, w, h)
        surface.SetDrawColor(255, 255, 255)
        surface.SetMaterial(util.ImgurToMaterial("MKZzk2v.png"))
        surface.DrawTexturedRect(0, 0, w, h)
        
        -- draw.RoundedBox(0, SW(1221), SH(400), SW(50), SH(50), Color(29, 29, 29))
    end
    

    self.vFrame.bClose = vgui.Create("DButton", self.vFrame)
    self.vFrame.bClose:SetSize(SW(23), SH(22))
    self.vFrame.bClose:SetPos(SW(1570), SH(40))
    self.vFrame.bClose:SetText("")
    self.vFrame.bClose.Paint = function(self, w, h)
        surface.SetMaterial(WQ_Seller:Material("WQ_seller_close.png"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(0, 0, w, h)
    end
    self.vFrame.bClose.DoClick = function()
        self.vFrame:Remove()
    end

    self.vFrame.vInventory = vgui.Create("DPanel", self.vFrame)
    self.vFrame.vInventory:SetSize(SW(586), SH(577))
    self.vFrame.vInventory:SetPos(SW(295), SH(130))
    self.vFrame.vInventory.Paint = nil

    self.vFrame.vInventory.vScroll = vgui.Create("DScrollPanel", self.vFrame.vInventory)
    self.vFrame.vInventory.vScroll:SetSize(SW(586), SH(500))
    self.vFrame.vInventory.vScroll:SetPos(SW(-15), SH(60))
    self.vFrame.vInventory.vScroll.Paint = function(s, w, h)
        surface.SetDrawColor(255, 255, 255, 0)
    end

    self.vFrame.vInventory.vScroll.vBar = self.vFrame.vInventory.vScroll:GetVBar()
    self.vFrame.vInventory.vScroll.vBar:SetSize(SW(10), SH(496))
    self.vFrame.vInventory.vScroll.vBar.Paint = function(s, w, h)
        surface.SetDrawColor(255, 255, 255)
        surface.SetMaterial(WQ_Inventory.Materials["WQ_inventory_scroll_background"])
        surface.DrawTexturedRect(0, 0, w, h)
    end

    self.vFrame.vInventory.vScroll.vBar.btnUp.Paint = function(self, w, h)
    end
    self.vFrame.vInventory.vScroll.vBar.btnDown.Paint = function(self, w, h)
    end

    self.vFrame.vInventory.vScroll.vBar.btnGrip.Paint = function(s, w, h)
        surface.SetDrawColor(255, 255, 255)
        surface.SetMaterial(WQ_Inventory.Materials["WQ_inventory_scroll_grip"])
        surface.DrawTexturedRect(0, 0, w, h)
    end

    self.vFrame.vInventory.vScroll.IconLayout = vgui.Create("DIconLayout", self.vFrame.vInventory.vScroll)
    self.vFrame.vInventory.vScroll.IconLayout:Dock(FILL)
    self.vFrame.vInventory.vScroll.IconLayout:DockMargin(30, 0, 0, 0)
    self.vFrame.vInventory.vScroll.IconLayout:SetSpaceX(SW(10))
    self.vFrame.vInventory.vScroll.IconLayout:SetSpaceY(SH(10))

    function self:UpdateInventory()
        getInventoryBoutique(function(tInventory)
            for i = 1, #tInventory + 1 do
                if IsValid(self.vFrame.vInventory.vScroll["Item".. i]) then
                    self.vFrame.vInventory.vScroll["Item".. i]:Remove()
                end
            end
    
            for i = 1, #tInventory  do
                if IsValid(self.vFrame.vInventory.vScroll["Item".. i]) then
                    self.vFrame.vInventory.vScroll["Item".. i]:Clear()
                end
                
    
                local itemData = WQ_Inventory:GetItem(tInventory[i].item)
                if not itemData then continue end
                
                self.vFrame.vInventory.vScroll["Item".. i] = self.vFrame.vInventory.vScroll.IconLayout:Add("DPanel")
                self.vFrame.vInventory.vScroll["Item".. i]:SetSize(SW(96), SH(99))
                self.vFrame.vInventory.vScroll["Item".. i]:SetMouseInputEnabled(true)
                self.vFrame.vInventory.vScroll["Item".. i]:SetCursor("hand")
                self.vFrame.vInventory.vScroll["Item".. i].Paint = function(s, w, h)
                    local bg = WQ_Inventory.Materials["item_none"]
                    
                    if itemData.sRarity and WQ_Inventory.Materials["item_"..itemData.sRarity] then
                        bg = WQ_Inventory.Materials["item_"..itemData.sRarity]
                    end
    
                    surface.SetDrawColor(255, 255, 255)
                    surface.SetMaterial(bg)
                    surface.DrawTexturedRect(0, 0, w, h)
                end
    
                self.vFrame.vInventory.vScroll["Item".. i].OnStartDragging = function(self)
                    surface.PlaySound("solve_naruto_base/ui/menu_selection_v2.wav")
                end
    
                self.vFrame.vInventory.vScroll["Item".. i].OnStopDragging = function(self)
                    surface.PlaySound("solve_naruto_base/ui/menu_selection_v2.wav")
                end
    
                self.vFrame.vInventory.vScroll["Item".. i].InfoButton = vgui.Create("DButton", self.vFrame.vInventory.vScroll["Item".. i])
                self.vFrame.vInventory.vScroll["Item".. i].InfoButton:SetSize(SW(13), SH(13))
                self.vFrame.vInventory.vScroll["Item".. i].InfoButton:SetPos(SW(75), SH(5))
                self.vFrame.vInventory.vScroll["Item".. i].InfoButton:SetText("")
                self.vFrame.vInventory.vScroll["Item".. i].InfoButton.Paint = function(self, w, h)
                    if WQ_MarketPlace and WQ_MarketPlace.Material and WQ_MarketPlace.Material["info"] then
                        surface.SetMaterial(WQ_MarketPlace.Material["info"])
                        surface.SetDrawColor(color_white)
                        surface.DrawTexturedRect(0, 0, w, h)
                    end
                end
    
                self.vFrame.vInventory.vScroll["Item".. i].InfoButton.OnCursorEntered = function(btn)
                    if IsValid(btn.InfoPanel) then return end 
                    surface.PlaySound("solve_naruto_base/ui/menu_selection_v2.wav")
                    local parent = btn:GetParent()
                    local x, y = gui.MousePos() 
                    
                    btn.InfoPanel = self:OpenInfoItemMenu(x + 10, y + 10, itemData, tInventory[i].iAmount)
                end
                
                self.vFrame.vInventory.vScroll["Item".. i].InfoButton.OnCursorExited = function(btn)
                    if IsValid(btn.InfoPanel) then
                        btn.InfoPanel:Remove()
                        btn.InfoPanel = nil
                    end
                end
    
                
                self.vFrame.vInventory.vScroll["Item"..i].sItem = itemData
                self.vFrame.vInventory.vScroll["Item".. i].bInventory = true
                if itemData.sItemType == "farm" then
                    self.vFrame.vInventory.vScroll["Item".. i]:Droppable("LVM_case")
                    
                end
                if itemData.sModel then 
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon = vgui.Create("DModelPanel", self.vFrame.vInventory.vScroll["Item".. i])
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon:SetSize(SW(64), SH(64))
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon:SetPos(SW(16), SH(13))
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon:SetModel(itemData.sModel)
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon.LayoutEntity = function(self, Entity)
                        return
                    end
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon.Entity:SetPos(self.vFrame.vInventory.vScroll["Item".. i].vIcon.Entity:GetPos() - Vector(0, 0, 0))
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon:SetCamPos(Vector(15, -6, 60))
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon:SetLookAt(Vector(0, 0, 60))
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon:SetFOV(50)
                    local iMin,iMax = self.vFrame.vInventory.vScroll["Item".. i].vIcon.Entity:GetRenderBounds()
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon:SetCamPos(iMin:Distance(iMax) * Vector(.7, .7, .7))
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon:SetLookAt((iMax + iMin) / 2)
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon:SetMouseInputEnabled(false)
    
                elseif itemData.sMaterial and WQ_Inventory.Materials[itemData.sMaterial] then
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon = vgui.Create("DPanel", self.vFrame.vInventory.vScroll["Item".. i])
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon:SetSize(SW(64), SH(64))
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon:SetPos(SW(16), SH(13))
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon.Paint = function(s, w, h)
                        surface.SetDrawColor(255, 255, 255)
                        surface.SetMaterial(WQ_Inventory.Materials[itemData.sMaterial])
                        surface.DrawTexturedRect(0, 0, w, h)
                    end
                    self.vFrame.vInventory.vScroll["Item".. i].vIcon:SetMouseInputEnabled(false)
                end
    
                self.vFrame.vInventory.vScroll["Item".. i].Ammount = vgui.Create("DLabel", self.vFrame.vInventory.vScroll["Item".. i])
                self.vFrame.vInventory.vScroll["Item".. i].Ammount:SetPos(SW(70), SH(70))
                self.vFrame.vInventory.vScroll["Item".. i].Ammount:SetFont("LVM:WQ:INVENTORY:2:17")
                self.vFrame.vInventory.vScroll["Item".. i].Ammount:SetTextColor(color_white)
                local sAmountText = "x" .. tInventory[i].iAmount
                surface.SetFont("LVM:WQ:INVENTORY:2:20")
                local iTextWidth, iTextHeight = surface.GetTextSize(sAmountText)
                self.vFrame.vInventory.vScroll["Item".. i].Ammount:SetSize(iTextWidth, iTextHeight)
                self.vFrame.vInventory.vScroll["Item".. i].Ammount:SetText(sAmountText)
            end
        end)
    end

    self:UpdateInventory()

    local item;
    function self:UpgradeCraftFinal()
    

        self.vFrame.CaseCraftFinal = vgui.Create("DPanel", self.vFrame)
        self.vFrame.CaseCraftFinal:SetSize(SW(96), SH(99))
        self.vFrame.CaseCraftFinal:SetPos(SW(1090), SH(275))
        self.vFrame.CaseCraftFinal.Paint = function(self, w,  h)

            local bg = WQ_Inventory.Materials["item_none"]

            if item and item.sRarity then 
                bg = WQ_Inventory.Materials["item_1"]
            end

            surface.SetDrawColor(255, 255, 255)
            surface.SetMaterial(bg)
            surface.DrawTexturedRect(0, 0, w, h)
        end
        
        self.vFrame.CaseCraftFinal:Receiver("LVM_case", function(self, tblDropped, bDropped)
            if not bDropped then return end
    
            if #tblDropped > 0 then
                local droppedItem = tblDropped[1]
    
                if IsValid(droppedItem) and droppedItem.sItem then
                    item = droppedItem.sItem
    
                    -- Supprimer l'ancien icon s’il existe
                    if IsValid(self.Icon) then
                        self.Icon:Remove()
                    end
    
                    -- Met à jour l'affichage
                    if item and WQ_Inventory.Materials[item.sMaterial] then
                        self.Icon = vgui.Create("DPanel", self)
                        self.Icon:SetSize(SW(80), SH(80))
                        self.Icon:SetPos(SW(10), SH(5))
                        self.Icon.Paint = function(s, w, h)
                            surface.SetDrawColor(255, 255, 255)
                            surface.SetMaterial(WQ_Inventory.Materials[item.sMaterial])
                            surface.DrawTexturedRect(0, 0, w, h)
                        end
    
                        self.Icon.sItem = item
                        self.Icon.final = true
                        self.Icon:Droppable("LVM_inventory")
                    end
    
                    self:InvalidateLayout(true)
                end
            end
        end)
        
    end

        
    --     if item then
    --         if WQ_Inventory.Materials[item.sMaterial] then
    --             self.vFrame.CaseCraftFinal.Icon = vgui.Create("DPanel", self.vFrame.CaseCraftFinal)
    --             self.vFrame.CaseCraftFinal.Icon:SetSize(SW(80), SH(80))
    --             self.vFrame.CaseCraftFinal.Icon:SetPos(SW(10), SH(5))
    --             self.vFrame.CaseCraftFinal.Icon.Paint = function(s, w, h)

    --                 surface.SetDrawColor(255, 255, 255)
    --                 surface.SetMaterial(WQ_Inventory.Materials[item.sMaterial])
    --                 surface.DrawTexturedRect(0, 0, w, h)
    --             end

    --             self.vFrame.CaseCraftFinal.Icon.sItem = item
    --             self.vFrame.CaseCraftFinal.Icon.final = true
    --             self.vFrame.CaseCraftFinal.Icon:Droppable("LVM_inventory")  
    --         end
    --     end


    -- end

    -- self:UpgradeCraftFinal()

end