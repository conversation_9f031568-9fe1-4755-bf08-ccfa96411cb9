---------------------------------------
---@author: WQ
---@time: 27/02/2025 00:53
---@version: 1.0.0
---@server: ©️ LVM - Naruto RP
---------------------------------------


LVM_V2 = LVM_V2 or {};

LVM_V2.Pages = LVM_V2.Pages or {};
LVM_V2.Pages["Library"] = {};
LVM_V2.Pages["F4"] = {};
LVM_V2.Pages["Reroll"] = {};

LVM_V2.Pages.Config = {
    ["Library"] = {
        ["Navbar"] = {
            ["Nature"] = 1,
            ["Keikkei Genkai"] = 2,
            ["Clan"] = 3,
            ["Art Ninja"] = 4,
            ["Autre"] = 5,
            ["Classe"] = 6,
        },
        ["Pos"] = 800
    },
    ["F4"] = {
        ["Navbar"] = {
            ["Natures"] = 1,
            ["Keikkei Genkai"] = 2,
            ["Clan"] = 3,
            ["Art Ninja"] = 4,
            ["Kenjutsu"] = 5,
            ["Autre"] = 6,
            ["Classe"] = 7,
            ["Statistiques"] = 8,
            ["Binds"] = 9,
            ["Favoris"] = 10
        },
        ["Pos"] = 800
    },
    ["Reroll"] = {
        ["Navbar"] = {
            ["Basique"] = 1,
            ["Keikkei Genkai"] = 2,
        },
        ["Pos"] = 850
    },
}

if CLIENT then
    LVM_V2.ScrW = ScrW();
    LVM_V2.ScrH = ScrH();

    LVM_V2.Fonts = LVM_V2.Fonts or {};

    for i = 1, 30 do
        surface.CreateFont("LVM:" .. i .. ":500", {
            font = "Albert Sans Bold",
            size = SW(i*3),
            weight = 500
        })
        LVM_V2.Fonts["LVM:" .. i .. ":500"] = "LVM:" .. i .. ":500";

        surface.CreateFont("LVM2:" .. i .. ":500", {
            font = "Albert Sans SemiBold",
            size = SW(i*3),
            weight = 500
        })
        LVM_V2.Fonts["LVM2:" .. i .. ":500"] = "LVM2:" .. i .. ":500";

        surface.CreateFont("LVM3:" .. i .. ":500", {
            font = "FOT-Reggae Std B",
            size = SW(i*3),
            weight = 500
        })
        LVM_V2.Fonts["LVM3:" .. i .. ":500"] = "LVM3:" .. i .. ":500";
    end

    setmetatable(LVM_V2.Fonts, {
        __call = function(self, name, size, weight)
            if self[name .. ":" .. tostring(size) .. ":" .. tostring(weight)] then
                return name .. ":" .. tostring(size) .. ":" .. tostring(weight);
            else
                return "LVM:10:500"
            end
        end
    })

    function LVM_V2.Fonts:Add(fontName, size, weight)
        if self[fontName .. ":" .. tostring(size) .. ":" .. tostring(weight)] == nil then
            surface.CreateFont(fontName .. ":" .. tostring(size) .. ":" .. tostring(weight), {
                font = fontName,
                size = ScreenScale(size),
                weight = weight
            })

            LVM_V2.Fonts[fontName .. ":" .. tostring(size) .. ":" .. tostring(weight)] = fontName ..
                ":" .. size .. ":" .. weight;
        end
    end

    hook.Add("OnScreenSizeChanged", "ScreenSize::LVM_V2", function(a, b, scrW, scrH)
        LVM_V2.ScrW = scrW;
        LVM_V2.ScrH = scrH;
    end)


    LVM_V2.mats = {}
    function LVM_V2:Material(img, ...)
        if LVM_V2.mats[img] == nil then
            LVM_V2.mats[img] = Material("LVM_v2/" .. img, ...)
            return LVM_V2.mats[img];
        end

        return LVM_V2.mats[img];
    end

    function LVM_V2:Key(keycode)
        keycode = (keycode == nil and "0" or keycode)
        return LVM_V2:Material("keys/" .. keycode .. ".png", "mips")
    end
end