---------------------------------------
   ---@author: WQ
   ---@time: 18/05/2025 16:17
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------

LVM_V2.Pages["Library"]["Keikkei Genkai"] = {};
LVM_V2.Pages["Library"]["Keikkei Genkai"].Paint = function(self, w, h)
    if self.Index == 1 then
        surface.SetMaterial(LVM_V2:Material("learning/genkai_".. (self.Index or 1) ..".png"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(SW(200), SH(60), SW(1350), SH(700))
    elseif self.Index == 2 then
        surface.SetMaterial(LVM_V2:Material("learning/genkai_".. (self.Index or 1) ..".png"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(SW(40), SH(60), SW(1500), SH(700))
    elseif self.Index == 3 then
        surface.SetMaterial(LVM_V2:Material("learning/genkai_".. (self.Index or 1) ..".png"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(SW(40), SH(60), SW(1350), SH(700))
    end

end

LVM_V2.Pages["Library"]["Keikkei Genkai"].After = function(panel)
    local dpanel = nil;
    
    local genkai = {
        [1] = {
            ["Jiton"] = { x = 355, y = 200 },
            ["Futton"] = { x = 720, y = 580 },
            ["Shakuton"] = { x = 1080, y = 200 },
        },
        [2] = {
            ["Bakuton"] = { x = 430, y = 200 },
            ["Hyoton"] = { x = 793, y = 580 },
            ["Kiminari"] = { x = 1150, y = 200 },
        },
        [3] = {
            ["Inkuton"] = { x = 509, y = 200 },
            ["Jinton"] = { x = 873, y = 580 },
            ["Mokuton"] = { x = 1235, y = 200 },
        }
    }
    
    local data = {
        [1] = {Jiton = {}, Futton = {}, Shakuton = {}},
        [2] = {Bakuton = {}, Kiminari = {}, Hyoton = {}},
        [3] = {Inkuton = {}, Jinton = {}, Mokuton = {}}
    }
    
    local function selectPage(index)
        if IsValid(dpanel) then dpanel:Remove() end
        panel.Index = index;
        
        dpanel = vgui.Create("DPanel", panel)
        dpanel:Dock(FILL)
        dpanel.Paint = nil;
    
        local button_pos = {
            { x = 100, y = -111 }, -- 1
            
            { x = 135, y = -40 }, -- 2
            
            { x = 150, y = 45 }, -- 3
            
            { x = 110, y = 121 }, -- 4
            
            { x = 45, y = 170 }, -- 5
            
            { x = -45, y = 170 }, -- 6
            
            { x = -110, y = 121 }, -- 7
            
            { x = -150, y = 40 }, -- 8
            
            { x = -135, y = -45 }, -- 9
        
            { x = -100, y = -111 }, -- 10
        }
        
        
        local tree_pos = genkai[index]
        
        local tree_data = data[index]
        
        for i = 1, #DLVM.Technics do
            if tree_data[DLVM.Technics[i].Nature] != nil then
                tree_data[DLVM.Technics[i].Nature] = {};
            end
        end
        
        for i = 1, #DLVM.Technics do
            if tree_data[DLVM.Technics[i].Nature] != nil then
                local tech = DLVM.Technics[i]
                
                tree_data[tech.Nature][#tree_data[tech.Nature] + 1] = {
                    Id = i,
                    Name = tech.Name,
                    Description = tech.Description,
                    Icon = tech.Icon,
                    PDC = tech.PDC,
                    Rang = tech.Rang
                }
            end
        end
        
        local function ShowTooltip(bool, name, rang)
            
            if not bool then panel.PaintOver = nil; return end
            
            surface.SetFont(LVM_V2.Fonts("LVM", 9, 500))
            local w, h = surface.GetTextSize(name)
            
            panel.PaintOver = function(self)
                local x, y = panel:LocalCursorPos()
                x = x + SW(10)
            
                draw.RoundedBox(20, x, y, w + SW(20), SH(60), Color(50, 50, 50))
                draw.SimpleText(name, LVM_V2.Fonts("LVM", 9, 500), x + SW(8), y + SH(5), color_white, TEXT_ALIGN_LEFT)
                draw.SimpleText("Rareté:", LVM_V2.Fonts("LVM", 7, 500), x + SW(8), y + SH(30), Color(200, 200, 200), TEXT_ALIGN_LEFT)
                draw.SimpleText(rang, LVM_V2.Fonts("LVM", 7, 500), x + SW(60), y + SH(30), DLVM.RarityColors[rang], TEXT_ALIGN_LEFT)
            end
            
        end
        
        for tree_name,tree in pairs(tree_pos) do
            for i = 1, #button_pos do
                local w, h = SW(50), SH(50)
                
                local technic = tree_data[tree_name][i] == nil and {
                    Id = -1,
                    Name = "An error occured",
                    Description = "Error",
                    Icon = "",
                    Rang = "?",
                    PDC = -1
                } or tree_data[tree_name][i]
                
                local is_posses = LocalPlayer():HasJutsu(technic.Id)
                local dbutton = vgui.Create("DButton", dpanel)
                dbutton:SetSize(w, h)
                dbutton:SetPos(SW(tree.x) + SW(button_pos[i].x) - w/2, SH(tree.y) + SH(button_pos[i].y) - h/2)
                dbutton:SetText("")
                -- dbutton:SetTooltip("(".. technic.Rang ..") "..technic.Name)
                dbutton.Paint = function(self, w, h)
                
                    is_posses = LocalPlayer():HasJutsu(technic.Id)
                
                    LVM_V2.Pages.DrawCircle(w/2, h/2, h/2, 360, (is_posses and color_white or Color(100, 100, 100)))
                
                    local icon = LVM_V2.Pages.GetIcon(technic)
                
                    masks.Start()
                        if icon != nil then
                            surface.SetMaterial(util.ImgurToMaterial(icon, "smooth noclamp"))
                        else
                            surface.SetMaterial(LVM_V2:Material("learning/no_icon.png"))
                        end
                        surface.SetDrawColor((is_posses and Color(255,255,255) or Color(100,100,100)))
                        surface.DrawTexturedRect(0, 0, w, h)
                    masks.Source()
                        LVM_V2.Pages.DrawCircle(w/2, h/2, h*0.45, 360, color_white)
                    masks.End()
                end
                dbutton.OnCursorEntered = function()
                    local x, y = gui.MousePos()
                
                    ShowTooltip(true, technic.Name, technic.Rang)
                end
                dbutton.OnCursorExited = function()
                
                    ShowTooltip(false)
                    
                end
                dbutton.DoClick = function()
                    if not is_posses then
                        LVM_V2.Pages.UnlockSkill(technic)
                    else
                        LVM_V2.Pages.UpgradeSkill(technic)
                    end
                end
            end
        end
        
        if index == 1 then
            local dbutton = vgui.Create("DButton", dpanel)
            dbutton:SetSize(SW(148), SH(700))
            dbutton:SetPos(SW(1400), SH(60))
            dbutton:SetText("")
            dbutton.Paint = nil;
            dbutton.DoClick = function()
                selectPage(2)
            end
        elseif index == 2 then
            local dbutton = vgui.Create("DButton", dpanel)
            dbutton:SetSize(SW(145), SH(700))
            dbutton:SetPos(SW(36), SH(60))
            dbutton:SetText("")
            dbutton.Paint = nil;
            dbutton.DoClick = function()
                selectPage(1)
            end
        
            local dbutton = vgui.Create("DButton", dpanel)
            dbutton:SetSize(SW(148), SH(700))
            dbutton:SetPos(SW(1400), SH(60))
            dbutton:SetText("")
            dbutton.Paint = nil;
            dbutton.DoClick = function()
                selectPage(3)
            end
        elseif index == 3 then
            local dbutton = vgui.Create("DButton", dpanel)
            dbutton:SetSize(SW(145), SH(700))
            dbutton:SetPos(SW(36), SH(60))
            dbutton:SetText("")
            dbutton.Paint = nil;
            dbutton.DoClick = function()
                selectPage(2)
            end
        end
        
        
    end
    
    selectPage(1)
end

LVM_V2.Pages["F4"]["Keikkei Genkai"] = {};
 
 LVM_V2.Pages["F4"]["Keikkei Genkai"].Paint = function(self, w, h)
     surface.SetMaterial(LVM_v2:Material("f4/jutsu.png"))
     surface.SetDrawColor(color_white)
     surface.DrawTexturedRect(SW(110), SH(50), SW(1383), SH(710))
     
     if self.jutsu_actual == nil then return end
     for i = 1, 6 do
         
         local x, y = SW(180) + SW(170 * (i-1)), SH(640)
         local jutsu_id = self.jutsu_actual[i]
 
         if DLVM.Technics[jutsu_id] != nil then
            masks.Start()
            if DLVM.Technics[jutsu_id].Icon != "" then
                surface.SetMaterial(util.ImgurToMaterial(DLVM.Technics[jutsu_id].Icon, "smooth mips"))
            else
                surface.SetMaterial(LVM_V2:Material("learning/no_icon.png"))
            end
            surface.SetDrawColor(color_white)
            surface.DrawTexturedRect(x - SW(1) * 1.2, y - SH(3), SW(80), SH(80))
            masks.Source()
            DrawCircle(x + SW(37) + SW(2) * 0.8, y + SH(37.5), SH(35), 360, nil)
            masks.End()
        end
         
     end
     
     if self.TechClicked != nil then
         
         local tech = DLVM.Technics[self.TechClicked]
         if tech == nil then return end
         
         if tech.Icon != "" then
            surface.SetMaterial(util.ImgurToMaterial(tech.Icon, "smooth mips"))
            surface.SetDrawColor(color_white)
            surface.DrawTexturedRect(SW(1255), SH(120), SW(128), SH(128))
         else
            surface.SetMaterial(LVM_V2:Material("learning/no_icon.png"))
            surface.SetDrawColor(color_white)
            surface.DrawTexturedRect(SW(1255), SH(120), SW(128), SH(128))
         end
         
         draw.SimpleText(tech.Name, LVM_V2.Fonts("LVM", 8, 500), SW(1180), SH(70), color_white, TEXT_ALIGN_LEFT)
         
         draw.SimpleText("Description", LVM_V2.Fonts("LVM", 5, 500), SW(1180), SH(320), Color(200, 200, 200, 30), TEXT_ALIGN_LEFT)
         
     end
     draw.RoundedBoxEx(6, SW(115), SH(57), SW(688), SH(34), Color(29, 29, 29), true, false, false, false)
    
     draw.SimpleText("Keikkei Genkai", LVM_V2.Fonts("LVM", 7, 500), SW(130), SH(63), color_white, TEXT_ALIGN_LEFT)
     
     draw.RoundedBoxEx(6, SW(802), SH(57), SW(344), SH(34), Color(32, 32, 32), false, true, false, false)
 end
 
 LVM_V2.Pages["F4"]["Keikkei Genkai"].After = function(panel)
     
    local jutsu_old;
    panel.jutsu_actual = {}
    timer.Simple(0.1, function()
        jutsu_old = {
            DLVM.Binds["Jutsu1"]:GetInt(),
            DLVM.Binds["Jutsu2"]:GetInt(),
            DLVM.Binds["Jutsu3"]:GetInt(),
            DLVM.Binds["Jutsu4"]:GetInt(),
            DLVM.Binds["Jutsu5"]:GetInt(),
            DLVM.Binds["Jutsu6"]:GetInt()
        }
        
        panel.jutsu_actual = jutsu_old
    end)
    
    local search
    

    
    local scrollpanel = vgui.Create("DScrollPanel", panel)
    scrollpanel:SetSize(SW(1010), SH(491))
    scrollpanel:SetPos(SW(130), SH(110))
     
    local dtextentry = vgui.Create("DTextEntry", panel)
    dtextentry:SetSize(SW(300), SH(40))
    dtextentry:SetPos(SW(820), SH(52))
    --  dtextentry:SetBackgroundColor(Color(23, 23, 23))
    dtextentry:SetPaintBackground(false)
    dtextentry:SetDrawLanguageID(false)
    dtextentry:SetFont(LVM_V2.Fonts("LVM", 8, 500))
    dtextentry:SetPlaceholderColor(Color(200, 200, 200, 50))
    dtextentry:SetPlaceholderText("Rechercher...")
    dtextentry:SetTextColor(color_white)
    dtextentry:SetCursorColor(Color(200, 200, 200, 200))
    dtextentry.OnTextChanged = function(self)
        search(self:GetValue())
    end
     
     local vbar = scrollpanel:GetVBar()
     vbar:SetWide(SW(4))
     function vbar.btnUp:Paint(w, h)
     end
     
     function vbar.btnDown:Paint(w, h)
     end
     
     function vbar.btnGrip:Paint(w, h)
         draw.RoundedBox(8, 0, 0, w, h, Color(100, 100, 100, 200))
     end
     
     function vbar:Paint(w, h)
         draw.RoundedBox(8, 0, 0, w, h, Color(0, 0, 0, 200))
     end
     
     local keikkei_genkai = LocalPlayer():GetKeikkeiGenkai()
     
     local tooltip;
     local selected_id = 0;
     
     local function ShowTooltip(bool, name, rang)
         
         if not bool then panel.PaintOver = nil; return end
         
         surface.SetFont(LVM_V2.Fonts("LVM", 9, 500))
         local w, h = surface.GetTextSize(name)
         
         panel.PaintOver = function(self)
             local x, y = panel:LocalCursorPos()
             x = x + SW(10)
         
             draw.RoundedBox(20, x, y, w + SW(20), SH(60), Color(50, 50, 50))
             draw.SimpleText(name, LVM_V2.Fonts("LVM", 9, 500), x + SW(8), y + SH(5), color_white, TEXT_ALIGN_LEFT)
             draw.SimpleText("Rareté:", LVM_V2.Fonts("LVM", 7, 500), x + SW(8), y + SH(30), Color(200, 200, 200), TEXT_ALIGN_LEFT)
             draw.SimpleText(rang, LVM_V2.Fonts("LVM", 7, 500), x + SW(60), y + SH(30), DLVM.RarityColors[rang], TEXT_ALIGN_LEFT)
         end
         
     end
     
     for i = 1, 3 do
        local dbutton = vgui.Create("DButton", panel)
        dbutton:SetSize(SW(30), SH(30))
        dbutton:SetPos(SW(70), SH(640 + (i-1)*35))
        dbutton:SetText("")
        dbutton.Paint = function(s, w, h)
            draw.SimpleText(i, LVM_V2.Fonts("LVM", 7, 500), w/2, h/2, (DLVM.Binds["Tree"]:GetInt() == i and color_white or Color(200, 200, 200, 200)), 1, 1)
        end
        dbutton.DoClick = function()
            DLVM.Binds["Tree"]:SetInt(i)
            DLVM:RetrieveBinds(LocalPlayer():GetNWInt("CharacterID", 1), i)
            
            panel.jutsu_actual = {
                DLVM.Binds["Jutsu1"]:GetInt(),
                DLVM.Binds["Jutsu2"]:GetInt(),
                DLVM.Binds["Jutsu3"]:GetInt(),
                DLVM.Binds["Jutsu4"]:GetInt(),
                DLVM.Binds["Jutsu5"]:GetInt(),
                DLVM.Binds["Jutsu6"]:GetInt()
            }
        end
     end
     
     local richtext = vgui.Create("RichText", panel)
     richtext:SetSize(SW(270), SH(110))
     richtext:SetPos(SW(1180), SH(340))
     function richtext:PerformLayout()
         self:SetFontInternal(LVM_V2.Fonts("LVM", 5, 500))
     end
     richtext:InsertColorChange( 255, 255, 255, 255 )
     
     local function SetDescription(text, level)
        richtext:SetText("")

        richtext:InsertColorChange(255, 255, 255, 255)
        richtext:AppendText(text.."\n")

        richtext:InsertColorChange(255, 255, 0, 255)
        richtext:AppendText("Niveau : " .. level .. "\n")

        local id = selected_id;
         LVM_V2:GetConfigJutsu(id, function(config)
            if id != selected_id then print("id not valid") return end
            if not IsValid(richtext) then print("richtext not valid") return end
            local jutsuConfig = LVM_V2:GetConfigJutsuLevel(config, level)
            
            for key, value in pairs(jutsuConfig) do
                local color = LVM_V2.Equilibrage.Config["Color"][key] or color_white
                local text = (LVM_V2.Equilibrage.Config["Key"][key] or key)
    
                richtext:InsertColorChange(color.r, color.g, color.b, 255)
                richtext:AppendText(text.." : ")
    
                richtext:AppendText(value)
                
                richtext:AppendText("\n")
            end
        end)
     end
     
     local natures = DLVM.Keikkei_Genkai
     
    function search(searched)
        scrollpanel:Clear()
        
        local function AddNature(name)
            if name == "none" then return end
            
            local is_posses = (LocalPlayer():IsDev() and true or (keikkei_genkai == name))
        
            local menu = vgui.Create("DPanel", scrollpanel)
            menu:SetSize(SW(1010), SH(150))
            menu:DockMargin(0, 0, 0, SH(10))
            menu:Dock(TOP)
            menu.Paint = function(self, w, h)
                if is_posses then
                    surface.SetMaterial(LVM_V2:Material("F4/bar.png"))
                    surface.SetDrawColor(color_white)
                    surface.DrawTexturedRect(0, 0, w, h)
                    
                    draw.SimpleText(name, LVM_V2.Fonts("LVM", 6, 500), w/2, SH(10), color_white, TEXT_ALIGN_CENTER)
                else
                    surface.SetMaterial(LVM_V2:Material("F4/bar.png"))
                    surface.SetDrawColor(color_white)
                    surface.DrawTexturedRect(0, 0, w, h)
                    
                    draw.SimpleText(name, LVM_V2.Fonts("LVM", 6, 500), w/2, SH(10), Color(255, 255, 255, 100), TEXT_ALIGN_CENTER)
                    
                    surface.SetMaterial(LVM_V2:Material("F4/bar_locked.png"))
                    surface.SetDrawColor(color_white)
                    surface.DrawTexturedRect(0, 0, w, h)
                end
            end
            
            if is_posses then
                
                local nature_tech = {}
                for i = 1, #DLVM.Technics do
                    if DLVM.Technics[i].Nature == name and DLVM.Technics[i].Name:lower():match(searched:lower()) == searched:lower() then
                        local index = #nature_tech+1
                        nature_tech[index] = DLVM.Technics[i]
                        nature_tech[index].TechID = i;
                    end
                end
                
                for i = 1, #nature_tech do
                    local dbutton = vgui.Create("DButton", menu)
                    dbutton:SetSize(SW(75), SH(75))
                    dbutton:SetPos(SW(23) + SW(99*(i-1)), SH(40))
                    dbutton:SetText("")
                    dbutton.TechID = nature_tech[i].TechID
                    dbutton.Paint = function(s, w, h)
                        surface.SetMaterial(LVM_V2:Material("f4/jutsu_slot.png"))
                        surface.SetDrawColor(color_white)
                        surface.DrawTexturedRect(0, 0, w, h)
                        
                        masks.Start()
                            surface.SetMaterial(util.ImgurToMaterial(nature_tech[i].Icon, "smooth mips"))
                            surface.SetDrawColor((LocalPlayer():HasJutsu(nature_tech[i].TechID) and color_white or Color(100,100,100)))
                            surface.DrawTexturedRect(0, 0, w, h)
                        masks.Source()
                            LVM_V2.Pages.DrawCircle(SW(38), SH(37), SH(32), 360, nil)
                        masks.End()
                    end
                    dbutton.DoClick = function()
                        panel.TechClicked = nature_tech[i].TechID
                        local techid = nature_tech[i].TechID
                        
                        local jutsulevel = (LocalPlayer().LVM_Jutsu == nil and 0 or (LocalPlayer().LVM_Jutsu[techid] or 0))
                        selected_id = techid;

                        
                        SetDescription(nature_tech[i].Description, jutsulevel)

                        if input.IsKeyDown(KEY_LSHIFT) then 
                            techid = nature_tech[i].TechID
                            if techid and LocalPlayer():HasJutsu(techid) then
                                for i = 1, 6 do
                                    if panel.jutsu_actual[i] == 0 then 
                                        panel.jutsu_actual[i] = techid
                                        DLVM.Binds["Jutsu" .. i]:SetInt(techid)
                                        return
                                    end
                                end
                            end
                        end

                    end
                    if LocalPlayer():HasJutsu(nature_tech[i].TechID) then
                        dbutton:Droppable("abre_jutsu")
                    end
                    dbutton.OnCursorEntered = function()
                        ShowTooltip(true, nature_tech[i].Name, nature_tech[i].Rang)
                    end
                    dbutton.OnCursorExited = function()
                        ShowTooltip(false)
                    end

                    dbutton.DoRightClick = function(self)
                        if input.IsKeyDown(KEY_LSHIFT) then 
                            local techID = self.TechID
                            if techID and LocalPlayer():HasJutsu(techID) then
                                for i = 1, 6 do
                                    if panel.jutsu_actual[i] == 0 then 
                                        panel.jutsu_actual[i] = techID
                                        DLVM.Binds["Jutsu" .. i]:SetInt(techID)
                                        return
                                    end
                                end
                            end
                        end
                    end
                end
                
            end
        end
        
        local function AddNatures(tbl)
            local tbl2 = {}
            for i = 1, #tbl do
                if tbl2[tbl[i]] == nil then
                    AddNature(tbl[i])
                    tbl2[tbl[i]] = true
                end
            end
        end
        
        AddNatures(natures)
     end
     
     search("")

     
     for i = 1, 6 do
     
         local button = vgui.Create("DButton", panel)
         button:SetSize(SW(90), SH(80))
         button:SetPos(SW(180) + SW(170 * (i-1)), SH(640))
         button:SetText("")
         button.Paint = nil;
         button.Tech = panel.jutsu_actual[i]
         button.OnCursorEntered = function(self)
             if DLVM.Technics[self.Tech] == nil then return end
             ShowTooltip(true, DLVM.Technics[self.Tech].Name, DLVM.Technics[self.Tech].Rang)
         end
         button.DoRightClick = function(self)
             panel.jutsu_actual[i] = 0;
             self.Tech = 0
             ShowTooltip(false)
         end
         button.OnCursorExited = function()
             ShowTooltip(false)
         end
         button:Receiver("abre_jutsu", function(self, tbl, dropped, menu, x, y)
             if not dropped then return end
             
             local jutsu = tbl[1]
             if jutsu == nil then return end
             
             if !LocalPlayer():HasJutsu(jutsu.TechID) then return end
             
             panel.jutsu_actual[i] = jutsu.TechID
             self.Tech = jutsu.TechID
         end)
         
         local binder = vgui.Create("DBinder", panel)
         binder:SetSize(SW(25), SH(25))
         binder:SetPos(SW(230) + SW(170 * (i-1)), SH(700))
         binder:SetConVar("kjutsu_"..i)
         binder:SetFontInternal(LVM_V2.Fonts("LVM", 5, 500))
         binder:SetTextColor(color_white)
         binder.Paint = function(self, w, h)
             -- local key = input.GetKeyName(tonumber(self:GetValue()))
             draw.RoundedBox(8, 0, 0, w, h, Color(71, 71, 71))
             -- draw.SimpleText(key, LVM_V2.Fonts("LVM", 5, 500), w/2, h/2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
         end
         
     end
     
     local button = vgui.Create("DPanel", panel)
     button:SetSize(SW(296), SH(70))
     button:SetPos(SW(1175), SH(625))
     -- button:SetConVar("kpermutation")
     button:SetText("")
     button.Paint = function(self, w, h)
         draw.RoundedBox(0, 0, 0, w, h, Color(29, 29, 29))
         
         surface.SetMaterial(util.ImgurToMaterial("ReyiIP4.png", "smooth"))
         surface.SetDrawColor(color_white)
         surface.DrawTexturedRect(SW(30), SH(15), SW(40), SH(40))
     
         draw.SimpleText("Permutation: ", LVM_V2.Fonts("LVM", 7, 500), SW(130), h / 2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
     end
     
     local dbinder = vgui.Create("DBinder", button)
     dbinder:SetSize(SW(80), SH(40))
     dbinder:SetPos(SW(180), SH(15))
     dbinder:SetConVar("kpermutation")
     dbinder:SetFontInternal(LVM_V2.Fonts("LVM", 7, 500))
     dbinder:SetTextColor(color_white)
     dbinder.Paint = function(self, w, h)
         draw.RoundedBox(4, 0, 0, w, h, Color(71, 71, 71, 200))
     end
 
     local button2 = vgui.Create("DPanel", panel)
     button2:SetSize(SW(296), SH(70))
     button2:SetPos(SW(1175), SH(545))
     button2:SetText("")
     button2.Paint = function(self, w, h)
        draw.RoundedBox(6, 0, 0, w, h, Color(29, 29, 29))
        
        draw.SimpleText("Style de combat: ", LVM_V2.Fonts("LVM", 7, 500), SW(100), h / 2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        draw.SimpleText("*Le style de combat n'affecte que les animations*", LVM_V2.Fonts("LVM", 4, 500), SW(40), h / 2 + SH(25), Color(200, 200, 200, 30), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
     end
     
     local dstyle = vgui.Create("DButton", button2)
     dstyle:SetSize(SW(80), SH(40))
     dstyle:SetPos(SW(180), SH(15))
     dstyle:SetText("")
     dstyle.Paint = function(self, w, h)
         draw.RoundedBox(4, 0, 0, w, h, Color(71, 71, 71, 200))
         draw.SimpleText(DLVM.Binds.FightStyle:GetInt() == 1 and "Bandit" or (DLVM.Binds.FightStyle:GetInt() == 2 and "Conventionnel" or "Technique"), LVM_V2.Fonts("LVM", 5, 500), w/2, h/2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
     end
     dstyle.DoClick = function()
        local menu = DermaMenu(false, button2)
        menu:SetPos(SW(1175), SH(545))
        menu:SetSize(SW(296), SH(70))
        
        local dbutton = menu:AddOption("Bandit", function()
            DLVM.Binds.FightStyle:SetInt(1)
        end):SetIcon("icon16/arrow_right.png")
        local dbutton = menu:AddOption("Conventionnel", function()
            DLVM.Binds.FightStyle:SetInt(2)
        end):SetIcon("icon16/arrow_right.png")
        local dbutton = menu:AddOption("Technique", function()
            DLVM.Binds.FightStyle:SetInt(3)
        end):SetIcon("icon16/arrow_right.png")

        
        menu:Open()
     end
     local function Save()
         for i = 1, 6 do
             DLVM.Binds["Jutsu" .. i]:SetInt(panel.jutsu_actual[i])
             jutsu_old[i] = panel.jutsu_actual[i]
         end
 
         DLVM:SaveBinds()
     end
     
     button2.OnRemove = Save
 end