---------------------------------------
   ---@author: WQ
   ---@time: 29/05/2025 22:12
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------

util.AddNetworkString("LVM_V2:BuyJutsu")
util.AddNetworkString("LVM_V2:UpgradeJutsu")
util.AddNetworkString("LVM_V2:BuyKenjutsu")
util.AddNetworkString("LVM_V2:UpgradeKenjutsu")


local convertclass = {[1] = "Médicin", [2] = "Éclaireur", [3] = "Combattant", [4] = "Rempart"}


net.Receive("LVM_V2:BuyJutsu", function(_, ply)
    local id = net.ReadUInt(8)
    local tech = DLVM.Technics[id]
    if tech == nil then return end

    if DLVM.Technics:<PERSON><PERSON><PERSON>u(ply, id) then
        DarkRP.notify(ply, 1, 5, "Vous avez déjà débloqué cette technique !")
        return
    end
        
    if DLVM.RankToJob[tech.Rang] != nil and ply:IfMinimumRank(DLVM.RankToJob[tech.Rang]) == false then
        DarkRP.notify(ply, 1, 5, "Vous devez être plus haut gradé pour lancer cette technique !")
        return
    end
    
    if DLVM.RankToLVL[tech.Rang] != nil and ply:GetLvl() < DLVM.RankToLVL[tech.Rang] then
        DarkRP.notify(ply, 1, 5, "Vous devez être niveau ".. DLVM.RankToLVL[tech.Rang] .." minimum pour cette technique !")
        return
    end
    
    local base_nature = ply.Nature or "None"
    local reroll_nature = ply.RerollNature or "None"
    local gived_nature = ply.GivedNature or "None"
    
    local keikkei_nature = ply.KeikkeiGenkai or "None"
    
    print("------------------------------------------------------")
    print("Information de l'utilisateur :".. ply:Nick() .. " (".. ply:SteamID() ..")")
    print("Base nature : "..base_nature)
    print("Reroll nature : "..reroll_nature)
    print("Gived nature : "..gived_nature)
    print("Keikkei nature : "..keikkei_nature)
    print("Spécialité : "..ply:GetSpeciality())
    print("Classe : "..ply:GetClasse())
    print("------------------------------------------------------")
    if not ply:IsBeforeJutsuUnlocked(id) then
        DarkRP.notify(ply, 1, 5, "Vous devez débloquer la technique "..DLVM.Technics[id-1].Name.." avant de débloquer celle-ci")
        return
    end
    
    if tech.CustomUnlock != nil then
        local canUnlock = tech.CustomUnlock(ply)
        
        if not canUnlock then
            DarkRP.notify(ply, 1, 5, "Vous ne remplissez pas les conditions nécéssaire au débloquage de cette technique !")
            return
        end
        
    elseif tech.Nature == "Taijutsu" then
        if ply:GetSpeciality() != 1 then
            DarkRP.notify(ply, 1, 5, "Vous ne pouvez pas debloquer une technique qui n'est pas de votre spécialité")
            return
        end
    elseif table.HasValue(convertclass, tech.Nature) then
        if convertclass[ply:GetClasse()] != tech.Nature then
            DarkRP.notify(ply, 1, 5, "Vous ne pouvez pas debloquer une technique qui n'est pas de votre classe")
            return
        end
    else
        if tech.Nature ~= base_nature and tech.Nature ~= reroll_nature and tech.Nature ~= gived_nature and tech.Nature ~= DLVM:GetClanById(ply:GetClan()) and tech.Nature ~= keikkei_nature then
            DarkRP.notify(ply, 1, 5, "Vous ne pouvez pas debloquer une technique qui n'est pas de votre nature")
            return
        end
    end
    

    if ply:GetPDC() < tech.PDC then 
        DarkRP.notify(ply, 1, 5, "Vous n'avez pas assez de PDC pour débloquer cette technique")
        return
    end
    
    if ply:GetPDC() <= 0 then return end
    
    DLVM.Technics:GiveJutsu(ply, id)
    
    ply:RemovePDC(tech.PDC)
    
    DLVM.Technics:SyncPlayer(ply)

    DarkRP.notify(ply, 0, 5, "Vous avez débloqué la technique : "..tech.Name)

    hook.Run("LVM:OnUnlcockJutsu", ply, id)
end)


net.Receive("LVM_V2:UpgradeJutsu", function(_, ply)
    local id = net.ReadUInt(8)
    local tech = DLVM.Technics[id]
    if tech == nil then return end
    
    local technics = DLVM.GC:GetByChar("jutsu", ply)
    if technics == nil then return end
    
    local hasJutsu = DLVM.Technics:HasJutsu(ply, id)

    if not hasJutsu then
        DarkRP.notify(ply, 1, 5, "Vous n'avez pas débloqué cette technique !")
        return
    end
    
    local jutsu = technics.owned_jutsu
    
    if jutsu[id].star >= 5 then return end
    
    local base_nature = ply.Nature or "None"
    local reroll_nature = ply.RerollNature or "None"
    local gived_nature = ply.GivedNature or "None"
    
    local keikkei_nature = ply.KeikkeiGenkai or "None"

    if tech.CustomUnlock != nil then
        local canUnlock = tech.CustomUnlock(ply)
        
        if not canUnlock then
            DarkRP.notify(ply, 1, 5, "Vous ne remplissez pas les conditions nécéssaire au débloquage de cette technique !")
            return
        end
        
    elseif tech.Nature == "Taijutsu" then
        if ply:GetSpeciality() != 1 then
            DarkRP.notify(ply, 1, 5, "Vous ne pouvez pas debloquer une technique qui n'est pas de votre spécialité")
            return
        end
    elseif table.HasValue(DLVM.Autres, tech.Nature) then
        if not hasJutsu then return end
    elseif table.HasValue(convertclass, tech.Nature) then
        if convertclass[ply:GetClasse()] != tech.Nature then
            DarkRP.notify(ply, 1, 5, "Vous ne pouvez pas debloquer une technique qui n'est pas de votre classe")
            return
        end
    else
        --print(ply:GetClan())
        if tech.Nature ~= base_nature and tech.Nature ~= reroll_nature and tech.Nature ~= gived_nature and tech.Nature ~= DLVM:GetClanById(ply:GetClan()) and tech.Nature ~= keikkei_nature then
            DarkRP.notify(ply, 1, 5, "Vous ne pouvez pas debloquer une technique qui n'est pas de votre nature")
            return
        end
    end
    

    if tech.Upgrades[jutsu[id].star + 1] == nil then return end
    
    local pdcstar = tech.Upgrades[jutsu[id].star+1]

    if ply:GetPDC() <= 0 then return end
    if ply:GetPDC() < pdcstar then
        DarkRP.notify(ply, 1, 5, "Vous n'avez pas assez de PDC pour améliorer cette technique")
        return
    end
    
    DLVM.Technics:UpgradeJutsu(ply, id)

    DLVM.Technics:SyncPlayer(ply)

    ply:RemovePDC(pdcstar)
    DarkRP.notify(ply, 0, 5, "Vous avez améliorer votre technique : "..tech.Name)
end)

concommand.Add("set_jutsu_upgrade", function(ply, cmd, args)

    if not ply:IsSuperAdmin() then return end
    
    local technics = DLVM.GC:GetByChar("jutsu", ply)
    if technics == nil then return end
    
    local jutsu = technics.owned_jutsu
    if not jutsu then return end
    -- if not jutsu[tonumber(args[1])].star then return end
    if jutsu[tonumber(args[1])] == nil then
        jutsu[tonumber(args[1])] = {owned_at = os.time(), updated_at = os.time(), star = tonumber(args[2])}
    else
        jutsu[tonumber(args[1])].star = tonumber(args[2])
    end
    
    technics.Push(technics)
    
    DLVM.Technics:SyncPlayer(ply)
    
end)

net.Receive("LVM_V2:BuyKenjutsu", function(_, ply) 
    local id = net.ReadUInt(3)
    if ply:GetSpeciality() != 2 then
        return
    end

    
    if DLVM:HasKenjutsu(ply, id) then
        return
    end
     
    if ply:GetPDC() < DLVM.KenjutsuUnlock[id] then return end
    

    DLVM.Kenjutsu:GiveKenjutsu(ply, id)
    
    ply:AddPDC(-DLVM.KenjutsuUnlock[id])
    
    DLVM.Technics:SyncPlayer(ply)

    DarkRP.notify(ply, 0, 5, "Vous avez débloqué la technique : "..DLVM.Kenjutsu[ply.Kenjutsu].Technics[id].Name)
end)

net.Receive("LVM_V2:UpgradeKenjutsu", function(_, ply)
    local id = net.ReadUInt(3)

    if ply:GetSpeciality() != 2 then
        return
    end
    
    if not DLVM:HasKenjutsu(ply, id) then
        return
    end
    
    local star = ply:KenjutsuStar(id)
    
    if star+1 > 5 then return end
    
    if ply:GetPDC() < DLVM.KenjutsuUpgrades[star+1] then return end
    
    DLVM.Kenjutsu:UpgradeKenjutsu(ply, id)
    
    ply:AddPDC(-DLVM.KenjutsuUpgrades[star+1])
    
    DLVM.Technics:SyncPlayer(ply)


   DarkRP.notify(ply, 0, 5, "Vous avez amélioré la technique : "..DLVM.Kenjutsu[ply.Kenjutsu].Technics[id].Name)
end)

util.AddNetworkString("LVM:SelectMySpeciality")

local convertSpeciality = {[1] = "Taijutsu", [2] = "Kenjutsu"}

net.Receive("LVM:SelectMySpeciality", function(_, ply)
    
    local int = net.ReadUInt(2)
    
    if ply:GetSpeciality() != 0 then return end
    if int == 0 then return end
    
    local jutsu = DLVM.GC:GetByChar("jutsu", ply)
    if jutsu == nil then return end
    
    jutsu.nature.speciality = int;
    
    ply.Speciality = int;
    
    jutsu.Push(jutsu)
    
    DarkRP.notify(ply, 0, 5, "Félicitations, vous avez choisi votre spécialité ! (".. convertSpeciality[int] ..")")
    
    DLVM.Technics:SyncPlayer(ply)

    if int == 2 then 
        local item = net.ReadString()
        WQ_Inventory:AddInventory(ply, item, 1, ply.SelectedCharacter)
    end

end)

util.AddNetworkString("LVM:SelectClass")


net.Receive("LVM:SelectClass", function(_, ply)
    
    local int = net.ReadUInt(3)
    
    if ply:GetClasse() != 0 then return end
    if int == 0 then return end
    
    local jutsu = DLVM.GC:GetByChar("jutsu", ply)
    if jutsu == nil then return end
    
    jutsu.nature.classe = int;
    
    ply.Classe = int;
    
    jutsu.Push(jutsu)
    
    DarkRP.notify(ply, 0, 5, "Félicitations, vous avez choisi votre Classe ! (".. convertclass[int] ..")")
    
    DLVM.Technics:SyncPlayer(ply)
end)

-- util.AddNetworkString("LVM:Clan:Set")
-- net.Receive("LVM:Clan:Set", function(_, ply)

--     local target = net.ReadEntity()
--     local int = net.ReadUInt(3)

--     if target == nil or ! target:IsPlayer() then return end

--     local jutsu = DLVM.GC:GetByChar("jutsu", target)
--     if jutsu == nil then return end

--     jutsu.nature.Clan = int;
    
--     target.Clan = int;
    
--     jutsu.Push(jutsu)
    
--     DarkRP.notify(target, 0, 5, "Félicitations, vous integrer le clan ! (".. DLVM.Clans[int] ..")")
    
--     DLVM.Technics:SyncPlayer(target)

-- end)


concommand.Add("roll_kg", function(ply, cmd, args)
    if IsValid(ply) and not ply:IsPlayer() then return end
    
    local result = {}
    
    for i = 1, tonumber(args[1]) do
        local kg = LVM_V2.Reroll:GetRandom(2)
        result[kg] = (result[kg] or 0) + 1;
    end
    
    print("Taux de drop (".. args[1] .." roll) : \n")
    for k,v in pairs(result) do
        print(k.." : "..v)
    end
    
end)