---------------------------------------
   ---@author: WQ
   ---@time: 03/05/2025 19:09
   ---@version: 1.0.0
   ---@server: ©️ LVM - Naruto RP
 ---------------------------------------

net.Receive("LVM:MySkills", function()

    local order = {
        "strength",
        "speed",
        "resistance",
        "jump",
        "chakra"
    }
    
    LocalPlayer().PDC = net.ReadUInt(10)
    
    for i = 1, #order do
        LocalPlayer()[order[i]:upper()] = math.Clamp(net.ReadUInt(4), 0, 10)
    end
    
    print("[WQ] Finished to syncronise skill&pdc on ur character !")
    
end)

local PLAYER = FindMetaTable("Player")

---@return integer
function PLAYER:GetPDC()
    return (self.PDC == nil and 0 or self.PDC)
end

---@param skill string
---@return integer
function PLAYER:GetSkill(skill)
    if self[skill:upper()] == nil then return 0 end

    return self[skill:upper()]
end


hook.Remove("PlayerFootstep", "LVM:PlayerFootstep")
hook.Add("PlayerFootstep", "LVM:PlayerFootstep", function(pPlayer, vecPos, iFoot, sSound, iVolume, cFilter)
    if pPlayer:GetNetworkVar("LVM:WQ:InvisibiliterFuma", false) then return end
    if pPlayer:GetNetworkVar("Invisibility", false) then return end
    if pPlayer:AdminMode() and not pPlayer:IsAnimateur() then return end
    timer.Simple(0, function()
        ParticleEffectAttach("izox_mud_foot", PATTACH_ABSORIGIN_FOLLOW, pPlayer, 0)
    end)

end)


net.Receive("LVM:EFFECT:NARUTORUN", function()
    if IsFirstTimePredicted() then return end
    local pPlayer = LocalPlayer()
    local bActivate = net.ReadBool()
    if not IsValid(pPlayer) then return end
    
    if bActivate then 
        local eff = EffectData()
        eff:SetEntity(pPlayer)
        util.Effect("nrp_fasting", eff, true, true)
    end
end)

local Player = FindMetaTable("Player")
function Player:PlayAnim(anim, loop, freeze, speed)
    if self != LocalPlayer() then return end
    net.Start("LVM:RequestAnimations")
        net.WriteString(anim)
        net.WriteBool(freeze == nil and false or freeze)
        net.WriteBool(loop == nil and false or loop)
        if speed != nil then
            net.WriteFloat(speed)
        end
    net.SendToServer()
    
    return self:SequenceDuration(self:LookupSequence(anim))
end

function Player:ResetAnimation()
    if self != LocalPlayer() then return end
    net.Start("LVM:RequestAnimations")
        net.WriteString("reset")
        net.WriteBool(false)
    net.SendToServer()
end


hook.Add("OnPlayerHitGround", "LVM:JUMPPARTICLE", function(pPlayer, bInWater, bOnFloater, flFallSpeed)
    
    if flFallSpeed < 450 then return end

    -- if pPlayer:Crouching() then
    --     pPlayer:PlayAnim("nrp_lobby_etc_emotion_chakralost_start")
    -- else
    --     pPlayer:PlayAnim("nrp_base_aerial_dash_end", false, 1.5)
    -- end
    
    pPlayer:EmitSound("narutorp/movement/jumpland.mp3", 75, 100, flFallSpeed / 1200) 

    local tr = util.QuickTrace(pPlayer:GetPos(), Vector(0, 0, -1000), pPlayer)
    ParticleEffect("sh_floorjump", tr.HitPos, Angle(0, 0, 0))
 --   ParticleEffect("izox_smoke_jump", tr.HitPos, Angle(0, 0, 0))

    pPlayer.playGroundImpact = true
end)



local iAngle = 15
local cBlue = Color(0, 162, 255, 255)
local iMaxChargeTime = 3
local bPressJumpKey = false
local iStartCooldown

hook.Add("PlayerBindPress", "LVM:PlayerBindPress:Chakra", function(pPlayer, sBind, bPressed)

    local pLocal = LocalPlayer()

    local vel = pLocal:GetVelocity()
    if vel:Length() > 100 then return end   

    local angEye = pLocal:EyeAngles()  
    if pPlayer:GetNetworkVar("LVM:PainMode", false) then return end

    if pLocal:KeyDown(IN_DUCK) and sBind == "+jump" and bPressed then

        local iCurTime = CurTime()

        if pPlayer:GetNetworkVar("iChakraJumpCooldown", 0) > iCurTime then return end

        local iDuration = pPlayer:PlayAnim("nrp_base_chakrajump_charge_start")

        timer.Simple(iDuration - 0.2, function()
            pPlayer:PlayAnim("nrp_base_chakrajump_charge_loop", true)
        end)

        bPressJumpKey = true
        iStartCooldown = iCurTime

        pPlayer:SetMoveType(MOVETYPE_NONE)

        return true

    else
        
        if sBind == "+voicerecord" then return end
        if pLocal:KeyDown(IN_JUMP) then return end


        if iStartCooldown and bPressJumpKey then
            
            local iTimePassed = math.Clamp(CurTime() - iStartCooldown, 0, iMaxChargeTime)
            
            pPlayer:ResetAnimation()
            if iTimePassed > (iMaxChargeTime*0.4) then

                net.Start("LVM:ChakraJump:Jump")
                    net.WriteUInt(math.Clamp((CurTime() - iStartCooldown)/iMaxChargeTime, 0, 1)*100, 7)
                net.SendToServer()
            end


            iStartCooldown = nil
            bPressJumpKey = false

            if IsValid(pLocal.ChakraJumpIndication) then

                pPlayer:PlayAnim("nrp_base_chakrajump_charge_end")
                SafeRemoveEntity(pLocal.ChakraJumpIndication)
                
                pPlayer:ResetAnimation()
            end    
        end

        pPlayer:SetMoveType(MOVETYPE_WALK)
        pPlayer:ResetAnimation()
    end 
end)


local iOldHealth = 0

hook.Add("PostDrawTranslucentRenderables", "LVM:PostDrawTranslucentRenderables:ChakraJump", function(bDrawingDepth, bDrawingSkybox)

    local pLocal = LocalPlayer()
    if not IsValid(pLocal) then return end

    if not pLocal:Alive() then return end
    
    if not pLocal:IsOnGround() then 
        local iDistanceToGround = pLocal:GetDistanceToGround()
        if pLocal:GetVelocity():Length() > 500 and iDistanceToGround > 100 then
            if pLocal:GetMoveType() == MOVETYPE_NOCLIP then return end
            if pLocal:GetNetworkVar("bWasDoubleJumping", false) then return end
            if pLocal:GetNetworkVar("OnAirEcrasement", false) then return end
            pLocal:PlayAnim("nrp_base_aerial_dash_loop")
        end
    end

    if not pLocal:Crouching() or iOldHealth > pLocal:Health() then 

        bPressJumpKey = false; iStartCooldown = nil
        iOldHealth = pLocal:Health()

        if IsValid(pLocal.ChakraJumpIndication) then

            SafeRemoveEntity(pLocal.ChakraJumpIndication)
            pLocal:ResetAnimation()
        end

        return 
        
    end
    
    iOldHealth = pLocal:Health()

    if not bPressJumpKey then return end
    local iTimePassed = math.Clamp(CurTime() - (iStartCooldown or 0), 0, iMaxChargeTime)
    
    local iHeadBone = pLocal:LookupBone("ValveBiped.Bip01_Head1")
    if not iHeadBone then return end
    
    local vecPos = pLocal:GetBonePosition(iHeadBone) + pLocal:GetRight()*20 + Vector(0, 0, 10)

    local angEye = pLocal:EyeAngles()
    local ang = Angle(0, angEye.y - 90, 90 - angEye.x)

    local iRatio = iTimePassed/iMaxChargeTime
    

    cam.Start3D2D(vecPos, ang, 0.01)

        draw.RoundedBox(50, 0, 0, 200, 1000, Color(0, 0, 0, 0.5*255))
        draw.RoundedBox(50, 0, 1000*(1-iRatio), 200, 1000*iRatio, cBlue)

    cam.End3D2D()

    if not IsValid(pLocal.ChakraJumpIndication) then
        
        pLocal.ChakraJumpIndication = ClientsideModel("models/hunter/blocks/cube025x025x025.mdl")
        pLocal.ChakraJumpIndication:SetNoDraw(true)

    end

    pLocal.ChakraJumpIndication:SetPos(pLocal:GetPos() + Vector(0, 0, 15))

    local angDirection = pLocal:EyeAngles()
    angDirection.x = math.min(angDirection.x, -iAngle)

    pLocal.ChakraJumpIndication:SetAngles(angDirection + Angle(180, 0, 0))

    if not IsValid(pLocal.ChakraJumpIndication.eParticle) then
        
        pLocal.ChakraJumpIndication.eParticle = CreateParticleSystem(pLocal.ChakraJumpIndication, "hpw_white_main", PATTACH_ABSORIGIN_FOLLOW, nil, Vector(0, 0, 0))
        ParticleEffect("ctg_chakrajump", pLocal.ChakraJumpIndication:GetPos() + Vector(0, 0, 20), Angle(0, 0, 0), pLocal.ChakraJumpIndication)
    end
end)

local function DrawOutlinedCircle(x, y, radius, thickness, segs)
    for i = 0, segs do
        local theta1 = math.rad((i / segs) * 360)
        local theta2 = math.rad(((i + 1) / segs) * 360)

        local x1, y1 = x + math.cos(theta1) * radius, y + math.sin(theta1) * radius
        local x2, y2 = x + math.cos(theta2) * radius, y + math.sin(theta2) * radius
        local x3, y3 = x + math.cos(theta2) * (radius - thickness), y + math.sin(theta2) * (radius - thickness)
        local x4, y4 = x + math.cos(theta1) * (radius - thickness), y + math.sin(theta1) * (radius - thickness)

        surface.DrawPoly({
            { x = x1, y = y1 },
            { x = x2, y = y2 },
            { x = x3, y = y3 },
            { x = x4, y = y4 }
        })
    end
end

local function DrawGradientCircle(x, y, radius, thickness, segs)
    local steps = 10 
    local alphaStep = 100 / steps 

    for i = 0, steps do
        local currentRadius = radius - (i * (thickness / steps))
        local currentAlpha = math.Clamp(100 - (alphaStep * i), 0, 255)

        surface.SetDrawColor(255, 255, 255, currentAlpha)
        draw.NoTexture()
        DrawOutlinedCircle(x, y, currentRadius, 4, segs)
    end
end

local iLerp = 0 

hook.Remove("PostDrawTranslucentRenderables", "LVM:DrawPlayerCircle")
hook.Add("PostDrawTranslucentRenderables", "LVM:DrawPlayerCircle", function()
    local pLocal = LocalPlayer()
    if not IsValid(pLocal) or not pLocal:Alive() then return end
    if pLocal:AdminMode() and not pLocal:IsAnimateur() then return end
    if pLocal:GetNetworkVar("Invisibility", false  ) then return end

    local bNarutoRun = pLocal:GetNetworkVar("bNarutoRun", false)
    local bOnGround = pLocal:IsOnGround()

    iLerp = Lerp(FrameTime() * 5, iLerp, (not bNarutoRun and bOnGround) and 1 or 0)

    if iLerp <= 0 then return end

    local vecPos = pLocal:GetPos() + Vector(0, 0, 1)
    local ang = Angle(0, 0, 0)

    local iRadius = 130 * iLerp 

    cam.Start3D2D(vecPos, ang, 0.1)
        draw.NoTexture()


        surface.SetDrawColor(255, 255, 255, 79)
        DrawOutlinedCircle(0, 0, iRadius, 10, 50)

        
        surface.SetDrawColor(255, 255, 255)
        DrawOutlinedCircle(0, 0, iRadius, 3, 50)

        DrawGradientCircle(0, 0, iRadius, 15, 50)

    cam.End3D2D()
end)


local angRotate = Angle(0, 180, 0)

local function CarryPlayer(bool, porteur, porter)
    if bool then
        return
    else
        net.Start("LVM:StopBeCarrier")
        net.SendToServer()
    end
end


hook.Add( "ShouldCollide", "LVM:Carry", function( ent1, ent2 )
	if ( ent1:IsCarry() and ent2:IsPlayer() ) then return false end
end )

local function Carried(bool, porteur, porter)
    if bool then
        local sequenceCarried = porter:LookupSequence("piggyback_2")
        local bone = porteur:LookupBone("ValveBiped.Bip01_Spine2")
        if not bone then return end
        
        local bonePos = porteur:GetBonePosition(bone) - Vector(0, 0, 30)
        local eyeForward = porteur:GetAngles():Forward()
        local ang = porteur:EyeAngles()
        ang.x = 0;
        
        porter:SetPos(bonePos - ang:Forward() * 10)
        porter:SetAngles(porteur:GetAngles())
        porter:SetEyeAngles(porteur:GetAngles())
        porter:SetupBones()
        porter:SetParent(porteur)
        porter:SetSequence(sequenceCarried)
        porter:FrameAdvance(FrameTime())
        porter:SetCustomCollisionCheck(true)
        porter:SetCollisionGroup(COLLISION_GROUP_WEAPON)
        
    else
        porter:SetParent(nil)
        porter:SetCollisionGroup(COLLISION_GROUP_PLAYER)
        net.Start("LVM:StopBeCarry")
        net.SendToServer()
    end
end

hook.Add("KeyPress", "LVM:StopCarry", function(ply, key)
	if ( key == IN_USE ) and ply:IsCarry() then
        Carried(false, nil, ply)
	end

end)

hook.Add("PostPlayerDraw", "LVM:Carry", function(ply)
    
    local sequenceCarrier = ply:LookupSequence("piggyback_1")
    
    if ply:IsCarry() then
        local porteur = ply:CarryBy()
        local porter = ply;
        if not IsValid(porteur) then
            Carried(false, porteur, porter)
        else
            Carried(true, porteur, porter)
        end
    end
    
    if ply:IsCarrying() then
        local porteur = ply;
        local porter = ply:CarriedPlayer()
        
        if not IsValid(porter) then
            CarryPlayer(false, porteur, porter)
        else
            CarryPlayer(true, porteur, porter)
            
            if porteur == LocalPlayer() then
                LocalPlayer().CameraLVM = 3
            end
            
            porteur:SetSequence(sequenceCarrier)
            porteur:FrameAdvance(FrameTime())
        end
    end

end)

function LVM_V2:WrapText(sText, sFont, w)

    local tText = string.Explode(" ", sText)
    local sFinal = ""
    local sLine = ""

    for i, sWord in ipairs(tText) do

        local sTemp = (sLine == "" and "" or sLine.." ")..sWord
        if surface.GetTextSize(sTemp, sFont) > w then

            sFinal = sFinal..sLine.."\n"
            sLine = sWord

        else

            sLine = sTemp

        end

    end

    sFinal = sFinal..sLine

    return sFinal, #string.Explode("\n", sFinal)

end

local matBlur = Material("pp/blurscreen")
function LVM_V2:DrawBlur(vPanel, iAmount, iDensity)

    local scrw = ScrW()
    local scrh = ScrH()

    local x, y = vPanel:LocalToScreen(0, 0)

    surface.SetDrawColor(color_white)
    surface.SetMaterial(matBlur)

    iDensity = iDensity or 3

    for i = 1, iDensity do

        matBlur:SetFloat("$blur", (i/iDensity)*(iAmount or 3))
        matBlur:Recompute()

        render.UpdateScreenEffectTexture()
        surface.DrawTexturedRect(x*-1, y*-1, scrw, scrh)

    end

end

function LVM_V2:DrawBlurNoPanel(iAmount, iDensity)

    local scrw = ScrW()
    local scrh = ScrH()

    local x, y = 0, 0

    surface.SetDrawColor(color_white)
    surface.SetMaterial(matBlur)

    iDensity = iDensity or 3

    for i = 1, iDensity do

        matBlur:SetFloat("$blur", (i/iDensity)*(iAmount or 3))
        matBlur:Recompute()

        render.UpdateScreenEffectTexture()
        surface.DrawTexturedRect(x*-1, y*-1, scrw, scrh)

    end

end


function LVM_V2:Popup(sTitle, sSubTitle, fnSuccess, fnCancel)
    if IsValid(self.vPopup) then self.vPopup:Remove() end

    self.vPopup = vgui.Create("EditablePanel")
    self.vPopup:SetSize(SW(1920), SH(1080))
    self.vPopup:MakePopup()
    self.vPopup:SetAlpha(0)
    self.vPopup.Close = function(self)
        
        self:AlphaTo(0, 0.3, 0, function(_, self)

            self:Remove()

        end)

    end

    local vAccept
    local height = 0;

    self.vPopup.Paint = function(self, w, h)
        
        LVM_V2:DrawBlur(self, 5, 3)

        surface.SetDrawColor(Color(0, 0, 0, 0.97*255))
        surface.DrawRect(0, 0, w, h)
        
        local sWrappedSubTitle, iSubTitleH = LVM_V2:WrapText(sSubTitle, LVM_V2.Fonts("LVM", 10, 500), SW(300))
        height = SH(300) + (iSubTitleH*SH(10))
        
        draw.RoundedBox(0, w*0.325, h/2 - height/2, w*0.35, height, Color(33, 34, 38, 200))

        draw.RoundedBox(0, w*0.325, h/2 - height/2, w*0.35, SH(40), Color(53, 54, 64, 200))
        draw.SimpleText(sTitle, LVM_V2.Fonts("LVM", 12, 500), w*0.33, h/2 - height/2, color_white, TEXT_ALIGN_LEFT)
        
        draw.DrawText(sWrappedSubTitle, LVM_V2.Fonts("LVM", 10, 500), w/2, h*0.43, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)

        -- if input.IsKeyDown(KEY_ENTER) then

        --     vAccept:DoClick()

        -- end

    end

    vAccept = vgui.Create("DButton", self.vPopup)
    vAccept:SetSize(SW(130), SH(40))
    vAccept:SetPos(SW(700), ((SH(1080)/2) - height/2) + SH(50))
    vAccept:SetText("")
    vAccept.Paint = function(self, w, h)
        
        self.iLerp = Lerp(FrameTime()*5, self.iLerp or 0, (self:IsHovered() and not self:IsDown()) and 1 or 0)
        
        draw.RoundedBox(0, 0, 0, w, h, ColorAlpha(Color(105, 209, 93), 255 - 200*self.iLerp))

        draw.SimpleText("Oui", LVM_V2.Fonts("LVM", 8, 500), w/2, h/2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)

    end
    vAccept.DoClick = function()
        
        if isfunction(fnSuccess) then fnSuccess() end
        self.vPopup:Close()

    end

    local vCancel = vgui.Create("DButton", self.vPopup)
    vCancel:SetSize(SW(130), SH(40))
    vCancel:SetPos(SW(1100), ((SH(1080)/2) - height/2) + SH(50))
    vCancel:SetText("")
    vCancel.Paint = function(self, w, h)

        self.iLerp = Lerp(FrameTime()*5, self.iLerp or 0, (self:IsHovered() and not self:IsDown()) and 1 or 0)
        
        draw.RoundedBox(0, 0, 0, w, h, ColorAlpha( Color(254, 54, 54), 255 - 200*self.iLerp))

        draw.SimpleText("Non", LVM_V2.Fonts("LVM", 8, 500), w/2, h/2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)

    end
    vCancel.DoClick = function()
        
        if isfunction(fnCancel) then fnCancel() end
        self.vPopup:Close()

    end

    self.vPopup:AlphaTo(255, 0.3, 0)

end

function LVM_V2:PopupAmount(sTitle, sSubTitle, bNumeric, fnSuccess, fnCancel)
    if IsValid(self.vPopup) then self.vPopup:Remove() end

    bNumeric = bNumeric or false 

    self.vPopup = vgui.Create("EditablePanel")
    self.vPopup:SetSize(SW(1920), SH(1080))
    self.vPopup:MakePopup()
    self.vPopup:SetAlpha(0)
    self.vPopup.Close = function(self)
        self:AlphaTo(0, 0.3, 0, function(_, self)
            self:Remove()
        end)
    end

    local vAccept
    local height = 0
    local amountInput

    self.vPopup.Paint = function(self, w, h)
        LVM_V2:DrawBlur(self, 5, 3)

        surface.SetDrawColor(Color(0, 0, 0, 0.97*255))
        surface.DrawRect(0, 0, w, h)

        local sWrappedSubTitle, iSubTitleH = LVM_V2:WrapText(sSubTitle, LVM_V2.Fonts("LVM", 10, 500), SW(300))
        height = SH(300) + (iSubTitleH*SH(10))

        draw.RoundedBox(0, w*0.325, h/2 - height/2, w*0.35, height, Color(33, 34, 38, 200))
        draw.RoundedBox(0, w*0.325, h/2 - height/2, w*0.35, SH(40), Color(53, 54, 64, 200))
        draw.SimpleText(sTitle, LVM_V2.Fonts("LVM", 12, 500), w*0.33, h/2 - height/2, color_white, TEXT_ALIGN_LEFT)

        draw.DrawText(sWrappedSubTitle, LVM_V2.Fonts("LVM", 10, 500), w/2, h*0.43, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end


    local sText = (bNumeric and 1 or "Entrez un texte")

    amountInput = vgui.Create("DTextEntry", self.vPopup)
    amountInput:SetSize(SW(550), SH(75))
    amountInput:SetPos(SW(700), ((SH(1080)/2) - height/2) - SH(25))
    amountInput:SetText(sText)
    amountInput:SetNumeric(iNumeric)
    amountInput.Paint = function(self, w, h)

        draw.RoundedBox(0, 0, 0, w, h, Color(53, 54, 64, 200))
        draw.SimpleText(self:GetText(), LVM_V2.Fonts("LVM", 10, 500), 5, h/2, color_white, TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
        draw.RoundedBox(0, 0, h-1, w, 1, Color(105, 209, 93))
    end



    vAccept = vgui.Create("DButton", self.vPopup)
    vAccept:SetSize(SW(130), SH(40))
    vAccept:SetPos(SW(700), ((SH(1080)/2) - height/2) + SH(50) + SH(40))
    vAccept:SetText("")
    vAccept.Paint = function(self, w, h)
        self.iLerp = Lerp(FrameTime()*5, self.iLerp or 0, (self:IsHovered() and not self:IsDown()) and 1 or 0)

        draw.RoundedBox(0, 0, 0, w, h, ColorAlpha(Color(105, 209, 93), 255 - 200*self.iLerp))
        draw.SimpleText("Oui", LVM_V2.Fonts("LVM", 8, 500), w/2, h/2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
    vAccept.DoClick = function()
        local enteredAmount = (bNumeric and tonumber(amountInput:GetText()) or amountInput:GetText())
        if enteredAmount and isfunction(fnSuccess) then 
            fnSuccess(enteredAmount) 
        end
        self.vPopup:Close()
    end

    local vCancel = vgui.Create("DButton", self.vPopup)
    vCancel:SetSize(SW(130), SH(40))
    vCancel:SetPos(SW(1100), ((SH(1080)/2) - height/2) + SH(50) + SH(40))
    vCancel:SetText("")
    vCancel.Paint = function(self, w, h)
        self.iLerp = Lerp(FrameTime()*5, self.iLerp or 0, (self:IsHovered() and not self:IsDown()) and 1 or 0)

        draw.RoundedBox(0, 0, 0, w, h, ColorAlpha(Color(254, 54, 54), 255 - 200*self.iLerp))
        draw.SimpleText("Non", LVM_V2.Fonts("LVM", 8, 500), w/2, h/2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
    vCancel.DoClick = function()
        if isfunction(fnCancel) then fnCancel() end
        self.vPopup:Close()
    end

    self.vPopup:AlphaTo(255, 0.3, 0)
end


net.Receive("LVM:AskCarried", function()

    print("chef")

    local sender = net.ReadPlayer()

    LVM_V2:Popup("Porter une personne", "Voulez vous porter cette personne ?", function()

        net.Start("LVM:AcceptCarried")
            net.WriteBool(true)
            net.WritePlayer(sender)
        net.SendToServer()

    end, function()
        
        net.Start("LVM:AcceptCarried")
            net.WriteBool(false)
            net.WritePlayer(sender)
        net.SendToServer()
        
    end)

end)


-- P.animations.Add("brasdos", "act_bras_dos_idle", true, "Bras dans le dos", true)
-- P.animations.Add("brascroise", "act_bras_croise_idle", true, "Bras croisé", true)
-- P.animations.Add("poingciel", "nrp_base_bigsword_idle_loop", false, "Poing vers le ciel", false)
-- P.animations.Add("genousol", "nrp_beaten_bellydown_large_loop", true, "Genou au sol", false)
-- P.animations.Add("etirementdepaule", "nrp_lobby_ay_etc_win_type01_start", false, "Etirement d'épaule", true)
-- P.animations.Add("approve", "nrp_lobby_boruto_etc_team_fin", false, "J'approuve", false)
-- P.animations.Add("sitting", "nrp_lobby_etc_emotion_sitting_pattern_03_loop", true, "Assis #1", false)
-- P.animations.Add("sitting2", "nrp_lobby_etc_emotion_itachi_sitting_loop", true, "Assis #2", false)
-- P.animations.Add("tendreqlq", "nrp_lobby_etc_emotion_combination_type03_a_loop", false, "Tendre quelque chose", false)
-- P.animations.Add("pleurer", "nrp_lobby_etc_emotion_cry_type02_loop", true, "Pleurer", true)
-- P.animations.Add("actpresence", "nrp_lobby_etc_emotion_d32_loop", true, "Acte de présence", false)
-- P.animations.Add("dance1", "nrp_lobby_etc_emotion_dance_type01_loop", true, "Dance #1", false)
-- P.animations.Add("doscontreterre", "nrp_lobby_etc_emotion_dying_type03_loop", true, "Allongé dos contre terre", false)
-- P.animations.Add("recolterqlqc", "nrp_lobby_etc_emotion_festivals_type02_loop", true, "Récolter quelque chose", false)
-- P.animations.Add("applaudir", "nrp_lobby_etc_emotion_festivals_type04_loop", true, "Applaudir étrangement", false)
-- P.animations.Add("win", "nrp_lobby_etc_emotion_jiraiya_etc_win_type01_loop", true, "J'ai gagné !", false)
-- P.animations.Add("gorila", "nrp_lobby_etc_emotion_gorilla", true, "Gorille", false)
-- P.animations.Add("beautiful", "nrp_lobby_etc_emotion_d34_start", false, "Je suis trop beau", false)


local cacheFavourite = {}
local loaded = false;
local function getFavouriteAct()
    if loaded then
        return cacheFavourite;
    end
    
    local result = {}
    
    if file.Exists("LVM_animations_favourite.json", "DATA") then
        local jsonData = file.Read("LVM_animations_favourite.json", "DATA")
        result = util.JSONToTable(jsonData)
    else
        file.Write("LVM_animations_favourite.json", util.TableToJSON(result))
    end
    
    cacheFavourite = result;
    loaded = true;
    
    return result;
end

local function addFavouriteAct(act)
    local favouriteAct = getFavouriteAct()
    
    table.insert(favouriteAct, act)
    
    file.Write("LVM_animations_favourite.json", util.TableToJSON(favouriteAct))
    
    cacheFavourite = favouriteAct;
end

local function removeFavouriteAct(act)
    local favouriteAct = getFavouriteAct()
    
    for k,v in pairs(favouriteAct) do
        if v == act then
            table.remove(favouriteAct, k)
            break
        end
    end
    
    file.Write("LVM_animations_favourite.json", util.TableToJSON(favouriteAct))
    
    cacheFavourite = favouriteAct;
end

local MOVE_ANIM = {}

local COOOL = 0

hook.Add("Think", "LVM:AnimationSync", function()
    if COOOL > CurTime() then return end
    
    for k,v in pairs(MOVE_ANIM) do
        if not IsValid(k) then
            MOVE_ANIM[k] = nil
        end
        
        local sequnence = k:LookupSequence(v)
        
        if k.animation_playing then
            if k:GetSequence() != sequnence then
                -- print("ici")
                k:AddVCDSequenceToGestureSlot(GESTURE_SLOT_CUSTOM, sequnence, 0, false)
            end
        else
            MOVE_ANIM[k] = nil;
        end
    end
    
    COOOL = CurTime() + 0.3
end)

local function getanim(act)
    if act == nil then return end
    if act == "" then return end

    local anim = LVM_V2.Animations

    for k, v in pairs(anim) do
        if v.Act == act then
            return v
        end
    end

    return nil
end

net.Receive("LVM:startAnimation", function()
    local anim = net.ReadString()
    local ply = net.ReadPlayer()

    ply.animationID = anim
    ply.animation_playing = true
    
    local animation = getanim(anim)
    if animation == nil then return end
    
    local sequence = ply:LookupSequence(animation.Act)
    
    if MOVE_ANIM[ply] != nil then
        MOVE_ANIM[ply] = nil;
    end
    
    ply:AddVCDSequenceToGestureSlot(GESTURE_SLOT_CUSTOM, sequence, 0, not animation.Loop)
    local duration = ply:SequenceDuration(sequence)
    
    if animation.CanWalk and animation.Loop then
        MOVE_ANIM[ply] = animation.Act
    end
    
    if not animation.Loop then
        timer.Simple(duration, function()
            if IsValid(ply) then
                ply:AnimResetGestureSlot(GESTURE_SLOT_CUSTOM)
                ply.animation_playing = false
            end
        end)
    end
end)

net.Receive("LVM:stopAnimation", function()
    local ply = net.ReadPlayer()

    ply.animation_playing = false
    if not IsValid(ply) then return end
    ply:AnimResetGestureSlot(GESTURE_SLOT_CUSTOM)
    
    if MOVE_ANIM[ply] == nil then
        MOVE_ANIM[ply] = nil;
    end
end)

local frame;
function LVM_V2:AnimationMenu()
    local animations = LVM_V2.Animations
    if not animations then return end
    if IsValid(frame) then
        frame:Remove()
        return
    end

    frame = vgui.Create("DFrame")
    frame:SetSize(SW(500), SH(600))
    frame:Center()
    frame:SetTitle("")
    frame:MakePopup()
    frame:SetDraggable(false)
    frame:ShowCloseButton(false)
    frame.Paint = function(self, w, h)
        -- Fond
        surface.SetDrawColor(30, 30, 30, 255)
        surface.DrawRect(0, 0, w, h)

        -- En-tête
        surface.SetDrawColor(60, 60, 60, 255)
        surface.DrawRect(0, 0, w, SH(50))

        draw.SimpleText("LVM - Menu d'animations", LVM_V2.Fonts("LVM", 8, 500), SW(20), SH(12), Color(255, 255, 255, 255), TEXT_ALIGN_LEFT)
        
        draw.RoundedBox(6, SW(20), SH(60), SW(460), SH(30), Color(50, 50, 50))
    end
    
    local search;
    
    local close = vgui.Create("DButton", frame)
    close:SetSize(SW(35), SH(35))
    close:SetPos(SW(450), SH(7))
    close:SetText("")
    close.Paint = function(self, w, h)
        draw.SimpleText("✕", LVM_V2.Fonts("LVM", 10, 500), w / 2, h / 2,
            (self:IsHovered() and Color(255, 0, 0) or color_white), 1, 1)
    end
    close.DoClick = function()
        if IsValid(frame) then
            frame:AlphaTo(0, 0.1, 0, function()
                if IsValid(frame) then
                    frame:Remove()
                end
            end)
        end
    end
    
    -- Barre de recherche stylisée
    local searchBox = vgui.Create("DTextEntry", frame)
    searchBox:SetPos(SW(30), SH(60))
    searchBox:SetSize(SW(450), SH(30))
    searchBox:SetFont(LVM_V2.Fonts("LVM", 7, 500))
    searchBox:SetUpdateOnType(true)
    searchBox:SetPlaceholderText("Rechercher une animation...")
    searchBox:SetValue("")
    searchBox:SetPlaceholderColor(Color(255, 255, 255, 100))
    searchBox:SetTextColor(Color(255, 255, 255))
    searchBox:SetDrawLanguageID(false)
    searchBox:SetPaintBackground(false)

    searchBox.OnValueChange = function(self, text)
        search(text)
    end

    -- Liste stylée
    local animList = vgui.Create("DScrollPanel", frame)
    animList:SetPos(SW(20), SH(100))
    animList:SetSize(SW(460), SH(480))
    animList.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(40, 40, 40))
    end
    animList:GetVBar():SetWide(SW(2))
    
    function search(text)
        animList:Clear()
        local favouriteAct = getFavouriteAct()
        table.sort(animations, function(a, b)
            return a.Name:lower() > b.Name:lower()
        end)
        table.sort(animations, function(a, b)
            return table.HasValue(favouriteAct, a.Act) and not table.HasValue(favouriteAct, b.Act)
        end)
        
        for k,v in pairs(animations) do
            if string.find(string.lower(v.Name), string.lower(text)) then
                local button = vgui.Create("DButton", animList)
                button:Dock(TOP)
                button:SetTall(SH(35))
                button:SetText("")
                button:DockMargin(0, 0, 0, SH(5))
                button.Paint = function(self, w, h)
                    draw.RoundedBox(4, 0, 0, w, h, Color(50, 50, 50, (self:IsHovered() and 200 or 255)))
                    draw.SimpleText(v.Name, LVM_V2.Fonts("LVM", 8, 500), SW(7), SH(5), color_white, TEXT_ALIGN_LEFT)
                    
                    if table.HasValue(favouriteAct, v.Act) then
                        draw.SimpleText("★", LVM_V2.Fonts("LVM", 8, 500), SW(440), SH(5), Color(244, 255, 126), TEXT_ALIGN_RIGHT)
                    else
                        draw.SimpleText("☆", LVM_V2.Fonts("LVM", 8, 500), SW(440), SH(5), Color(200, 200, 200, 200), TEXT_ALIGN_RIGHT)
                    end
                end
                button.DoClick = function()
                    net.Start("LVM:requestAnimation")
                        net.WriteString(v.Act)
                    net.SendToServer()
                    close.DoClick()
                end
                button.DoRightClick = function()
                    local menu = DermaMenu()
                    if not LocalPlayer().animation_playing then
                        menu:AddOption("Jouer l'animation", function()
                            net.Start("LVM:requestAnimation")
                                net.WriteString(v.Act)
                            net.SendToServer()
                            close.DoClick()
                        end)
                    end
                    menu:AddOption("Ajouter aux favoris", function()
                        addFavouriteAct(v.Act)
                        notification.AddLegacy("L'animation a été ajoutée à vos favoris.", 1, 5)
                    end)
                    menu:AddOption("Supprimer des favoris", function()
                        removeFavouriteAct(v.Act)
                        notification.AddLegacy("L'animation a été supprimée de vos favoris.", 1, 5)
                    end)
                    menu:Open()
                end
            end
        end
    end
    
    search("")
end

function LVM_V2:IsInAnimationMenu()
    return IsValid(frame)
end