LVM_V2.Pages["Library"]["Classe"] = {}

local Classes = {
    [1] = { 
        nom = "Médecin",   
        image = "class/medecin.png",
        description = "Maître du chakra curatif, le Médecin excelle dans les soins et le soutien de ses alliés sur le champ de bataille. Il peut soigner les blessures, purifier les altérations et maintenir l’équipe en vie lors des combats les plus intenses."
    },
    [2] = { 
        nom = "Éclaireur", 
        image = "class/eclaireur.png",
        description = "Spécialiste de la vitesse et de la furtivité, l’Éclaireur utilise son agilité pour repérer les ennemis, esquiver les attaques et surprendre ses adversaires. Parfait pour explorer, tendre des embuscades et se déplacer rapidement sur le terrain."
    },
    [3] = { 
        nom = "Combattant",
        image = "class/combatant.png",
        description = "Guerrier polyvalent, le Combattant manie aussi bien les arts martiaux que les armes blanches. Son style de combat repose sur la force brute et l’efficacité au corps-à-corps, capable d’infliger de lourds dégâts à ses ennemis."
    },
    [4] = { 
        nom = "Rempart",   
        image = "class/rempart.png",
        description = "Pilier défensif de l’équipe, le Rempart encaisse les coups et protège ses alliés grâce à une résistance exceptionnelle. Il peut détourner l’attention, absorber les attaques et retourner la situation à l’avantage de son groupe."
    },
}


LVM_V2.Pages["Library"]["Classe"].Paint = function(self, w, h)
    if not LocalPlayer():IfMinimumRank("genin2") then
        draw.SimpleText(
            "Vous devez être au minimum Genin Confirmée pour accéder à cette page",
            LVM_V2.Fonts("LVM", 12, 900),
            w/2, h/2 - SH(40),
            Color(255, 90, 90),
            TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER
        )
        return
    end



    local class_id = LocalPlayer():GetClasse()
    local class_label = Classes[class_id] and Classes[class_id].nom or "Aucune"

    draw.SimpleText(
        "Votre Classe : " .. class_label,
        LVM_V2.Fonts("LVM", 18, 800),
        w/2, SH(50),
        Color(255,255,255),
        TEXT_ALIGN_CENTER, TEXT_ALIGN_TOP
    )
    if LocalPlayer():GetClasse() ~= 0 then


        return 
    end

    local cardW, cardH = SW(300), SH(460)
    local spaceX = SW(70)
    local startX = SW(75)
    local y = SH(170)

    for i = 1, 4 do
        local class = Classes[i]
        local x = startX + (i-1)*(cardW + spaceX)

        draw.RoundedBox(18, x, y, cardW, cardH, Color(32,34,40, 230))
        draw.RoundedBox(18, x+2, y+2, cardW-4, cardH-4, Color(44,48,55, 230))

        draw.SimpleText(
            class.nom,
            LVM_V2.Fonts("LVM", 13, 900),
            x + cardW/2, y + SH(36),
            Color(205,225,255),
            TEXT_ALIGN_CENTER
        )

        surface.SetMaterial(LVM_V2:Material(class.image))
        surface.SetDrawColor(255,255,255,230)
        surface.DrawTexturedRect(x + (cardW - SW(145))/2, y + SH(70), SW(165), SW(165))
    end
end

local convertclass = {[1] = "Médicin", [2] = "Éclaireur", [3] = "Combattant", [4] = "Rempart"}


LVM_V2.Pages["Library"]["Classe"].After = function(panel)
    if not LocalPlayer():IfMinimumRank("genin2") then return end
    local classe = LocalPlayer():GetClasse()
    if LocalPlayer():GetClasse() ~= 0 then 
        local convertclass = convertclass[classe] or "Aucune"
        local tree_data = {[convertclass] = {}}

        local GetIcon = LVM_V2.Pages.GetIcon
        local DrawCircle = LVM_V2.Pages.DrawCircle
        local UpgradeSkill = LVM_V2.Pages.UpgradeSkill
        local UnlockSkill = LVM_V2.Pages.UnlockSkill
            
        for i = 1, #DLVM.Technics do
            if tree_data[DLVM.Technics[i].Nature] != nil then
                local tech = DLVM.Technics[i]
                
                tree_data[tech.Nature][#tree_data[tech.Nature] + 1] = {
                    Id = i,
                    Name = tech.Name,
                    Description = tech.Description,
                    Icon = tech.Icon,
                    PDC = tech.PDC,
                    Rang = tech.Rang
                }
            end
        end

        local cardW, cardH = SW(300), SH(460)
        local spaceX = SW(70)
        local startX = SW(275)
        local y = SH(170)

        local jutsu_star = LocalPlayer().LVM_Jutsu or {}

        for nature, techs in pairs(tree_data) do
            if #techs == 0 then continue end
            local i = 1
            for k, technic in pairs(techs) do

                local is_posses = LocalPlayer():HasJutsu(technic.Id)
                local panel = vgui.Create("DPanel", panel)
                panel:SetSize(cardW, cardH)
                panel:SetPos(startX + (i-1)*(cardW + spaceX), y)
                panel.Icon = GetIcon(technic)
                panel.Paint = function(self, w, h)
                    draw.RoundedBox(18, 0, 0, w, h, Color(32,34,40, 230))
                    draw.RoundedBox(18, 2, 2, w-4, h-4, Color(44,48,55, 230))

                    draw.SimpleText(
                        technic.Name,
                        LVM_V2.Fonts ("LVM", 13, 900),
                        w/2, SH(25),
                        Color(205,225,255),
                        TEXT_ALIGN_CENTER
                    )

                    draw.SimpleText(
                        "Rang : " .. technic.Rang,
                        LVM_V2.Fonts("LVM", 11, 800),
                        w/2, SH(55),
                        Color(255,255,255),
                        TEXT_ALIGN_CENTER
                    )

                    is_posses = LocalPlayer():HasJutsu(technic.Id)
            
                    local iconw, iconh = SW(128), SW(128)

                    DrawCircle(w/2, SH(100) + iconh/2, iconh/2, 360, (is_posses and color_white or Color(100, 100, 100)))

                    local icon = self.Icon
            
                    masks.Start()
                        if icon != nil then
                            surface.SetMaterial(util.ImgurToMaterial(icon, "smooth noclamp"))
                        else
                            surface.SetMaterial(LVM_V2:Material("learning/no_icon.png"))
                        end
                        surface.SetDrawColor((is_posses and Color(255,255,255) or Color(100,100,100)))
                        surface.DrawTexturedRect(w/2 - iconw/2, SH(100), iconw, iconh)
                    masks.Source()
                        DrawCircle(w/2, SH(100) + iconh/2, iconh*0.45, 360, color_white)
                    masks.End()
                    jutsu_star = LocalPlayer().LVM_Jutsu
                    if is_posses then 
                        if jutsu_star != nil then
                            if jutsu_star[technic.Id] != nil then
                                for i = 1, 5 do
                                    surface.SetMaterial(LVM_V2:Material("learning/star.png"))
                                    surface.SetDrawColor(jutsu_star[technic.Id] >= i and color_white or Color(100, 100, 100, 100))
                                    surface.DrawTexturedRect(SW(100) + ((i-1) * SW(25)), SH(240), SW(13), SH(13))
                                end 
                            end
                            
                            if not technic.Upgrades then 
                                technic.Upgrades = { 1, 2, 2, 3, 3 }
                            end
                            if jutsu_star[technic.Id] != nil then
                                if tonumber(jutsu_star[technic.Id])+1 <= 5 then
                                    draw.SimpleText(technic.Upgrades[tonumber(jutsu_star[technic.Id])+1].." PDC", LVM_V2.Fonts("LVM", 6, 500), 
                                    SW(170), SH(260), Color(35, 162, 42, 200), TEXT_ALIGN_RIGHT)
                                else
                                    draw.SimpleText("Technique niveau max", LVM_V2.Fonts("LVM", 6, 500), 
                                    SW(220), SH(260), Color(35, 162, 42, 200), TEXT_ALIGN_RIGHT)
                                end
                            end
                        end 
                    end
                end

                
                local richtext;

                local techid = technic.Id

                local function UpdateRichText()
                    local jutsulevel = (LocalPlayer().LVM_Jutsu == nil and 0 or (LocalPlayer().LVM_Jutsu[techid] or 0))
                    richtext = vgui.Create("RichText", panel)
                    richtext:SetSize(cardW - SW(20), SH(65))
                    richtext:SetPos(SW(10), SH(280))
                    richtext:SetVerticalScrollbarEnabled(true)
                    function richtext:PerformLayout()
                        self:SetFontInternal(LVM_V2.Fonts("LVM", 5, 500))
                    end

                    richtext:InsertColorChange(255, 255, 255, 255)
                    richtext:InsertColorChange(255, 255, 255, 255)
                    richtext:AppendText(technic.Description.."\n")

                    jutsu_star = LocalPlayer().LVM_Jutsu or {}

                    richtext:InsertColorChange(255, 255, 0, 255)
                    richtext:AppendText("Niveau : " .. jutsulevel .. "\n")
                    LVM_V2:GetConfigJutsu(technic.Id, function(config)
                        for k, v in pairs(config) do
                            local color = LVM_V2.Equilibrage.Config["Color"][k] or Color(255, 255, 255)
                            local key = LVM_V2.Equilibrage.Config["Key"][k] or k
                            
                            local actual_value = v[jutsu_star[technic.Id]]
                            local next_value = v[math.Clamp(jutsu_star[technic.Id]+1, 1, 5)]
                            
                            richtext:InsertColorChange(color.r, color.g, color.b, 255)
                            richtext:AppendText(key.." : "..actual_value .." → "..next_value)
                            
                            richtext:InsertColorChange(255, 255, 255, 255)
                            richtext:AppendText("\n")
                        end
                    end)
                end

                UpdateRichText()

                local btn = vgui.Create("DButton", panel)
                btn:SetSize(SW(250), SH(50))
                btn:SetPos(SW(25), SH(380))
                btn:SetText("")
                    
                btn.Paint = function(self, w, h)
                    local mainColor = self:IsHovered() and Color(60,160,255,245) or Color(34,130,220, 210) 
                    local text = is_posses and "Améliorer" or "Débloquer"

                    draw.RoundedBox(14, 0, 0, w, h, mainColor)
                    draw.SimpleText(text,
                        LVM_V2.Fonts("LVM", 11, 800),
                        w/2, h/2,
                        color_white,
                        TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER
                    )
                end

                btn.DoClick = function(self)
                    if LocalPlayer():GetClasse() == 0 then return end
                    if jutsu_star[technic.Id] == 5 then 
                        notification.AddLegacy("Vous avez déjà débloqué le niveau maximum de ce jutsu.", NOTIFY_ERROR, 5)
                        return
                    end
                    LVM_V2:Popup(
                        ""..(is_posses and "Améliorer" or "Débloquer").." le jutsu",
                        "Êtes-vous sûr de vouloir "..(is_posses and "améliorer" or "débloquer").." ce jutsu ?",
                        function()
                            if is_posses then
                                net.Start("LVM_V2:UpgradeJutsu")
                                    net.WriteUInt(techid, 8)
                                net.SendToServer()
                            else
                                net.Start("LVM_V2:BuyJutsu")
                                    net.WriteUInt(techid, 8)
                                net.SendToServer()
                            end

                            if IsValid(richtext) then
                                richtext:Remove()
                            end

                            timer.Simple(0.1, function()
                                if not IsValid(panel) then return end
                                UpdateRichText()
                            end)
                        end,
                        function() end
                    )
                end

                i = i + 1
            end
        end
    

        return 
    end


    for i = 1, 4 do
        local x = SW(75) + (i-1)*(SW(300) + SW(70))

        local richtext = vgui.Create("RichText", panel)
        richtext:SetSize(SW(270), SH(100))
        richtext:SetPos(x + (SW(300)-SW(270))/2, SH(420))
        richtext:SetVerticalScrollbarEnabled(true)
        function richtext:PerformLayout()
            self:SetFontInternal(LVM_V2.Fonts("LVM", 5, 500))
        end

        local desc = Classes[i].description or "Aucune description disponible"

        richtext:InsertColorChange(255, 255, 255, 255)
        richtext:InsertColorChange(255, 255, 255, 255)
        richtext:AppendText(desc.."\n")

        local btn = vgui.Create("DButton", panel)
        btn:SetSize(SW(250), SH(50))
        btn:SetPos(x + (SW(300)-SW(250))/2, SH(550))
        btn:SetText("")

        btn.classID = i

        btn.Paint = function(self, w, h)
            local selected = (classe ~= 0)
            local hovered = self:IsHovered() and not selected
            local mainColor = hovered and Color(60,160,255,245) or Color(34,130,220, selected and 85 or 210)
            local textcolor = selected and Color(170,170,170) or color_white

            draw.RoundedBox(14, 0, 0, w, h, mainColor)
            draw.SimpleText("Choisir cette spécialité",
                LVM_V2.Fonts("LVM", 11, 800),
                w/2, h/2,
                textcolor,
                TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER
            )
        end

        btn.DoClick = function(self)
            if LocalPlayer():GetClasse() ~= 0 then return end
            LVM_V2:Popup(
                "Choisir la classe",
                "Êtes-vous sûr de vouloir choisir cette classe ?",
                function()
                    net.Start("LVM:SelectClass")
                        net.WriteUInt(self.classID, 3)
                    net.SendToServer()
                    if IsValid(panel) then
                        panel:GetParent():Remove()
                    end
                end,
                function() end
            )
        end
    end
end


LVM_V2.Pages["F4"]["Classe"] = {};

LVM_V2.Pages["F4"]["Classe"].Paint = function(self, w, h)
    surface.SetMaterial(LVM_v2:Material("f4/jutsu.png"))
    surface.SetDrawColor(color_white)
    surface.DrawTexturedRect(SW(110), SH(50), SW(1383), SH(710))
    
    if self.jutsu_actual == nil then return end
    for i = 1, 6 do
        
        local x, y = SW(180) + SW(170 * (i-1)), SH(640)
        local jutsu_id = self.jutsu_actual[i]

        if DLVM.Technics[jutsu_id] != nil then
            masks.Start()
            if DLVM.Technics[jutsu_id].Icon != "" then
                surface.SetMaterial(util.ImgurToMaterial(DLVM.Technics[jutsu_id].Icon, "smooth mips"))
            else
                surface.SetMaterial(LVM_V2:Material("learning/no_icon.png"))
            end
            surface.SetDrawColor(color_white)
            surface.DrawTexturedRect(x - SW(1) * 1.2, y - SH(3), SW(80), SH(80))
            masks.Source()
            DrawCircle(x + SW(37) + SW(2) * 0.8, y + SH(37.5), SH(35), 360, nil)
            masks.End()
        end
    end
    
    if self.TechClicked != nil then
        
        local tech = DLVM.Technics[self.TechClicked]
        if tech == nil then return end
        
        if tech.Icon != "" then
            surface.SetMaterial(util.ImgurToMaterial(tech.Icon, "smooth mips"))
            surface.SetDrawColor(color_white)
            surface.DrawTexturedRect(SW(1255), SH(120), SW(128), SH(128))
        else
            surface.SetMaterial(LVM_V2:Material("learning/no_icon.png"))
            surface.SetDrawColor(color_white)
            surface.DrawTexturedRect(SW(1255), SH(120), SW(128), SH(128))
        end
        
        draw.SimpleText(tech.Name, LVM_V2.Fonts("LVM", 8, 500), SW(1180), SH(70), color_white, TEXT_ALIGN_LEFT)
        
        draw.SimpleText("Description", LVM_V2.Fonts("LVM", 5, 500), SW(1180), SH(320), Color(200, 200, 200, 30), TEXT_ALIGN_LEFT)
        
    end
    draw.RoundedBoxEx(6, SW(115), SH(57), SW(688), SH(34), Color(29, 29, 29), true, false, false, false)
    
    draw.SimpleText("Classe", LVM_V2.Fonts("LVM", 7, 500), SW(130), SH(63), color_white, TEXT_ALIGN_LEFT)
    
    draw.RoundedBoxEx(6, SW(802), SH(57), SW(344), SH(34), Color(32, 32, 32), false, true, false, false)
end


LVM_V2.Pages["F4"]["Classe"].After = function(panel)
    
    
    local convertclass = {[1] = "Médicin", [2] = "Éclaireur", [3] = "Combattant", [4] = "Rempart"}

     
    local jutsu_old;
    panel.jutsu_actual = {}
    timer.Simple(0.1, function()
        jutsu_old = {
            DLVM.Binds["Jutsu1"]:GetInt(),
            DLVM.Binds["Jutsu2"]:GetInt(),
            DLVM.Binds["Jutsu3"]:GetInt(),
            DLVM.Binds["Jutsu4"]:GetInt(),
            DLVM.Binds["Jutsu5"]:GetInt(),
            DLVM.Binds["Jutsu6"]:GetInt()
        } 
        
        panel.jutsu_actual = jutsu_old
    end)
    
    local search
    
    local dtextentry = vgui.Create("DTextEntry", panel)
    dtextentry:SetSize(SW(300), SH(40))
    dtextentry:SetPos(SW(820), SH(52))
    --  dtextentry:SetBackgroundColor(Color(23, 23, 23))
    dtextentry:SetPaintBackground(false)
    dtextentry:SetDrawLanguageID(false)
    dtextentry:SetFont(LVM_V2.Fonts("LVM", 8, 500))
    dtextentry:SetPlaceholderColor(Color(200, 200, 200, 50))
    dtextentry:SetPlaceholderText("Rechercher...")
    dtextentry:SetTextColor(color_white)
    dtextentry:SetCursorColor(Color(200, 200, 200, 200))
    dtextentry.OnTextChanged = function(self)
        search(self:GetValue())
    end
    
    for i = 1, 3 do
        local dbutton = vgui.Create("DButton", panel)
        dbutton:SetSize(SW(30), SH(30))
        dbutton:SetPos(SW(70), SH(640 + (i-1)*35))
        dbutton:SetText("")
        dbutton.Paint = function(s, w, h)
            draw.SimpleText(i, LVM_V2.Fonts("LVM", 7, 500), w/2, h/2, (DLVM.Binds["Tree"]:GetInt() == i and color_white or Color(200, 200, 200, 200)), 1, 1)
        end
        dbutton.DoClick = function()
            DLVM.Binds["Tree"]:SetInt(i)
            DLVM:RetrieveBinds(LocalPlayer():GetNWInt("CharacterID", 1), i)
            
            panel.jutsu_actual = {
                DLVM.Binds["Jutsu1"]:GetInt(),
                DLVM.Binds["Jutsu2"]:GetInt(),
                DLVM.Binds["Jutsu3"]:GetInt(),
                DLVM.Binds["Jutsu4"]:GetInt(),
                DLVM.Binds["Jutsu5"]:GetInt(),
                DLVM.Binds["Jutsu6"]:GetInt()
            }
        end
     end
    
    local scrollpanel = vgui.Create("DScrollPanel", panel)
    scrollpanel:SetSize(SW(1010), SH(491))
    scrollpanel:SetPos(SW(130), SH(110))
    
    local vbar = scrollpanel:GetVBar()
    vbar:SetWide(SW(4))
    function vbar.btnUp:Paint(w, h)
    end
    
    function vbar.btnDown:Paint(w, h)
    end
    
    function vbar.btnGrip:Paint(w, h)
        draw.RoundedBox(8, 0, 0, w, h, Color(100, 100, 100, 200))
    end
    
    function vbar:Paint(w, h)
        draw.RoundedBox(8, 0, 0, w, h, Color(0, 0, 0, 200))
    end
    
    local classe = LocalPlayer():GetClasse()
    
    local tooltip;
    local selected_id = 0;
    local function ShowTooltip(bool, name, rang)
        
        if not bool then panel.PaintOver = nil; return end
        
        surface.SetFont(LVM_V2.Fonts("LVM", 9, 500))
        local w, h = surface.GetTextSize(name)
        
        panel.PaintOver = function(self)
            local x, y = panel:LocalCursorPos()
            x = x + SW(10)
        
            draw.RoundedBox(20, x, y, w + SW(20), SH(60), Color(50, 50, 50))
            draw.SimpleText(name, LVM_V2.Fonts("LVM", 9, 500), x + SW(8), y + SH(5), color_white, TEXT_ALIGN_LEFT)
            draw.SimpleText("Rareté:", LVM_V2.Fonts("LVM", 7, 500), x + SW(8), y + SH(30), Color(200, 200, 200), TEXT_ALIGN_LEFT)
            draw.SimpleText(rang, LVM_V2.Fonts("LVM", 7, 500), x + SW(60), y + SH(30), DLVM.RarityColors[rang], TEXT_ALIGN_LEFT)
        end
        
    end
    
    local richtext = vgui.Create("RichText", panel)
    richtext:SetSize(SW(270), SH(110))
    richtext:SetPos(SW(1180), SH(340))
    function richtext:PerformLayout()
        self:SetFontInternal(LVM_V2.Fonts("LVM", 5, 500))
    end
    richtext:InsertColorChange( 255, 255, 255, 255 )
    
    local function SetDescription(text, level)
        richtext:SetText("")

        richtext:InsertColorChange(255, 255, 255, 255)
        richtext:AppendText(text.."\n")

        richtext:InsertColorChange(255, 255, 0, 255)
        richtext:AppendText("Niveau : " .. level .. "\n")
        
        local id = selected_id;

        
        LVM_V2:GetConfigJutsu(id, function(config)
            if id != selected_id then print("id not valid") return end
            if not IsValid(richtext) then print("richtext not valid") return end
            local jutsuConfig = LVM_V2:GetConfigJutsuLevel(config, level)
            
            for key, value in pairs(jutsuConfig) do
                local color = LVM_V2.Equilibrage.Config["Color"][key] or color_white
                local text = (LVM_V2.Equilibrage.Config["Key"][key] or key)
    
                richtext:InsertColorChange(color.r, color.g, color.b, 255)
                richtext:AppendText(text.." : ")
    
                richtext:AppendText(value)
                
                richtext:AppendText("\n")
            end
        end)
 
    end
    
    local natures = DLVM.Classe
    
    function search(searched)
        scrollpanel:Clear()
        local function AddNature(name)
            if name == "none" then return end
            
            local is_posses = (LocalPlayer():IsDev() and true or (convertclass[classe] == name))
        
            local menu = vgui.Create("DPanel", scrollpanel)
            menu:SetSize(SW(1010), SH(150))
            menu:DockMargin(0, 0, 0, SH(10))
            menu:Dock(TOP)
            menu.Paint = function(self, w, h)
                if is_posses then
                    surface.SetMaterial(LVM_V2:Material("F4/bar.png"))
                    surface.SetDrawColor(color_white)
                    surface.DrawTexturedRect(0, 0, w, h)
                    
                    draw.SimpleText(name, LVM_V2.Fonts("LVM", 6, 500), w/2, SH(10), color_white, TEXT_ALIGN_CENTER)
                else
                    surface.SetMaterial(LVM_V2:Material("F4/bar.png"))
                    surface.SetDrawColor(color_white)
                    surface.DrawTexturedRect(0, 0, w, h)
                    
                    draw.SimpleText(name, LVM_V2.Fonts("LVM", 6, 500), w/2, SH(10), Color(255, 255, 255, 100), TEXT_ALIGN_CENTER)
                    
                    surface.SetMaterial(LVM_V2:Material("F4/bar_locked.png"))
                    surface.SetDrawColor(color_white)
                    surface.DrawTexturedRect(0, 0, w, h)
                end
            end
            
            if is_posses then
                
                local nature_tech = {}
                for i = 1, #DLVM.Technics do
                    if DLVM.Technics[i].Nature == name and DLVM.Technics[i].Name:lower():match(searched:lower()) == searched:lower() then
                        local index = #nature_tech+1
                        nature_tech[index] = DLVM.Technics[i]
                        nature_tech[index].TechID = i;
                    end
                end
                
                for i = 1, #nature_tech do
                    local dbutton = vgui.Create("DButton", menu)
                    dbutton:SetSize(SW(75), SH(75))
                    dbutton:SetPos(SW(23) + SW(99*(i-1)), SH(40))
                    dbutton:SetText("")
                    dbutton.TechID = nature_tech[i].TechID
                    dbutton.Paint = function(s, w, h)
                        surface.SetMaterial(LVM_V2:Material("f4/jutsu_slot.png"))
                        surface.SetDrawColor(color_white)
                        surface.DrawTexturedRect(0, 0, w, h)
                        
                        if nature_tech[i].Icon != "" then
                            masks.Start()
                                surface.SetMaterial(util.ImgurToMaterial(nature_tech[i].Icon, "smooth mips"))
                                surface.SetDrawColor((LocalPlayer():HasJutsu (nature_tech[i].TechID) and color_white or Color(100,100,100)))
                                surface.DrawTexturedRect(0, 0, w, h)
                            masks.Source()
                                DrawCircle(SW(38), SH(37), SH(32), 360, nil)
                            masks.End()
                        end
                    end
                    dbutton.DoClick = function()
                        panel.TechClicked = nature_tech[i].TechID
                        local techid = nature_tech[i].TechID
         
                        local jutsulevel = (LocalPlayer().LVM_Jutsu == nil and 0 or (LocalPlayer().LVM_Jutsu[techid] or 0))
                        
                        selected_id = techid;

                        SetDescription(nature_tech[i].Description, jutsulevel)

                        if input.IsKeyDown(KEY_LSHIFT) then 
                            techid = nature_tech[i].TechID
                            if techid and LocalPlayer():HasJutsu(techid) then
                                for i = 1, 6 do
                                    if panel.jutsu_actual[i] == 0 then 
                                        panel.jutsu_actual[i] = techid
                                        DLVM.Binds["Jutsu" .. i]:SetInt(techid)
                                        return
                                    end
                                end
                            end
                        end
                    end
                    if LocalPlayer():HasJutsu(nature_tech[i].TechID) then
                        dbutton:Droppable("abre_jutsu")
                    end
                    dbutton.OnCursorEntered = function()
                        ShowTooltip(true, nature_tech[i].Name, nature_tech[i].Rang)
                    end
                    dbutton.OnCursorExited = function()
                        ShowTooltip(false)
                    end

                    dbutton.DoRightClick = function(self)
                        if input.IsKeyDown(KEY_LSHIFT) then 
                            local techID = self.TechID
                            if techID and LocalPlayer():HasJutsu(techID) then
                                for i = 1, 6 do
                                    if panel.jutsu_actual[i] == 0 then 
                                        panel.jutsu_actual[i] = techID
                                        DLVM.Binds["Jutsu" .. i]:SetInt(techID)
                                        return
                                    end
                                end
                            end
                        end
                    end
                end
                
            end
        end
        
        local function AddNatures(tbl)
            local tbl2 = {}
            for i = 1, #tbl do
                if tbl2[tbl[i]] == nil then
                    AddNature(tbl[i])
                    tbl2[tbl[i]] = true
                end
            end
        end
        
        AddNatures(natures)
    end
    
    search("")
    
    for i = 1, 6 do
    
        local button = vgui.Create("DButton", panel)
        button:SetSize(SW(90), SH(80))
        button:SetPos(SW(180) + SW(170 * (i-1)), SH(640))
        button:SetText("")
        button.Paint = nil;
        button.Tech = panel.jutsu_actual[i]
        button.OnCursorEntered = function(self)
            if DLVM.Technics[self.Tech] == nil then return end
            ShowTooltip(true, DLVM.Technics[self.Tech].Name, DLVM.Technics[self.Tech].Rang)
        end
        button.DoRightClick = function(self)
            panel.jutsu_actual[i] = 0;
            self.Tech = 0
            ShowTooltip(false)
        end
        button.OnCursorExited = function()
            ShowTooltip(false)
        end
        button:Receiver("abre_jutsu", function(self, tbl, dropped, menu, x, y)
            if not dropped then return end
            
            local jutsu = tbl[1]
            if jutsu == nil then return end
            
            if !LocalPlayer():HasJutsu(jutsu.TechID) then return end
            
            panel.jutsu_actual[i] = jutsu.TechID
            self.Tech = jutsu.TechID
        end)
        
        local binder = vgui.Create("DBinder", panel)
        binder:SetSize(SW(25), SH(25))
        binder:SetPos(SW(230) + SW(170 * (i-1)), SH(700))
        binder:SetConVar("kjutsu_"..i)
        binder:SetFontInternal(LVM_V2.Fonts("LVM", 5, 500))
        binder:SetTextColor(color_white)
        binder.Paint = function(self, w, h)
            -- local key = input.GetKeyName(tonumber(self:GetValue()))
            draw.RoundedBox(8, 0, 0, w, h, Color(71, 71, 71))
            -- draw.SimpleText(key, LVM_V2.Fonts("LVM", 5, 500), w/2, h/2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
        end
        
    end
    
    local button = vgui.Create("DPanel", panel)
    button:SetSize(SW(296), SH(70))
    button:SetPos(SW(1175), SH(625))
    -- button:SetConVar("kpermutation")
    button:SetText("")
    button.Paint = function(self, w, h)
        draw.RoundedBox(0, 0, 0, w, h, Color(29, 29, 29))
        
        surface.SetMaterial(util.ImgurToMaterial("ReyiIP4.png", "smooth"))
        surface.SetDrawColor(color_white)
        surface.DrawTexturedRect(SW(30), SH(15), SW(40), SH(40))
    
        draw.SimpleText("Permutation: ", LVM_V2.Fonts("LVM", 7, 500), SW(130), h / 2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
    
    local dbinder = vgui.Create("DBinder", button)
    dbinder:SetSize(SW(80), SH(40))
    dbinder:SetPos(SW(180), SH(15))
    dbinder:SetConVar("kpermutation")
    dbinder:SetFontInternal(LVM_V2.Fonts("LVM", 7, 500))
    dbinder:SetTextColor(color_white)
    dbinder.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(71, 71, 71, 200))
    end

    local button2 = vgui.Create("DPanel", panel)
    button2:SetSize(SW(296), SH(70))
    button2:SetPos(SW(1175), SH(545))
    button2:SetText("")
    button2.Paint = function(self, w, h)
       draw.RoundedBox(6, 0, 0, w, h, Color(29, 29, 29))
       
       draw.SimpleText("Style de combat: ", LVM_V2.Fonts("LVM", 7, 500), SW(100), h / 2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
       draw.SimpleText("*Le style de combat n'affecte que les animations*", LVM_V2.Fonts("LVM", 4, 500), SW(40), h / 2 + SH(25), Color(200, 200, 200, 30), TEXT_ALIGN_LEFT, TEXT_ALIGN_CENTER)
    end
    
    local dstyle = vgui.Create("DButton", button2)
    dstyle:SetSize(SW(80), SH(40))
    dstyle:SetPos(SW(180), SH(15))
    dstyle:SetText("")
    dstyle.Paint = function(self, w, h)
        draw.RoundedBox(4, 0, 0, w, h, Color(71, 71, 71, 200))
        draw.SimpleText(DLVM.Binds.FightStyle:GetInt() == 1 and "Bandit" or (DLVM.Binds.FightStyle:GetInt() == 2 and "Conventionnel" or "Technique"), LVM_V2.Fonts("LVM", 5, 500), w/2, h/2, color_white, TEXT_ALIGN_CENTER, TEXT_ALIGN_CENTER)
    end
    dstyle.DoClick = function()
       local menu = DermaMenu(false, button2)
       menu:SetPos(SW(1175), SH(545))
       menu:SetSize(SW(296), SH(70))
       
       local dbutton = menu:AddOption("Bandit", function()
           DLVM.Binds.FightStyle:SetInt(1)
       end):SetIcon("icon16/arrow_right.png")
       local dbutton = menu:AddOption("Conventionnel", function()
           DLVM.Binds.FightStyle:SetInt(2)
       end):SetIcon("icon16/arrow_right.png")
       local dbutton = menu:AddOption("Technique", function()
           DLVM.Binds.FightStyle:SetInt(3)
       end):SetIcon("icon16/arrow_right.png")

       
       menu:Open()
    end
    local function Save()
        for i = 1, 6 do
            DLVM.Binds["Jutsu" .. i]:SetInt(panel.jutsu_actual[i])
            jutsu_old[i] = panel.jutsu_actual[i]
        end

        DLVM:SaveBinds()
    end
    
    button2.OnRemove = Save
end